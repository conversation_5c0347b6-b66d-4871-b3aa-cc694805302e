﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Core.Shared;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Sinks.Elasticsearch;
using System;
using System.Configuration;
using System.IO;
using System.Reflection;
using System.Text;

namespace Core.API
{
    public class Program
    {
        public static IConfiguration Configuration { get; private set; }

        //[Obsolete]
        public static void Main(string[] args)
        {
            //Fix lỗi: .NET6 and DateTime problem. Cannot write DateTime with Kind=UTC to PostgreSQL type 'timestamp without time zone'
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);

            var host = CreateHostBuilder(args).Build();
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            // Build Configuration
            Configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json", true,
                    true)
                .AddCommandLine(args)
                .AddEnvironmentVariables()
                .Build();

            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            #region Config Serilog
            // Configure serilog
            var logger = new LoggerConfiguration()
                    .ReadFrom.Configuration(Configuration)
                    .Enrich.FromLogContext()
                    .Enrich.WithExceptionDetails()
                    .Enrich.WithMachineName();

            var elaticUri = Utils.GetConfig("ElasticConfiguration:Uri");
            if (!string.IsNullOrEmpty(elaticUri))
            {
                logger.WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(elaticUri))
                {
                    AutoRegisterTemplate = true,
                    IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}"
                });
            }

            var dsn = Utils.GetConfig("Sentry:dsn");
            if (!string.IsNullOrEmpty(dsn))
                logger.WriteTo.Sentry(o =>
                {
                    // Debug and higher are stored as breadcrumbs (default is Information)
                    o.MinimumBreadcrumbLevel = LogEventLevel.Debug;
                    // Warning and higher is sent as event (default is Error)
                    o.MinimumEventLevel = LogEventLevel.Warning;
                    o.Dsn = dsn;
                    o.Release = Utils.GetConfig("Sentry:release");
                    o.Environment = Utils.GetConfig("Sentry:environment");
                    o.AttachStacktrace = true;
                    o.SendDefaultPii = true; // send PII like the username of the user logged in to the device
                    o.TracesSampleRate = Convert.ToDouble(Utils.GetConfig("Sentry:TracesSampleRate"));
                });
            Log.Logger = logger.CreateLogger();
            #endregion

            Log.Information($"API started (with evirontment - {environment}) at: " + DateTime.Now.ToString());
            // Log.Error("Demo Error Log - " + Guid.NewGuid().ToString());
            host.Run();
        }
       

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.UseSentry();
                })
                .UseSerilog();
    }
}
