﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/gioi-tinh")]
    [ApiExplorerSettings(GroupName = "17. Giới tính")]
    [Authorize]
    public class GioiTinhController : ApiControllerBase
    {
        public GioiTinhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách giới tính cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<GioiTinhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxGioiTinhQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách giới tính có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<GioiTinhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] GioiTinhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterGioiTinhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết giới tính
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<GioiTinhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetGioiTinhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới giới tính
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateGioiTinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIOI_TINH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIOI_TINH_CREATE;


                return await _mediator.Send(new CreateGioiTinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel giới tính
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyGioiTinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIOI_TINH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIOI_TINH_CREATE_MANY;


                return await _mediator.Send(new CreateManyGioiTinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa giới tính
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateGioiTinhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIOI_TINH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIOI_TINH_UPDATE;
                return await _mediator.Send(new UpdateGioiTinhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa giới tính
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIOI_TINH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIOI_TINH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIOI_TINH_DELETE;

                return await _mediator.Send(new DeleteGioiTinhCommand(id, u.SystemLog));
            });
        }

    }
}
