﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/thanh-phan-mon-theo-he")]
    [ApiExplorerSettings(GroupName = "60. Thành phần môn theo hệ")]
    [Authorize]
    public class ThanhPhanMonTheoHeController : ApiControllerBase
    {
        public ThanhPhanMonTheoHeController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách thành phần môn theo hệ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ThanhPhanMonTheoHeSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxThanhPhanMonTheoHeQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách thành phần môn theo hệ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThanhPhanMonTheoHeBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THANH_PHAN_MON_THEO_HE_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ThanhPhanMonTheoHeFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterThanhPhanMonTheoHeQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết thành phần môn theo hệ
        /// </summary>
        /// <param name="idThanhPhan"></param>
        /// <param name="idHe"></param>
        /// <returns></returns>
        [HttpGet, Route("{idThanhPhan}-{idHe}")]
        [ProducesResponseType(typeof(ResponseObject<ThanhPhanMonTheoHeModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THANH_PHAN_MON_THEO_HE_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int idThanhPhan, int idHe)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetThanhPhanMonTheoHeByIdQuery(idThanhPhan, idHe)));
        }

        /// <summary>
        /// Thêm mới thành phần môn theo hệ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THANH_PHAN_MON_THEO_HE_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateThanhPhanMonTheoHeModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_CREATE;


                return await _mediator.Send(new CreateThanhPhanMonTheoHeCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa thành phần môn theo hệ
        /// </summary>
        /// <param name="idThanhPhan"></param>
        /// <param name="idHe"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{idThanhPhan}-{idHe}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THANH_PHAN_MON_THEO_HE_EDIT))]
        public async Task<IActionResult> Update(int idThanhPhan, int idHe, [FromBody] UpdateThanhPhanMonTheoHeModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_UPDATE;
                return await _mediator.Send(new UpdateThanhPhanMonTheoHeCommand(idThanhPhan, idHe, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa thành phần môn theo hệ
        /// </summary>
        /// <param name="idThanhPhan"></param>
        /// <param name="idHe"></param>
        /// <returns></returns>
        [HttpDelete, Route("{idThanhPhan}-{idHe}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THANH_PHAN_MON_THEO_HE_DELETE))]
        public async Task<IActionResult> Delete(int idThanhPhan, int idHe)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_THANH_PHAN_MON_THEO_HE_DELETE;

                return await _mediator.Send(new DeleteThanhPhanMonTheoHeCommand(idThanhPhan,idHe, u.SystemLog));
            });
        }

    }
}
