﻿using System;
using System.Collections.Generic;

namespace Core.Business
{
    public class GetAllStatesNameResponseModel
    {
        public List<string> StateNames { get; set; }
    }

    public class HistoryApprovalModel
    {
        public Guid Id { get; set; }
        public Guid ProcessId { get; set; }
        public string IdentityId { get; set; }
        public List<string> AllowedTo { get; set; }
        public DateTime? TransitionTime { get; set; }
        public long Sort { get; set; }
        public string InitialState { get; set; }
        public string DestinationState { get; set; }
        public string TriggerName { get; set; }
        public string Commentary { get; set; }
        public string UserName { get; set; }
    }

    public class GetInfoWorkFlowModel
    {
        public Guid ProcessId { get; set; }
        public string State { get; set; }
        public List<ProcessWorkFlowCommandModel> CommandAvailables { get; set; }
        public string AuthorId { get; set; }
    }

    public class MapWorkFlowNameFunctionModel
    {
        public int IdPh { get; set; }
        public string WorkFlowName { get; set; }
        public string IdThamSo { get; set; }
        public bool Active { get; set; }
    }

    public class FilterMapWorkFlowNameFunctionModel : BaseQueryFilterModel
    {
        public int? IdPh { get; set; }
    }

    public class GetMapWorkFlowNameFunctionModel
    {
        public int IdPh { get; set; }
        public string PhanHe {  get; set; }
        public DateTime? DateModify { get; set; }
        public string IdThamSo { get; set; }
        public string GiaTri { get; set; }
        public string TenThamSo { get; set; }
        public string GhiChu { get; set; }
        public string UserName { get; set; }
        public bool Active { get; set; }
    } 
}
