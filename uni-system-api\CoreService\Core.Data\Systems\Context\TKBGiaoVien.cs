﻿using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("tkbGiaoVien")]
    public class TKBGiaoVien
    {
        public TKBGiaoVien()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_cb")]
        public int Id { get; set; }

        [Column("Ma_cb")]
        public string MaCB { get; set; }

        [Column("Ho_ten")]
        public string HoTen { get; set; }

        [Column("Ten")]
        public string Ten { get; set; }

        [Column("ID_gioi_tinh")]
        public int? GioiTinhID { get; set; }

        [Column("Ngay_sinh")]
        public DateTime? NgaySinh { get; set; }

        [Column("ID_khoa")]
        public int KhoaID { get; set; }

        [Column("ID_hoc_ham")]
        public int HocHamID { get; set; }

        [Column("ID_hoc_vi")]
        public int HocViID { get; set; }

        [Column("ID_chuc_danh")]
        public int ChucDanhID { get; set; }

        [Column("ID_chuc_vu")]
        public int ChucVuID { get; set; }

        [Column("Thinh_giang")]
        public bool ThinhGiang { get; set; }

        [Column("Ten_dang_nhap")]
        public string TenDangNhap { get; set; }

        [Column("Mat_khau")]
        public string MatKhau { get; set; }

        [Column("Not_active")]
        public bool? KhongHoatDong { get; set; }

        [Column("He_so_luong")]
        public float HeSoLuong { get; set; }

        [Column("So_dien_thoai")]
        public string SoDienThoai { get; set; }

        [Column("Email")]
        public string Email { get; set; }

        [Column("Dia_chi_lien_he")]
        public string DiaChiLienHe { get; set; }

        [Column("Co_huu")]
        public bool? CoHuu { get; set; }

        [Column("Kiem_giang")]
        public bool? KiemGiang { get; set; }

        [Column("CMTND")]
        public string CMTND { get; set; }

        [Column("Ngay_cap_CMND")]
        public DateTime? NgayCapCMND { get; set; }

        [Column("Noi_cap_CMND")]
        public string NoiCapCMND { get; set; }

        [Column("Ten_ngan_hang")]
        public string TenNganHang { get; set; }

        [Column("Chuyen_mon_dao_tao")]
        public string ChuyenMonDaoTao { get; set; }

        [Column("Tham_nien_giang_day")]
        public string ThamNienGiangDay { get; set; }

        [Column("So_tai_khoan")]
        public string SoTaiKhoan { get; set; }

        [Column("Chu_tai_khoan")]
        public string ChuTaiKhoan { get; set; }

        [Column("Ma_so_thue")]
        public string MaSoThue { get; set; }

        [Column("Chi_nhanh_ngan_hang")]
        public string ChiNhanhNganHang { get; set; }

        [Column("Ten_tinh_ngan_hang")]
        public string TenTinhNganHang { get; set; }

        [Column("Da_Nop_HD")]
        public bool? DaNopHD { get; set; }

        [Column("ID_bm_chinh")]
        public int? BmChinhID { get; set; }

        [Column("ID_don_vi_quan_ly")]
        public int? DonViQuanLyID { get; set; }

        [Column("ID_ton_giao")]
        public int TonGiaoID { get; set; }

        [Column("ID_dan_toc")]
        public int DanTocID { get; set; }

        [Column("ID_quoc_tich")]
        public int QuocTichID { get; set; }

        [Column("Chuyen_nganh_giang_day")]
        public string ChuyenNganhGiangDay { get; set; }

        [Column("Phan_loai")]
        public string PhanLoai { get; set; }

        [Column("So_so_bao_hiem")]
        public string SoSoBaoHiem { get; set; }

        [Column("ID_don_vi_cong_tac")]
        public int DonViCongTacID { get; set; }

        [Column("Don_vi_cong_tac")]
        public string DonViCongTac { get; set; }
    }
}
