{
  "AppSettings": {
    "EnableSwagger": "true",
    "Tittle": "UniLeader UniCore Service",
    "Description": "Net Core Framework",
    "TermsOfService": "",
    "Contact": {
      "Name": "HaiND",
      "Email": "<EMAIL>",
      "Url": "https://github.com/duchaindh94"
    },
    "License": {
      "Name": "The MIT License (MIT).",
      "Url": "https://github.com/dotnet/core"
    },
    "CorsOrigins": "http://localhost:5001",
    "EnableCache": "true", // Có sử dụng cache hay không
    "ShowRequestDuration": "true", // <PERSON><PERSON><PERSON> thị thời gian xử lý request
    "ReturnDetailError500Message": "true", // true - Hi<PERSON>n thị chi tiết lỗi 500 dùng cho test, false - dùng cho môi trường prod để hạn chế dữ liệu bảo mật
    "IPHeader": "X-Forwarded-For" // Header chứa IP client" - "RemoteIpAddress", "X-Forwarded-For", "X-Real-Ip"
  },
  "Database": {
    "Workflow": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;TrustServerCertificate=True;"
      }
    },
    "Leader": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_LEADER_CORE;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;",
        "MSSQLDatabaseRead": "server=**************;database=UNISOFT_LEADER_CORE;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;"
      }
    },
    "System": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_TEST;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;",
        "MSSQLDatabaseRead": "server=**************;database=UNISOFT_FULL6_TEST;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;"
      }
    }
  },
  "HRMClient": {
    "Hou": {
      "ApiUrl": "https://api.hou.edu.vn",
      "ClientId": "client-id",
      "ClientSecret": "client-secret"
    }
  },
  "minio": {
    "enabled": "true",
    "endpoint": "************:9010",
    "accesskey": "miniouploaduser",
    "secretKey": "ayrlcZz07JQQYSKMKEIDXJRu8uc",
    "defaultBucketName": "unisoft-files",
    "enableSsl": "false"
  },
  "redis": {
    "enabled": "true",
    "configuration": "************:6379,password=thienan123,defaultDatabase=0",
    "instanceName": "UniSoft:",
    "timeLive": "30000"
  },
  "EmailSettings": {
    "DefaultFromEmail": "<EMAIL>",
    "DefaultFromName": "UniSoft",
    "Host": "smtp.gmail.com",
    "Port": 587,
    "UserName": "<EMAIL>",
    "Password": "",
    "SSL": "1"
  },
  "Authentication": {
    "HOU": {
      "CasServerUrl": "https://cas.hou.edu.vn"
    },
    "Jwt": {
      "Enable": "false",
      "Key": "NETCORE_SECRET_KEY",
      "Issuer": "NETCORE-ORION-CORP",
      "TimeToLive": "3600"
    },
    "IdentityServer": {
      "Enable": "true",
      "Uri": "https://moodle.unisoft.edu.vn",
      "ClientId": "uni-hrm-portal-client",
      "Secret": "pt0bM7sY!9*cpT7s$MjGB4s"
    },
    "AdminUserName": "admin"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Core.API.Shared.CustomAuthHandler": "Error"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File"
    ],
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Core.API.Shared.CustomAuthHandler": "Error",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "File",
              "Args": {
                "path": "/opt/logs_folder/leader/log-.txt",
                "rollingInterval": "Day",
                "fileSizeLimitBytes": 10485760,
                "retainedFileCountLimit": 100,
                "rollOnFileSizeLimit": true,
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "Console",
              "Args": {
                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "GrafanaLoki",
              "Args": {
                "uri": "http://localhost:3100",
                "credentials": {
                  "login": "grafanalokiuser",
                  "password": "grafanalokipass"
                },
                "labels": [
                  {
                    "key": "app",
                    "value": "leader-api-test"
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  }
}