﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared.EmailTemplate
{
    public enum EmailTemplateEnum
    {
        SENDMAIL_FORGOT_PASS,
    }

    public class EmailTemplateSeedModel
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Subject { get; set; }
        public string Template { get; set; }
    }

    public class EmailTemplateSeedConstant
    {
        public static List<EmailTemplateSeedModel> ListSeedEmails = new List<EmailTemplateSeedModel>
        {
            new EmailTemplateSeedModel
            {
                Code = EmailTemplateEnum.SENDMAIL_FORGOT_PASS.ToString(),
                Name = "Email thông báo quên mật khẩu",
                Subject = "[UniSoft] Cập nhật mật khẩu",
                Template = "<p><PERSON><PERSON><PERSON> gửi {{ FullName }},</p><p><PERSON>ạ<PERSON> có yêu cầu đặt lại mật khẩu. " +
                "Nếu bạn là người thực hiện vui lòng click vào đường dẫn sau để thực hiện cập nhật mật khẩu mới. " +
                "Nếu không vui lòng liên hệ quản trị hệ thống để cập nhật bảo mật cho tài khoản.</p>" +
                "<p><a href=\"http://admin.unisoft.edu.vn/passport/recover-password?token={{ Token }}&amp;secretKey={{ SecretKey }}&amp;returnUrl= {{ ReturnUrl }}\"><strong>Cập nhật mật khẩu</strong></a></p>" +
                "<p>Thời gian hiệu lực là 1h.</p>"
            }
        };
    }
}
