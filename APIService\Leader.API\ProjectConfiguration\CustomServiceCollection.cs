using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Leader.API
{
    public static class CustomServiceCollection
    {
        /// <summary>
        /// RegisterCustomService
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCustomServiceComponents(this IServiceCollection services, IConfiguration configuration)
        {
            Core.Business.ServiceConfigure.Configure(services);
            Leader.Business.ServiceConfigure.Configure(services, configuration);

            return services;
        }
    }
}