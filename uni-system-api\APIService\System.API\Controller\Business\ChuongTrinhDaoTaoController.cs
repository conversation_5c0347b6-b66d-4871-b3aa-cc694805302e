﻿using Core.API.Shared;
using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;


namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chuong-trinh-dao-tao")]
    [ApiExplorerSettings(GroupName = "63. Chương trình đào tạo")]
    [Authorize]
    public class ChuongTrinhDaoTaoController : ApiControllerBase
    {
        public ChuongTrinhDaoTaoController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }


        /// <summary>
        /// Lấy danh sách ctdt cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<SoCtdtSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxSoCtdtQuery(count, ts));
            });
        }

        /// <summary>
        /// Get chương trình đào tạo 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuongTrinhDaoTaoModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> Filter()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChuongTrinhDaoTaoQuery(_.SystemLog)));
        }


        /// <summary>
        /// Get chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="idDt"></param>
        /// <returns></returns>
        [HttpGet, Route("chi-tiet-mon/{idDt}")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuongTrinhDaoTaoChiTietModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_CHI_TIET_VIEW))]
        public async Task<IActionResult> GetDaoTaoChiTietMon(int idDt = 0)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChuongTrinhDaoTaoChiTietQuery(idDt)));
        }

        /// <summary>
        /// Thêm chương trình đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateChuongTrinhDaoTao([FromBody] CreateChuongTrinhDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new CreateChuongTrinhDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Cập nhật chương trình đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_EDIT))]
        public async Task<IActionResult> UpdateChuongTrinhDaoTao([FromBody] List<UpdateChuongTrinhDaoTaoModel> model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE;


                return await _mediator.Send(new UpdateChuongTrinhDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chương trình đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_DELETE))]
        public async Task<IActionResult> DeleteChuongTrinhDaoTao(int id = 0)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_DELETE;


                return await _mediator.Send(new DeleteChuongTrinhDaoTaoCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Thêm chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("them-chuong-dao-tao-chi-tiet")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_CHI_TIET_ADD))]
        public async Task<IActionResult> CreateChuongTrinhDaoTaoChiTiet([FromBody] List<CreateChuongTrinhDaoTaoChiTietModel> model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_CREATE;


                return await _mediator.Send(new CreateChuongTrinhDaoTaoChiTietCommand(model, u.SystemLog, u.UserName));
            });
        }

        /// <summary>
        /// Cập nhật chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("update-chuong-trinh-dao-tao-chi-tiet")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_EDIT))]
        public async Task<IActionResult> UpdateChuongTrinhDaoTaoChiTiet([FromBody] List<UpdateChuongTrinhDaoTaoChiTietModel> model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE;


                return await _mediator.Send(new UpdateChuongTrinhDaoTaoChiTietCommand(model, u.SystemLog, u.UserName));
            });
        }

        /// <summary>
        /// Xóa học phần
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("xoa-chuong-dao-tao-chi-tiet")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_CHI_TIET_DELETE))]
        public async Task<IActionResult> DeleteChuongTrinhDaoTaoChiTiet(List<DeleteChuongTrinhDaoTaoChiTiet> model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_DELETE;


                return await _mediator.Send(new DeleteChuongTrinhDaoTaoChiTietCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Danh sách học phần và hiển thị công thức điểm 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("danh-sach-hoc-phan")]
        [ProducesResponseType(typeof(ResponseObject<List<CongThucTinhDiemHocPhanModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> CongThucDiemHocPhan([FromBody] CongThucDiemHocPhanBaseModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new GetCongThucTinhDiemHocPhanQuery(model, u.SystemLog));
            });
        }



        /// <summary>
        /// Thêm và xóa công thức vào học phần
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("add-cong-thuc-diem-vao")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_EDIT))]
        public async Task<IActionResult> AddDiemCongThuc([FromBody] AddDiemCtChuongTrinhDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE;


                return await _mediator.Send(new AddDiemCtChuongTrinhDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// sao chép chương trình đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("sao-chep-ctdt")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> CopyChuongTrinhDaoTao([FromBody] SaoChepChuongTrinhDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new SaoChepChuongTrinhDaoTaoCommand(model, u.SystemLog, u.UserName));
            });
        }

        /// <summary>
        /// Thay thế học phần trong bảng điểm
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("thay-the-hoc-phan")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> ThayTheHocPhan([FromBody] ThayTheHocPhanBangDiemModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new ThayTheHocPhanBangDiemCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Get Học phần có hiển thị tín chỉ 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("hoc-phan-theo-tin-chi")]
        [ProducesResponseType(typeof(ResponseObject<List<HocPhanTheoTinChiModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetHocPhanTheoTinChi()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocPhanTheoTinChiQuery(_.SystemLog)));
        }

        /// <summary>S
        /// Get Học phần tương đương
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("hoc-phan-tuong-duong")]
        [ProducesResponseType(typeof(ResponseObject<List<HocPhanTuongDuongModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetHocPhanTuongDuong()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocPhanTuongDuongQuery(_.SystemLog)));
        }


        /// <summary>
        /// Thiết lập môn học tương đương
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("thiet-lap-mon-hoc-tuong-duong")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> ThietLapMonHocTuongDuong([FromBody] ThietLapHocPhanTuongDuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new ThietLapHocPhanTuongDuongCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Xóa môn học tương đương
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("mon-hoc-tuong-duong/{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_DELETE))]
        public async Task<IActionResult> DeleteMonHocTuongDuong(int id = 0)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_DELETE;


                return await _mediator.Send(new DeleteHocPhanTuongDuongCommand(id, u.SystemLog));
            });
        }


        /// <summary>
        /// Gán chương trình đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("gan-chuong-trinh-dao-tao")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_EDIT))]
        public async Task<IActionResult> GanChuongTrinhDaoTao([FromBody] List<GanChuongTrinhDaoTaoModel> model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_UPDATE;


                return await _mediator.Send(new GanChuongTrinhDaoTaoCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Thêm ràng buộc học phần
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("them-rang-buoc-hoc-phan")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_RANG_BUOC_ADD))]
        public async Task<IActionResult> RangBuocHocPhan([FromBody] ThemRangBuocHocPhanModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new ThemRangBuocHocPhanCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Xóa ràng buộc học phần
        /// </summary>
        /// <param name="idRb"></param>
        /// <returns></returns>
        [HttpDelete, Route("delete-rang-buoc-hoc-phan/{idRb}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_RANG_BUOC_DELETE))]
        public async Task<IActionResult> DeleteRangBuocHocPhan(int idRb = 0)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_DELETE;


                return await _mediator.Send(new DeleteRangBuocHocPhanCommand(idRb, u.SystemLog));
            });
        }

        /// <summary>S
        /// Danh sách học phần ràng buộc
        /// </summary>
        /// <param name="idDt"></param>
        /// <returns></returns>
        [HttpGet, Route("hoc-phan-rang-buoc/{idDt}")]
        [ProducesResponseType(typeof(ResponseObject<List<HocPhanTuongDuongModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_RANG_BUOC_VIEW))]
        public async Task<IActionResult> GetHocPhanRangBuoc(int idDt)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetRangBuocHocPhanQuery(idDt, _.SystemLog)));
        }


        /// <summary>
        /// Get Học phần tương đương
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("danh-sach-mon-chung-chi")]
        [ProducesResponseType(typeof(ResponseObject<List<HocPhanTuongDuongModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetMonChungChi(MonHocTheoHeFilterModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetMonChungChiQuery(model, _.SystemLog)));
        }

        /// <summary>
        /// Thêm Môn chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("them-mon-chung-chi")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateMonChungChi([FromBody] CreateManyMonChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new CreateMonChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Môn chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("xoa-mon-chung-chi")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> DeleteMonChungChi([FromBody] CreateManyMonChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_CREATE;


                return await _mediator.Send(new DeleteMonChungChiCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Get môn học theo hệ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("danh-sach-mon-theo-he")]
        [ProducesResponseType(typeof(ResponseObject<List<MonHocTheoHeModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetMonHocTheoHe(MonHocTheoHeFilterModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetMonHocTheoHeQuery(model, _.SystemLog)));
        }

        /// <summary>
        /// Get danh sách lớp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("danh-sach-lop")]
        [ProducesResponseType(typeof(ResponseObject<List<LopHocModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> DanhSachLop(LopHocFilterModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLopQuery(model, _.SystemLog)));
        }


        /// <summary>
        /// Get nhóm tự chọn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("danh-sach-nhom-tu-chon")]
        [ProducesResponseType(typeof(ResponseObject<List<LopHocModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> DanhSachNhomTuChon(NhomTuChonFilterModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNhomTuChonQuery(model)));
        }


        /// <summary>
        /// Thêm môn tự chọn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-mon-tu-chon")]
        [ProducesResponseType(typeof(ResponseObject<List<LopHocModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateMonTuChon(CreateMonTuChonModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new CreateMonTuChonCommand(model, _.SystemLog)));
        }




        /// <summary>
        /// Xóa môn tự chọn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("delete-mon-tu-chon")]
        [ProducesResponseType(typeof(ResponseObject<List<LopHocModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_ADD))]
        public async Task<IActionResult> deleteMonTuChon(DeleteMonTuChonModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new DeleteMonTuChonCommand(model, _.SystemLog)));
        }

        /// <summary>
        /// Test công thức điểm
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("test-cong-thuc-diem")]
        [ProducesResponseType(typeof(ResponseObject<string>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> deleteMonTuChon(ListDiemModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new CongThucTinhDiemCommand(model, _.SystemLog)));
        }

        /// <summary>
        /// Import Excel chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("import-chuong-trinh-dao-tao-chi-tiet")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> ImportChuongTrinhDaoTaoChiTiet(ImportChuongTrinhDaoTaoModel model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new ImportChuongTrinhDaoTaoCommand(model, _.SystemLog)));
        }



        /// <summary>
        /// Check Import Excel chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("check-import-chuong-trinh-dao-tao-chi-tiet")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuongTrinhDaoTaoChiTietModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> CheckImportChuongTrinhDaoTaoChiTiet(CheckImportManyChuongTrinhDaoTaoChiTiet model)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new CheckImportChuongTrinhDaoTaoChiTietCommand(model, _.SystemLog)));

        }

        /// <summary>
        /// Nhóm tự chọn combobox
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("nhom-tu-chon-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<int>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> NhomTuChonCombobox()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNhomTuChonComboboxQuery(_.SystemLog)));

        }

        [HttpGet, Route("thanh-phan-diem")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiThanhPhanDiemSelectItemModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_VIEW))]
        public async Task<IActionResult> ThanhPhanDiem(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetLoaiThanhPhanDiemCtdtQuery(count, ts));
            });
        }


    }
}
