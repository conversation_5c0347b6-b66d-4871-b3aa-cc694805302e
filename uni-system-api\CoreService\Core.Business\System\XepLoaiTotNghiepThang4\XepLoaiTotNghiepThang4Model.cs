﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepLoaiTotNghiepThang4SelectItemModel
    {
        public int IdXepHang { get; set; }
        public string XepHang { get; set; }
        public string MaXepHang { get; set; }
        public int IdHe { get; set; }
    }

    public class XepLoaiTotNghiepThang4BaseModel
    {
        public int IdXepHang { get; set; }

        public float TuDiem { get; set; }

        public float TuDiemThang10 { get; set; }

        public float DenDiem { get; set; }

        public float DenDiemThang10 { get; set; }

        public string XepHang { get; set; }

        public string XepHangEn { get; set; }

        public string MaXepHang { get; set; }

        public int IdHe { get; set; }

        public string TenHe { get; set; }
    }


    public class XepLoaiTotNghiepThang4Model : XepLoaiTotNghiepThang4BaseModel
    {
      
    }

    public class XepLoaiTotNghiepThang4FilterModel : BaseQueryFilterModel
    {
        public XepLoaiTotNghiepThang4FilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepHang";
        }
    }

    public class CreateXepLoaiTotNghiepThang4Model
    {
        [MaxLength(20, ErrorMessage = "XepLoaiTotNghiepThang4.XepHang.MaxLength(20)")]
        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.XepHang.NotRequire")]
        public string XepHang { get; set; }

        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.DenDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.TuDiemThang10.NotRequire")]
        public float TuDiemThang10 { get; set; }

        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.DenDiemThang10.NotRequire")]
        public float DenDiemThang10 { get; set; }

        [MaxLength(20, ErrorMessage = "XepLoaiTotNghiepThang4.MaXepHang.MaxLength(20)")]
        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.MaXepHang.NotRequire")]
        public string MaXepHang { get; set; }

        [MaxLength(20, ErrorMessage = "XepLoaiTotNghiepThang4.XepHangEn.MaxLength(20)")]
        public string XepHangEn { get; set; }

        [Required(ErrorMessage = "XepLoaiTotNghiepThang4.IdHe.NotRequire")]
        public int IdHe { get; set; }
    }

    public class CreateManyXepLoaiTotNghiepThang4Model
    {
        public List<CreateXepLoaiTotNghiepThang4Model> listXepLoaiTotNghiepThang4Models { get; set; }
    }

    public class UpdateXepLoaiTotNghiepThang4Model : CreateXepLoaiTotNghiepThang4Model
    {
        public void UpdateEntity(SvXepHangTotNghiep input)
        {
            input.XepHang = XepHang;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.TuDiemThang10 = TuDiemThang10;
            input.DenDiemThang10 = DenDiemThang10;
            input.MaXepHang = MaXepHang;
            input.XepHangEn = XepHangEn;
            input.IdHe = IdHe;
        }
    }
}
