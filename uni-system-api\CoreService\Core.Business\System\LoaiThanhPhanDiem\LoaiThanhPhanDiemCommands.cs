﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


        public class CreateLoaiThanhPhanDiemCommand : IRequest<Unit>
        {
            public CreateLoaiThanhPhanDiemModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateLoaiThanhPhanDiemCommand(CreateLoaiThanhPhanDiemModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateLoaiThanhPhanDiemCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateLoaiThanhPhanDiemCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {LoaiThanhPhanDiemConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateLoaiThanhPhanDiemModel, SvThanhPhanMon>(model);

                    var checkCode = await _dataContext.SvThanhPhanMons.AnyAsync(x => x.IdThanhPhan == entity.IdThanhPhan || x.KyHieu == entity.KyHieu);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["KyHieu.Existed", entity.TenThanhPhan.ToString()]}");
                    }

                    await _dataContext.SvThanhPhanMons.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {LoaiThanhPhanDiemConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới loại điểm thành phần: {entity.TenThanhPhan }",
                        ObjectCode = LoaiThanhPhanDiemConstant.CachePrefix,
                        ObjectId = entity.IdThanhPhan .ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class CreateManyLoaiThanhPhanDiemCommand : IRequest<Unit>
        {
            public CreateManyLoaiThanhPhanDiemModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateManyLoaiThanhPhanDiemCommand(CreateManyLoaiThanhPhanDiemModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateManyLoaiThanhPhanDiemCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateManyLoaiThanhPhanDiemCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create many {LoaiThanhPhanDiemConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var listTenThanhPhanAdd = model.listLoaiThanhPhanDiemModels.Select(x => x.TenThanhPhan).ToList();
                    var listKyHieuAdd = model.listLoaiThanhPhanDiemModels.Select(x => x.KyHieu).ToList();
                    var entity = AutoMapperUtils.AutoMap<CreateManyLoaiThanhPhanDiemModel, SvThanhPhanMon>(model);

                    // Check data duplicate
                    if (listTenThanhPhanAdd.Count() != listTenThanhPhanAdd.Distinct().Count() || listKyHieuAdd.Count() != listKyHieuAdd.Distinct().Count())
                    {
                        throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                    }
                    // Check data exits DB
                    if (await _dataContext.SvThanhPhanMons.AnyAsync(x => listKyHieuAdd.Contains(x.KyHieu)))
                    {
                        throw new ArgumentException($"{_localizer["KyHieu.Existed"]}");
                    }

                    var listEntity = model.listLoaiThanhPhanDiemModels.Select(x => new SvThanhPhanMon()
                    {
                        KyHieu = x.KyHieu,
                        TenThanhPhan = x.TenThanhPhan,
                        TyLe = x.TyLe,
                        ChonMacDinh = x.ChonMacDinh,
                        ChuyenCan = x.ChuyenCan,
                        NhomThanhPhan = x.NhomThanhPhan,
                        TyLeNhom = x.TyLeNhom,
                        KyHieuNhom = x.KyHieuNhom,
                        ThucHanh = x.ThucHanh


                    }).ToList();

                    await _dataContext.AddRangeAsync(listEntity);
                    await _dataContext.SaveChangesAsync();

                    var createdIds = listEntity.Select(e => e.IdThanhPhan ).ToList();

                    Log.Information($"Create many {LoaiThanhPhanDiemConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Import Tôn Giáo từ file excel",
                        ObjectCode = LoaiThanhPhanDiemConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(createdIds)
                    });

                    //Xóa cache
                    _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class UpdateLoaiThanhPhanDiemCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiThanhPhanDiemModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiThanhPhanDiemCommand(int id, UpdateLoaiThanhPhanDiemModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiThanhPhanDiemCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiThanhPhanDiemCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiThanhPhanDiemConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvThanhPhanMons.FirstOrDefaultAsync(dt => dt.IdThanhPhan  == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvThanhPhanMons.AnyAsync(x => x.KyHieu == model.KyHieu  && x.IdThanhPhan != entity.IdThanhPhan);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KyHieu.Existed", model.KyHieu.ToString()]}");
                }

                Log.Information($"Before Update {LoaiThanhPhanDiemConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvThanhPhanMons.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiThanhPhanDiemConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiThanhPhanDiemConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại điểm thành phần: {entity.TenThanhPhan}",
                    ObjectCode = LoaiThanhPhanDiemConstant.CachePrefix,
                    ObjectId = entity.IdThanhPhan .ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey(entity.IdThanhPhan .ToString()));
                _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiThanhPhanDiemCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiThanhPhanDiemCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiThanhPhanDiemCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiThanhPhanDiemCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiThanhPhanDiemConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvThanhPhanMons.FirstOrDefaultAsync(x => x.IdThanhPhan  == id);

                _dataContext.SvThanhPhanMons.Remove(entity);

                Log.Information($"Delete {LoaiThanhPhanDiemConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại điểm thành phần: {entity.TenThanhPhan}",
                    ObjectCode = LoaiThanhPhanDiemConstant.CachePrefix,
                    ObjectId = entity.IdThanhPhan.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey());
                _cacheService.Remove(LoaiThanhPhanDiemConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
