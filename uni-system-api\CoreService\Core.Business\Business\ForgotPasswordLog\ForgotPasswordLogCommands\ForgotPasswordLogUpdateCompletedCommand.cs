﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Cập nhật nhật ký quên mật khẩu đã cập nhật mật khẩu thành công
    /// </summary>
    /// <param name="model">Model cập nhật nhật ký quên mật khẩu</param>
    /// <returns>Id nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogUpdateCompletedCommand : IRequest<Unit>
    {
        public string Token { get; set; }

        public ForgotPasswordLogUpdateCompletedCommand(string token)
        {
            Token = token;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogUpdateCompletedCommand, Unit>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<Unit> Handle(ForgotPasswordLogUpdateCompletedCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var filter = Builders<ForgotPasswordLog>.Filter.Eq(doc => doc.Token, request.Token);
                    var update = Builders<ForgotPasswordLog>.Update.Set(doc => doc.IsUpdatedPassword, true);
                    var result = await _logs.UpdateOneAsync(filter, update);
                }
                else
                {
                    // Không sử dụng MongoDB
                    var log = await _dataContext.ForgotPasswordLogs.FirstOrDefaultAsync(x => x.Token == request.Token);
                    if (log != null)
                    {
                        log.IsUpdatedPassword = true;
                        _dataContext.ForgotPasswordLogs.Update(log);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }
                return Unit.Value;
            }
        }
    }
}
