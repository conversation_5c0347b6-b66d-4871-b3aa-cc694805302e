﻿using Ocelot.Values;

namespace Ocelot.LoadBalancer.LoadBalancers
{
    public class DCDRLoadBalancerDelegatingHandler : DelegatingHandler
    {
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            //0HM16GD04N15H
            //Console.WriteLine("delegate:" + balancer._last);
            //request.RequestUri = new Uri("http://**************:8800/api/landelijke_tabellen/tabellen/1/waarden");
            HttpResponseMessage response = null;
            try
            {
                //do stuff and optionally call the base handler..
                response = await base.SendAsync(request, cancellationToken);
            }
            catch (Exception)
            {
                try
                {
                    Console.WriteLine("Marking as bad: " + request.RequestUri.Host);
                    var balancer = await DCDRLoadBalancer.GetBalancer(request);
                    if (balancer != null)
                    {
                        await balancer.MarkAsBad(new ServiceHostAndPort(request.RequestUri.Host, request.RequestUri.Port, request.RequestUri.Scheme));
                        // Get a new lease from the balancer
                        var s = await balancer.Lease(null); /* No Http Context needed for the balancer */
                        request.RequestUri = new Uri($"{s.Data.Scheme}://{s.Data.DownstreamHost}:{s.Data.DownstreamPort}/{request.RequestUri.PathAndQuery}");

                        response = await base.SendAsync(request, cancellationToken);
                    }
                }
                catch (Exception)
                {
                }
            }
            return response;
        }
    }
}
