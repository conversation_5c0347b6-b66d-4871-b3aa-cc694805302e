﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepLoaiHocBong")]
    public class SvXepLoaiHocBong
    {

        public SvXepLoaiHocBong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai_hb")]
        public int IdXepLoaiHb { get; set; }

        [Column("Ten_xep_loai"), MaxLength(50)]
        public string TenXepLoai { get; set; }

        [Column("Tu_diem_ht")]
        public float TuDiemHt { get; set; }

        [Column("Tu_diem_rl")]
        public float TuDiemRl { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("Tu_diem_ht4")]
        public float TuDiemHt4 { get; set; }

        [Column("Ma_xep_loai"), MaxLength(10)]
        public string MaXep<PERSON>oai { get; set; }

        [Column("So_tien")]
        public float SoTien { get; set; }


    }
}
