﻿using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class Location
    {
        [BsonElement("latitude")]
        [JsonPropertyName("latitude")]
        public decimal? Latitude { get; set; }

        [BsonElement("longitude")]
        [JsonPropertyName("longitude")]
        public decimal? Longitude { get; set; }

        [BsonElement("geo_location")]
        [JsonPropertyName("geoLocation")]
        public string GeoLocation { get; set; }
    }
}
