﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbToaNha")]
    public class TkbToaNha
    {

        public TkbToaNha()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nha")]
        public int IdNha { get; set; }

        [<PERSON>umn("Ma_nha"), MaxLength(10)]
        public string MaNha { get; set; }

        [Column("Ten_nha"), <PERSON><PERSON>ength(50)]
        public string TenNha { get; set; }

        [Column("ID_co_so")]
        public int IdCoSo { get; set; }


    }
}
