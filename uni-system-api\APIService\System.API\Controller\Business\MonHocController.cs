﻿using Core.API.Shared;
using Core.Business;
using Core.Business.System.MonHoc;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/mon-hoc")]
    [ApiExplorerSettings(GroupName = "105. Môn học")]
    [Authorize]
    public class MonHocController : ApiControllerBase
    {
        public MonHocController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Môn học cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<MonHocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxMonHocQuery(count, ts));
            });
        }

        /// <summary>
        /// Thêm môn học
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MON_HOC_ADD))]
        public async Task<IActionResult> InsertMonHoc(MonHocRequestModel m)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MON_HOC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MON_HOC_CREATE;
                return await _mediator.Send(new MonHocInsertCommand(m, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách môn học có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<BoMonBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MON_HOC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] MonHocFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetMonHocQuery(filter)));
        }

        /// <summary>
        /// Sửa môn học
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MON_HOC_EDIT))]
        public async Task<IActionResult> UpdateMonHoc(int id, [FromBody] MonHocItemModel filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MON_HOC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MON_HOC_UPDATE;
                return await _mediator.Send(new UpdateMonHocCommand(id, filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa môn học
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MON_HOC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MON_HOC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MON_HOC_DELETE;
                return await _mediator.Send(new DeleteMonHocCommand(id, u.SystemLog));
            });
        }

    }
}
