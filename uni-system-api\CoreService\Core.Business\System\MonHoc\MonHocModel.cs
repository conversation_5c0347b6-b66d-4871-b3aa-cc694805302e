﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Core.Data;

namespace Core.Business
{
    public class MonHocSelectItemModel
    {
        public int IdMonHoc { get; set; }
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
    }

    public class MonHocFilterModel : BaseQueryFilterModel
    {
        public string kyHieu { get; set; }
        public string tenMon { get; set; }
        public string tenTiengAnh { get; set; }
        public int idBm { get; set; }
        public int idHeDt { get; set; }
        public int idNhomHp { get; set; }

        public MonHocFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdMon";
        }
    }

    public class MonHocRequestModel
    {
        public string kyHieu { get; set; }
        public string tenMon { get; set; }
        public string tenTiengAnh { get; set; }
        public int idBm { get; set; }
        public int idHeDt { get; set; }
        public int idNhomHp { get; set; }
    }

    public class MonHocItemModel
    {
        public int IdMon { get; set; }
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
        public string TenTiengAnh { get; set; }
        public string TenVietTat { get; set; }
        public int IdBm { get; set; }
        public int IdHeDt { get; set; }
        public int IdNhomHp { get; set; }
        public bool HpThucHanh { get; set; }
        public bool ChatLuongCao { get; set; }
        public bool MonChungChi { get; set; }
        public bool MonNN { get; set; }
        public string KyHieuCu { get; set; }
        public string TenTiengPhap { get; set; }

        public void UpdateEntity(SvMonHoc input)
        {
            input.IdBoMon = IdBm;
            input.HocPhanTH = HpThucHanh;
            input.KyHieuCu = KyHieuCu;
            input.KyHieu = KyHieu;
            input.ChatLuongCao = ChatLuongCao;
            input.MonChungChi = MonChungChi;
            input.IdHeDt = IdHeDt;
            input.MonNn = MonNN;
            input.TenMon = TenMon;
            input.TenTiengAnh = TenTiengAnh;
            input.TenTiengPhap = TenTiengPhap;
            input.TenVietTat = TenVietTat;
        }
    }
}