﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Cập nhật nhật ký hệ thống
    /// </summary>
    /// <param name="model">Model cập nhật nhật ký hệ thống</param>
    /// <returns>Id nhật ký hệ thống</returns>
    public class SystemLogUpdateCommand : IRequest<Unit>
    {
        public SystemLog Model { get; set; }

        public SystemLogUpdateCommand(SystemLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<SystemLogUpdateCommand, Unit>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<Unit> Handle(SystemLogUpdateCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    await _logs.ReplaceOneAsync(log => log.Id == model.Id, model);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<SystemLog, SystemLogEntity>(model);
                    var entity = await _dataContext.SystemLogs
                        .FirstOrDefaultAsync(x => x.Id == dt.Id, cancellationToken);
                    if (entity != null)
                    {
                        AutoMapperUtils.AutoMap(dt, entity);
                        _dataContext.SystemLogs.Update(entity);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }

                return Unit.Value;
            }
        }
    }
}
