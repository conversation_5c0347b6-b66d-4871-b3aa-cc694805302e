﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiKhenThuongCommand : IRequest<Unit>
    {
        public CreateLoaiKhenThuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiKhenThuongCommand(CreateLoaiKhenThuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiKhenThuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiKhenThuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiKhenThuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiKhenThuongModel, SvLoaiKhenThuong>(model);

                var checkCode = await _dataContext.SvLoaiKhenThuongs.AnyAsync(x => x.IdLoaiKhenThuong == entity.IdLoaiKhenThuong || x.LoaiKhenThuong == entity.LoaiKhenThuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiKhenThuong.Existed", entity.LoaiKhenThuong.ToString()]}");
                }

                await _dataContext.SvLoaiKhenThuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiKhenThuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại khen thưởng: {entity.LoaiKhenThuong}",
                    ObjectCode = LoaiKhenThuongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiKhenThuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiKhenThuongCommand : IRequest<Unit>
    {
        public CreateManyLoaiKhenThuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiKhenThuongCommand(CreateManyLoaiKhenThuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiKhenThuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiKhenThuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiKhenThuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listLoaiKhenThuongAdd = model.listLoaiKhenThuongModels.Select(x => x.LoaiKhenThuong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiKhenThuongModel, SvLoaiKhenThuong>(model);

                // Check data duplicate
                if (listLoaiKhenThuongAdd.Count() != listLoaiKhenThuongAdd.Distinct().Count() )
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvLoaiKhenThuongs.AnyAsync(x => listLoaiKhenThuongAdd.Contains(x.LoaiKhenThuong)) )
                {
                    throw new ArgumentException($"{_localizer["LoaiKhenThuong.Existed"]}");
                }

                var listEntity = model.listLoaiKhenThuongModels.Select(x => new SvLoaiKhenThuong()
                {
                    IdLoaiKhenThuong = x.IdLoaiKhenThuong,
                    IdCap = x.IdCap,
                    LoaiKhenThuong = x.LoaiKhenThuong,
                    DiemThuong = x.DiemThuong

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdLoaiKhenThuong).ToList();

                Log.Information($"Create many {LoaiKhenThuongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import loại khen thưởng từ file excel",
                    ObjectCode = LoaiKhenThuongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiKhenThuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiKhenThuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiKhenThuongCommand(int id, UpdateLoaiKhenThuongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiKhenThuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiKhenThuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiKhenThuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiKhenThuongs.FirstOrDefaultAsync(dt => dt.IdLoaiKhenThuong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }


                var checkCode = await _dataContext.SvLoaiKhenThuongs.AnyAsync(x => x.LoaiKhenThuong == model.LoaiKhenThuong  && x.IdLoaiKhenThuong != model.IdLoaiKhenThuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiKhenThuong.Existed", model.LoaiKhenThuong.ToString()]}");
                }

                Log.Information($"Before Update {LoaiKhenThuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiKhenThuongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiKhenThuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiKhenThuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại khen thưởng: {entity.LoaiKhenThuong}",
                    ObjectCode = LoaiKhenThuongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiKhenThuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey(entity.IdLoaiKhenThuong.ToString()));
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiKhenThuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiKhenThuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiKhenThuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiKhenThuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiKhenThuongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiKhenThuongs.FirstOrDefaultAsync(x => x.IdLoaiKhenThuong == id);

                _dataContext.SvLoaiKhenThuongs.Remove(entity);

                Log.Information($"Delete {LoaiKhenThuongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại khen thưởng: {entity.LoaiKhenThuong}",
                    ObjectCode = LoaiKhenThuongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiKhenThuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey());
                _cacheService.Remove(LoaiKhenThuongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
