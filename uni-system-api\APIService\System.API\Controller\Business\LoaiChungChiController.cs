﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/loai-chung-chi")]
    [ApiExplorerSettings(GroupName = "73. Loại chúng chỉ")]
    [Authorize]
    public class LoaiChungChiController : ApiControllerBase
    {
        public LoaiChungChiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Loại chúng chỉ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiChungChiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiChungChiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Loại chúng chỉ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiChungChiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiChungChiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiChungChiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiChungChiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiChungChiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Loại chúng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_CHUNG_CHI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_CHUNG_CHI_CREATE;


                return await _mediator.Send(new CreateLoaiChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Loại chúng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyLoaiChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_CHUNG_CHI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_CHUNG_CHI_CREATE_MANY;


                return await _mediator.Send(new CreateManyLoaiChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLoaiChungChiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_CHUNG_CHI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_CHUNG_CHI_UPDATE;
                return await _mediator.Send(new UpdateLoaiChungChiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_CHUNG_CHI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_CHUNG_CHI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_CHUNG_CHI_DELETE;

                return await _mediator.Send(new DeleteLoaiChungChiCommand(id, u.SystemLog));
            });
        }

    }
}
