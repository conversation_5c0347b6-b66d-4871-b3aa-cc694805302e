﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteChungThuSoCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// X<PERSON>a chứng thư số theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Id chứng thư số cần xóa</param>
        public DeleteChungThuSoCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteChungThuSoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteChungThuSoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {ChungThuSoConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SgChungThuSos.FirstOrDefaultAsync(x => x.Id == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SgChungThuSos.Remove(dt);

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa chứng thư số serial: {dt.SerialNumber}",
                    ObjectCode = ChungThuSoConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(ChungThuSoConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(ChungThuSoConstant.BuildCacheKey());

                Log.Information($"Delete {ChungThuSoConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
