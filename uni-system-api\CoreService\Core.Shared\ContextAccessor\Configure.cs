using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Core.Shared.ContextAccessor
{
    public static class ApplicationContextAccessor
    {
        public static void Configure(IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            services.AddScoped<HttpContextAccessorWrapper>();
            services.AddScoped<MessageQueueContextAccessor>();
            services.AddScoped<Func<IContextAccessor>>(provider =>
            {
                var httpContextAccessor = provider.GetRequiredService<IHttpContextAccessor>();
                if (httpContextAccessor.HttpContext != null)
                {
                    return () => provider.GetRequiredService<HttpContextAccessorWrapper>();
                }

                return () => provider.GetRequiredService<MessageQueueContextAccessor>();
            });
        }
    }
}