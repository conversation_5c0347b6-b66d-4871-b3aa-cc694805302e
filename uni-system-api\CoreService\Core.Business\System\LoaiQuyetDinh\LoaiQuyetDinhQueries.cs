﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiQuyetDinhQuery : IRequest<List<LoaiQuyetDinhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại quyết định cho combobox
        /// </summary>
        /// <param name="count">S<PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiQuyetDinhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiQuyetDinhQuery, List<LoaiQuyetDinhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiQuyetDinhSelectItemModel>> Handle(GetComboboxLoaiQuyetDinhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiQuyetDinhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiQuyetDinhs.OrderBy(x => x.TenLoaiQd)
                                select new LoaiQuyetDinhSelectItemModel()
                                {
                                    IdLoaiQd = dt.IdLoaiQd,
                                    MaQd = dt.MaQd,
                                    TenLoaiQd = dt.TenLoaiQd
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenLoaiQd.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiQuyetDinhQuery : IRequest<PaginationList<LoaiQuyetDinhBaseModel>>
    {
        public LoaiQuyetDinhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại quyết định có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiQuyetDinhQuery(LoaiQuyetDinhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiQuyetDinhQuery, PaginationList<LoaiQuyetDinhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiQuyetDinhBaseModel>> Handle(GetFilterLoaiQuyetDinhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiQuyetDinhs
                            select new LoaiQuyetDinhBaseModel
                            {
                                IdLoaiQd = dt.IdLoaiQd,
                                MaQd = dt.MaQd,
                                TenLoaiQd = dt.TenLoaiQd,
                                ChuyenLop = dt.ChuyenLop,
                                ThoiHoc = dt.ThoiHoc,
                                NgungHoc = dt.NgungHoc,
                                HocTiep = dt.HocTiep,
                                ChuyenTruongDi = dt.ChuyenTruongDi,
                                ChuyentruongDen = dt.ChuyentruongDen,
                                ThoiHocQuyChe = dt.ThoiHocQuyChe,
                                XoaTenkhoiLop = dt.XoaTenkhoiLop

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenLoaiQd.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiQuyetDinhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiQuyetDinhByIdQuery : IRequest<LoaiQuyetDinhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại quyết định theo id
        /// </summary>
        /// <param name="id">Id loại quyết định</param>
        public GetLoaiQuyetDinhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiQuyetDinhByIdQuery, LoaiQuyetDinhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiQuyetDinhModel> Handle(GetLoaiQuyetDinhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiQuyetDinhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiQuyetDinhs.FirstOrDefaultAsync(x => x.IdLoaiQd == id);

                    return AutoMapperUtils.AutoMap<SvLoaiQuyetDinh, LoaiQuyetDinhModel>(entity);
                });
                return item;
            }
        }
    }
}
