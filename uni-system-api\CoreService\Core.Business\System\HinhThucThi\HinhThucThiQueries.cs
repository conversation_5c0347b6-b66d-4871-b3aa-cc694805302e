﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxHinhThucThiQuery : IRequest<List<HinhThucThiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y hình thức thi cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHinhThucThiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHinhThucThiQuery, List<HinhThucThiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HinhThucThiSelectItemModel>> Handle(GetComboboxHinhThucThiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HinhThucThiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvHinhThucThis.OrderBy(x => x.HinhThucThi)
                                select new HinhThucThiSelectItemModel()
                                {
                                    IdHinhThucThi = dt.IdHinhThucThi,
                                    MaHinhThucThi = dt.MaHinhThucThi,
                                    HinhThucThi = dt.HinhThucThi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.HinhThucThi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHinhThucThiQuery : IRequest<PaginationList<HinhThucThiBaseModel>>
    {
        public HinhThucThiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách hình thức thi có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHinhThucThiQuery(HinhThucThiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHinhThucThiQuery, PaginationList<HinhThucThiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HinhThucThiBaseModel>> Handle(GetFilterHinhThucThiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvHinhThucThis
                            select new HinhThucThiBaseModel
                            {
                                IdHinhThucThi = dt.IdHinhThucThi,
                                MaHinhThucThi = dt.MaHinhThucThi,
                                HinhThucThi = dt.HinhThucThi,
                                GhiChu = dt.GhiChu,
                                KhongKiemTraTrungLich = dt.KhongKiemTraTrungLich

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.HinhThucThi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HinhThucThiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHinhThucThiByIdQuery : IRequest<HinhThucThiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin hình thức thi theo id
        /// </summary>
        /// <param name="id">Id hình thức thi</param>
        public GetHinhThucThiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHinhThucThiByIdQuery, HinhThucThiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HinhThucThiModel> Handle(GetHinhThucThiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HinhThucThiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvHinhThucThis.FirstOrDefaultAsync(x => x.IdHinhThucThi == id);

                    return AutoMapperUtils.AutoMap<SvHinhThucThi, HinhThucThiModel>(entity);
                });
                return item;
            }
        }
    }
}
