﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Core.Data
{
    [Table("svHe")]
    public class SvHe
    {
        public SvHe()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_he")]
        public int IdHe { get; set; }

        [<PERSON>umn("Ma_he"), Max<PERSON>ength(5)]
        public string MaHe { get; set; }

        [<PERSON>umn("Ten_he"), MaxLength(50)]
        public string TenHe { get; set; }

        [<PERSON>umn("Ten_he_en"), MaxLength(50)]
        public string TenHeEn { get; set; }

        [Column("Quy_che")]
        public int? QuyChe { get; set; }

        [Column("Ten_bac_dao_tao"), MaxLength(200)]
        public string TenBacDaoTao { get; set; }

        [Column("Ten_bac_dao_tao_en"), <PERSON><PERSON>ength(200)]
        public string TenBacDaoTaoEn { get; set; }

        [Column("Hinh_thuc_dao_tao"), <PERSON><PERSON>ength(200)]
        public string HinhThuc<PERSON>aoTao { get; set; }

        [Column("Hinh_thuc_dao_tao_en"), MaxLength(200)]
        public string HinhThucDaoTaoEn { get; set; }

    }
}
