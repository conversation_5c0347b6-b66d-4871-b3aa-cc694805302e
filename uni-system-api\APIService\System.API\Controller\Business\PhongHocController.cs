﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phong-hoc")]
    [ApiExplorerSettings(GroupName = "33. Phòng học")]
    [Authorize]
    public class PhongHocController : ApiControllerBase
    {
        public PhongHocController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }


        /// <summary>
        /// Lấy danh sách phòng học cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhongHocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhongHocQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phòng học có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<PhongHocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] PhongHocFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterPhongHocQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết phòng học
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PhongHocModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetPhongHocByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới phòng học
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreatePhongHocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_HOC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_HOC_CREATE;


                return await _mediator.Send(new CreatePhongHocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel phòng học
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyPhongHocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_HOC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_HOC_CREATE_MANY;


                return await _mediator.Send(new CreateManyPhongHocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa phòng học
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdatePhongHocModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_HOC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_HOC_UPDATE;
                return await _mediator.Send(new UpdatePhongHocCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa phòng học
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_HOC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_HOC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_HOC_DELETE;

                return await _mediator.Send(new DeletePhongHocCommand(id, u.SystemLog));
            });
        }


    }
}
