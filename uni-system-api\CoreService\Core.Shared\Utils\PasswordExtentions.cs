﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Core.Shared
{
    public static class PasswordExtentions
    {
        /// <summary>
        /// Độ dài từ 8-20 ký tự
        /// Ít nhất 8 ký tự
        /// Có ít nhất 1 ký tự in hoa, 1 ký tự in thường, và 1 ký tự số
        /// Phải có ít nhất 1 ký tự đặc biệt thuộc 19 ký tự sau:  !@#$%^&(),.?:{}|<> * 
        /// </summary>
        /// <returns></returns>
        public static bool ValidatePassword(string password)
        {
            var regex = new Regex(@"^(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*(),.?:{}|<>])(?=.*\d)(?!.*\s)[A-Za-z!@#$%^&*(),.?:{}|<>0-9]{8,20}$");
            if (!regex.IsMatch(password))
                return false;
            return true;
        }

        /// <summary>
        /// Độ dài từ 8-20 ký tự
        /// Ít nhất 8 ký tự
        /// Có ít nhất 1 ký tự in hoa, 1 ký tự in thường, và 1 ký tự số
        /// Phải có ít nhất 1 ký tự đặc biệt thuộc 19 ký tự sau:  !@#$%^&(),.?:{}|<> * 
        /// </summary>
        /// <returns></returns>
        public static string GeneratePassword()
        {
            const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
            const string numbers = "0123456789";
            const string specialCharacters = "!@#$%^&(),.?:{}|<>*";

            Random random = new Random();
            string[] randomChars = new string[8]
            {
                upperCase,
                lowerCase,
                numbers,
                specialCharacters,
                upperCase + lowerCase + numbers + specialCharacters,
                upperCase + lowerCase + numbers + specialCharacters,
                upperCase + lowerCase + numbers + specialCharacters,
                upperCase + lowerCase + numbers + specialCharacters
            };

            return new string(Enumerable.Range(0, 8)
                .Select(i => randomChars[i][random.Next(randomChars[i].Length)]).ToArray())
                .Shuffle();
        }

        private static string Shuffle(this string str)
        {
            char[] array = str.ToCharArray();
            Random rng = new Random();
            int n = array.Length;
            while (n > 1)
            {
                int k = rng.Next(n--);
                char temp = array[n];
                array[n] = array[k];
                array[k] = temp;
            }
            return new string(array);
        }
    }
}
