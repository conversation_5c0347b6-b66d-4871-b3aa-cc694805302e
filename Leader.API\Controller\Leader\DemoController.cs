﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business.System;

namespace API.Controller
{
    [ApiController]
    [Route("leader/v1/demo")]
    [ApiExplorerSettings(GroupName = "01. Demo")]
    [Authorize]
    public class DemoController : ApiControllerBase
    {
        public DemoController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách quan hệ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<SelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhanHeQuery(count, ts));
            });
        }
    }
}
