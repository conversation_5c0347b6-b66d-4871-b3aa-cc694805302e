﻿using Core.Data;
using Core.Shared;
using FluentEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class EmailTemplateBaseModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "email-template.code.required")]
        public string Code { get; set; }

        [Required(ErrorMessage = "email-template.name.required")]
        [MaxLength(128, ErrorMessage = "email-template.name.max-length")]
        public string Name { get; set; }

        public string Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    public class EmailTemplateModel : EmailTemplateBaseModel
    {
        public string FromEmail { get; set; }
        public string FromUser { get; set; }
        public bool IsHighPriority { get; set; }
        public List<string> CC { get; set; }
        public List<string> BCC { get; set; }
        public string Subject { get; set; }
        public string Template { get; set; }
    }

    public class CreateEmailTemplateModel : EmailTemplateModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateEmailTemplateModel : EmailTemplateModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(EmailTemplate entity)
        {
            entity.Name = this.Name;
            entity.FromEmail = this.FromEmail;
            entity.FromUser = this.FromUser;
            entity.IsHighPriority = this.IsHighPriority;
            entity.CC = this.CC;
            entity.BCC = this.BCC;
            entity.Subject = this.Subject;
            entity.Template = this.Template;
            entity.IsActive = this.IsActive;
            entity.Description = this.Description;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class EmailTemplateSelectItemModel : SelectItemModel
    {
    }

    public class EmailTemplateQueryFilter : BaseQueryFilterModel
    {
    }

    public class SendMailUsingTemplateModel
    {
        public string EmailTemplateCode { get; set; }
        public List<string> To { get; set; }
        public List<string> CC { get; set; }
        public List<string> BCC { get; set; }
        public List<Attachment> Attachments { get; set; }
        public object Data { get; set; }
    }
}
