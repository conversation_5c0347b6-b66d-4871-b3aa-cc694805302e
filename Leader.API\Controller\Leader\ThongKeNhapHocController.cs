﻿using Core.API.Shared;
using Core.Business;
using Leader.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module Thống kê nhập học
    /// </summary>
    [ApiController]
    [Route("leader/v1/thong-ke-nhap-hoc")]
    [ApiExplorerSettings(GroupName = "10. Thống kê nhập học")]
    [Authorize]
    public class ThongKeNhapHocController : ApiControllerBase
    {
        public ThongKeNhapHocController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách thống kê nhập học theo điều kiện lọc
        /// </summary> 
        /// <param name="filter"><PERSON><PERSON><PERSON><PERSON> kiện lọc</param>
        /// <returns><PERSON><PERSON> sách thống kê nhập học</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeNhapHocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "THONG_KE_NHAP_HOC_VIEW")]
        public async Task<IActionResult> Filter([FromBody] ThongKeNhapHocQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetThongKeNhapHocByFilterQuery(filter, u.SystemLog));
            });
        }

    }
}
