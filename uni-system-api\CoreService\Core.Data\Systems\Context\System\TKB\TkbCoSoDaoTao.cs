﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbCoSoDaoTao")]
    public class TkbCoSoDaoTao
    {

        public TkbCoSoDaoTao()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_co_so")]
        public int IdCoSo{ get; set; }

        [Column("Ma_co_so"), MaxLength(20)]
        public string MaCoSo { get; set; }

        [Column("Ten_co_so"), MaxLength(200)]
        public string TenCoSo { get; set; }

        [Column("Day_ngoai_truong")]
        public bool DayNgoaiTruong { get; set; }

        [Column("gdCongViec")]
        public bool GdCongViec { get; set; }

    }
}
