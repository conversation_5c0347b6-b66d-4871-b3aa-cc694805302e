﻿using Core.Shared.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Leader.Shared.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Leader.Business.ThirdPartyBussiness.Hrm
{
    public class EmployeeService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IHrmService _service;

        public EmployeeService(IServiceProvider serviceProvider, IConfiguration config)
        {
            _serviceProvider = serviceProvider;
            if (!string.IsNullOrEmpty(config["HRMClient:Hou:ApiUrl"]))
            {
                _service = _serviceProvider.GetNamedService<IHrmService>(HrmServiceContants.Hou);
            }
            else
            {
                throw new Exception("No HRM client configured");
            }
        }

        public async Task<HrmEmployeeDetailModel> GetEmployeeByEmailAsync(string email)
        {
            return await _service.GetEmployeeByEmailAsync(email);
        }

        public async Task<List<HrmEmployeeModel>> GetEmployeesAsync()
        {
            return await _service.GetAllEmployeesAsync();
        }
    }
}
