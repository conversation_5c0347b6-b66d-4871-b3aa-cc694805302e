﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/mau-chu-ky")]
    [ApiExplorerSettings(GroupName = "10. MauChuKy (Mẫu chữ ký)")]
    [Authorize]
    public class MauChuKyController : ApiControllerBaseV2
    {
        public MauChuKyController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới mẫu chữ ký
        /// </summary>
        /// <param name="model">Thông tin mẫu chữ ký</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MAU_CHU_KY_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateMauChuKyModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_MAU_CHU_KY_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_MAU_CHU_KY_CREATE;

                return await _mediator.Send(new CreateMauChuKyCommand(model));
            });
        }

        /// <summary>
        /// Cập nhật mẫu chữ ký
        /// </summary>
        /// <param name="model">Thông tin mẫu chữ ký</param>
        /// <returns></returns>
        [HttpPut, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MAU_CHU_KY_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromBody] UpdateMauChuKyModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_MAU_CHU_KY_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_MAU_CHU_KY_UPDATE;

                return await _mediator.Send(new UpdateMauChuKyCommand(model));
            });
        }

        /// <summary>
        /// Xóa mẫu chữ ký
        /// </summary>
        /// <param name="id">Id mẫu chữ ký</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MAU_CHU_KY_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_MAU_CHU_KY_DELETE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_MAU_CHU_KY_DELETE;

                return await _mediator.Send(new DeleteMauChuKyCommand(id));
            });
        }

        /// <summary>
        /// Lấy thông tin mẫu chữ ký theo Id
        /// </summary>
        /// <param name="id">Id mẫu chữ ký</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MAU_CHU_KY_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<MauChuKyModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetMauChuKyByIdQuery(id));
            });
        }
        #endregion

        #region List
        /// <summary>
        /// Lấy danh sách mẫu chữ ký theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MAU_CHU_KY_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<MauChuKyBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] MauChuKyQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                filter.UserId = _contextAccessor.UserId;
                return await _mediator.Send(new GetFilterMauChuKyQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách mẫu chữ ký cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<MauChuKySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxMauChuKyQuery(count, ts));

                return result;
            });
        }

        /// <summary>
        /// Lấy danh sách mẫu chữ ký theo User
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <param name="loaiKySuDung">Loại ký sử dụng (optional)</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-user")]
        [ProducesResponseType(typeof(ResponseObject<List<MauChuKySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByUser(int count = 0, string ts = "", short? loaiKySuDung = null)
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxMauChuKyQuery(count, ts, _contextAccessor.UserId, loaiKySuDung));

                return result;
            });
        }
        #endregion
    }
}
