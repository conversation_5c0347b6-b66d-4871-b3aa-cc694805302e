﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/muc-huong-bhyt")]
    [ApiExplorerSettings(GroupName = "500. Mức hưởng BHYT")]
    [Authorize]
    public class MucHuongBhytController : ApiControllerBase
    {
        public MucHuongBhytController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }


        /// <summary>
        /// Thêm mới mức hưởng BHYT
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MUC_HUONG_BHYT_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateMucHuongBhytModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MUC_HUONG_BHYT_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MUC_HUONG_BHYT_CREATE;


                return await _mediator.Send(new CreateMucHuongBhytCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa mức hưởng bhyt
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MUC_HUONG_BHYT_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateMucHuongBhytModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MUC_HUONG_BHYT_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MUC_HUONG_BHYT_UPDATE;
                return await _mediator.Send(new UpdateMucHuongBhytCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa mức hưởng bhyt
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MUC_HUONG_BHYT_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_MUC_HUONG_BHYT_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_MUC_HUONG_BHYT_DELETE;

                return await _mediator.Send(new DeleteMucHuongBhytCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách mức hưởng bhyt cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<MucHuongBhytSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxMucHuongBhytQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách mức hưởng bhyt có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<MucHuongBhytBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MUC_HUONG_BHYT_VIEW))]
        public async Task<IActionResult> Filter([FromBody] MucHuongBhytFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterMucHuongBhytQuery(filter)));
        }

        /// <summary>
        /// Lấy chi tiết mức hưởng bhyt
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<MucHuongBhytModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.MUC_HUONG_BHYT_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetMucHuongBhytByIdQuery(id)));
        }

    }
}
