﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class NganhSelectItemModel
    {
        public int IdNganh { get; set; }
        public string MaNganh { get; set; }
        public string TenNganh { get; set; }
    }

    public class NganhBaseModel 
    {
        public int IdNganh { get; set; }
        public string MaNganh { get; set; }
        public string TenNganh { get; set; }
        public string TenNganhEn { get; set; }
        public bool SuPham { get; set; }

    }


    public class NganhModel
    {
        public int IdNganh { get; set; }
        public string MaNganh { get; set; }
        public string TenNganh { get; set; }
        public string TenNganhEn { get; set; }
        public bool SuPham { get; set; } = false;
    }

    public class NganhFilterModel : BaseQueryFilterModel
    {
        public NganhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNganh";
        }
    }

    public class CreateNganhModel
    {
        [Required(ErrorMessage = "Nganh.IdNganh.NotRequire")]
        public int IdNganh { get; set; }

        [MaxLength(20, ErrorMessage = "Nganh.MaNganh.MaxLength(20)")]
        [Required(ErrorMessage = "Nganh.MaNganh.NotRequire")]
        public string MaNganh { get; set; }

        [MaxLength(200, ErrorMessage = "Nganh.TenNganh.MaxLength(200)")]
        [Required(ErrorMessage = "Nganh.TenNganh.NotRequire")]
        public string TenNganh { get; set; }

        [MaxLength(200, ErrorMessage = "Nganh.TenNganhEn.MaxLength(200)")]
        public string TenNganhEn { get; set; }

        [Required(ErrorMessage = "Nganh.SuPham.NotRequire")]
        public bool SuPham { get; set; }

    }

    public class CreateManyNganhModel
    {
        public List<CreateNganhModel> listNganhModels { get; set; }
    }

    public class UpdateNganhModel : CreateNganhModel
    {
        public void UpdateEntity(SvNganh input)
        {
            input.IdNganh = IdNganh;
            input.MaNganh = MaNganh;
            input.TenNganh = TenNganh;
            input.TenNganhEn = TenNganhEn;
            input.SuPham = SuPham;

        }
    }
}
