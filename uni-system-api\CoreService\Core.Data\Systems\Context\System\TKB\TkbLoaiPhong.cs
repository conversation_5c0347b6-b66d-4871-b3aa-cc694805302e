﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbLoaiPhong")]
    public class TkbLoaiPhong
    {

        public TkbLoaiPhong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_loai_phong")]
        public int IdLoaiPhong { get; set; }

        [Column("Ma_loai"), MaxLength(5)]
        public string MaLoaiPhong { get; set; }

        [<PERSON>umn("Ten_loai_phong"), MaxLength(50)]
        public string <PERSON><PERSON>oaiPhong { get; set; }

        [Column("Thuc_hanh")]
        public bool ThucHanh { get; set; }


    }
}
