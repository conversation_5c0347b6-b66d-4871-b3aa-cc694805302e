﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// L<PERSON>y danh sách nhật ký gửi mail theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách nhật ký gửi mail</returns>
    public class SendMailLogFilterQuery : IRequest<PaginationList<SendMailLog>>
    {
        public SendMailLogQueryFilter Filter { get; set; }

        public SendMailLogFilterQuery(SendMailLogQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<SendMailLogFilterQuery, PaginationList<SendMailLog>>
        {
            private readonly IMongoCollection<SendMailLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemReadDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SendMailLog>(MongoCollections.SendMailLog);
                }
            }

            public async Task<PaginationList<SendMailLog>> Handle(SendMailLogFilterQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                // set time StartDate = 0h and time EndDate = 24h
                if (filter.StartDate.HasValue && filter.EndDate.HasValue)
                {
                    filter.StartDate = filter.StartDate.Value.Date;
                    filter.EndDate = filter.EndDate.Value.AddDays(1).Date;
                }

                if (string.IsNullOrEmpty(filter.TextSearch))
                {
                    filter.TextSearch = "";
                }

                if (string.IsNullOrEmpty(filter.EmailTemplateCode))
                {
                    filter.EmailTemplateCode = "";
                }

                List<SendMailLog> listResult = new List<SendMailLog>();
                long dataCount = 0;
                long totalCount = 0;

                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<SendMailLog>.Filter.And(
                        Builders<SendMailLog>.Filter.Where(p => p.ToEmail.ToLower().Contains(filter.TextSearch.ToLower())
                            || p.Subject.ToLower().Contains(filter.TextSearch.ToLower())
                            || string.IsNullOrEmpty(filter.TextSearch)
                        ),
                        Builders<SendMailLog>.Filter.Where(p => p.TraceId.Equals(filter.TradeId) || string.IsNullOrEmpty(filter.TradeId)),
                        Builders<SendMailLog>.Filter.Where(p => p.EmailTemplateCode.ToLower().Equals((object)filter.EmailTemplateCode.ToLower()) || string.IsNullOrEmpty(filter.EmailTemplateCode)),
                        Builders<SendMailLog>.Filter.Where(p => (filter.StartDate.HasValue && filter.EndDate.HasValue && p.CreatedDate >= filter.StartDate && p.CreatedDate < filter.EndDate)
                            || (!filter.StartDate.HasValue && !filter.EndDate.HasValue))
                    );

                    IFindFluent<SendMailLog, SendMailLog> data = _logs.Find(builder).Sort(Builders<SendMailLog>.Sort.Descending(x => x.CreatedDate));

                    totalCount = await data.CountDocumentsAsync();

                    // Pagination
                    if (filter.PageSize.HasValue && filter.PageNumber.HasValue)
                    {
                        if (filter.PageSize <= 0)
                        {
                            filter.PageSize = QueryFilter.DefaultPageSize;
                        }

                        //Calculate nunber of rows to skip on pagesize
                        int excludedRows = (filter.PageNumber.Value - 1) * (filter.PageSize.Value);
                        if (excludedRows <= 0)
                        {
                            excludedRows = 0;
                        }

                        // Query
                        data = data.Skip(excludedRows).Limit(filter.PageSize.Value);
                        dataCount = await data.CountDocumentsAsync();

                        listResult = await data.ToListAsync();
                    }
                }
                // Nếu không có cấu hình MongoDB thì lấy từ SQL
                else
                {
                    var data = (from dt in _dataContext.SendMailLogs.AsNoTracking()
                                select dt);

                    if (!string.IsNullOrEmpty(filter.TextSearch))
                    {
                        string ts = filter.TextSearch.Trim().ToLower();
                        data = data.Where(x => x.ToEmail.ToLower().Contains(ts)
                            || x.Subject.ToLower().Contains(ts)
                            || x.EmailTemplateCode.ToLower().Contains(ts));
                    }

                    data = data.OrderByDescending(x => x.Id);

                    // Apply pagination with improved performance
                    if (filter.PageSize <= 0)
                    {
                        filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    // Get total count asynchronously
                    totalCount = await data.CountAsync();

                    // Calculate number of rows to skip on pagesize
                    int excludedRows = (filter.PageNumber.Value - 1) * filter.PageSize.Value;
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Query
                    data = data.Skip(excludedRows).Take(filter.PageSize.Value);
                    dataCount = data.Count();

                    var listData = await data.ToListAsync();

                    listResult = AutoMapperUtils.AutoMap<SendMailLogEntity, SendMailLog>(listData);
                }

                return new PaginationList<SendMailLog>()
                {
                    DataCount = (int)dataCount,
                    TotalCount = (int)totalCount,
                    PageNumber = filter.PageNumber ?? 0,
                    PageSize = filter.PageSize ?? 0,
                    Data = listResult
                };
            }
        }
    }
}
