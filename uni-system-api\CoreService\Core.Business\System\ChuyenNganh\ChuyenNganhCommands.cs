﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateChuyenNganhCommand : IRequest<Unit>
    {
        public CreateChuyenNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateChuyenNganhCommand(CreateChuyenNganhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateChuyenNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateChuyenNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ChuyenNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChuyenNganhModel, SvChuyenNganh>(model);

                var checkCode = await _dataContext.SvChuyenNganhs.AnyAsync(x => x.IdChuyenNganh == entity.IdChuyenNganh || x.ChuyenNganh == entity.ChuyenNganh || x.MaChuyenNganh == entity.MaChuyenNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuyenNganh.Existed", entity.ChuyenNganh.ToString()]}");
                }

                await _dataContext.SvChuyenNganhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuyenNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới chuyên ngành: {entity.ChuyenNganh}",
                    ObjectCode = ChuyenNganhConstant.CachePrefix,
                    ObjectId = entity.IdChuyenNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyChuyenNganhCommand : IRequest<Unit>
    {
        public CreateManyChuyenNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyChuyenNganhCommand(CreateManyChuyenNganhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyChuyenNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyChuyenNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {ChuyenNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listChuyenNganhAdd = model.listChuyenNganhModels.Select(x => x.ChuyenNganh).ToList();
                var listMaChuyenNganhAdd = model.listChuyenNganhModels.Select(x => x.MaChuyenNganh).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyChuyenNganhModel, SvChuyenNganh>(model);

                // Check data duplicate
                if (listChuyenNganhAdd.Count() != listChuyenNganhAdd.Distinct().Count() || listMaChuyenNganhAdd.Count() != listMaChuyenNganhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvChuyenNganhs.AnyAsync(x => listChuyenNganhAdd.Contains(x.ChuyenNganh)) || await _dataContext.SvChuyenNganhs.AnyAsync(x => listMaChuyenNganhAdd.Contains(x.MaChuyenNganh)))
                {
                    throw new ArgumentException($"{_localizer["ChuyenNganh.Existed"]}");
                }

                var listEntity = model.listChuyenNganhModels.Select(x => new SvChuyenNganh()
                {
                    IdChuyenNganh = x.IdChuyenNganh,
                    MaChuyenNganh = x.MaChuyenNganh,
                    ChuyenNganh = x.ChuyenNganh,
                    ChuyenNganhEn = x.ChuyenNganhEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdChuyenNganh).ToList();

                Log.Information($"Create many {ChuyenNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import chuyên ngành từ file excel",
                    ObjectCode = ChuyenNganhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateChuyenNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateChuyenNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateChuyenNganhCommand(int id, UpdateChuyenNganhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateChuyenNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateChuyenNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ChuyenNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvChuyenNganhs.FirstOrDefaultAsync(dt => dt.IdChuyenNganh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.SvChuyenNganhs.AnyAsync(x => (x.ChuyenNganh == model.ChuyenNganh || x.MaChuyenNganh == model.MaChuyenNganh) && x.IdChuyenNganh != model.IdChuyenNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuyenNganh.Existed", model.ChuyenNganh.ToString()]}");
                }

                Log.Information($"Before Update {ChuyenNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvChuyenNganhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChuyenNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ChuyenNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chuyên ngành: {entity.ChuyenNganh}",
                    ObjectCode = ChuyenNganhConstant.CachePrefix,
                    ObjectId = entity.IdChuyenNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey(entity.IdChuyenNganh.ToString()));
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChuyenNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteChuyenNganhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteChuyenNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteChuyenNganhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ChuyenNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvChuyenNganhs.FirstOrDefaultAsync(x => x.IdChuyenNganh == id);

                _dataContext.SvChuyenNganhs.Remove(entity);

                Log.Information($"Delete {ChuyenNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa chuyên ngành: {entity.ChuyenNganh}",
                    ObjectCode = ChuyenNganhConstant.CachePrefix,
                    ObjectId = entity.IdChuyenNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey());
                _cacheService.Remove(ChuyenNganhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
