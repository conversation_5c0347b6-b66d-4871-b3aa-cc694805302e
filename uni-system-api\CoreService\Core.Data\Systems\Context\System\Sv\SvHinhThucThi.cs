﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svHinhThucThi")]
    public class SvHinhThucThi
    {

        public SvHinhThucThi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_hinh_thuc_thi")]
        public int IdHinhThucThi { get; set; }

        [Column("Ma_hinh_thuc_thi"), MaxLength(50)]
        public string <PERSON>HinhThucThi { get; set; }

        [Column("ten_hinh_thuc_thi"), MaxLength(50)]
        public string HinhThucThi { get; set; }

        [Column("ghi_chu"), MaxLength(200)]
        public string GhiChu { get; set; }
        
        [Column("Khong_kiem_tra_trung_lich")]
        public bool KhongKiemTraTrungLich { get; set; }


    }
}
