# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["uni-leader-api/Leader.API/Leader.API.csproj", "uni-leader-api/Leader.API/"]
COPY ["uni-leader-api/Leader.Business/Leader.Business.csproj", "uni-leader-api/Leader.Business/"]
COPY ["uni-leader-api/Leader.Data/Leader.Data.csproj", "uni-leader-api/Leader.Data/"]
COPY ["uni-leader-api/uni-system-api/CoreService/Core.Data/Core.Data.csproj", "uni-leader-api/uni-system-api/CoreService/Core.Data/"]
COPY ["uni-leader-api/uni-system-api/CoreService/Core.Shared/Core.Shared.csproj", "uni-leader-api/uni-system-api/CoreService/Core.Shared/"]
COPY ["uni-leader-api/Leader.Shared/Leader.Shared.csproj", "uni-leader-api/Leader.Shared/"]
COPY ["uni-leader-api/uni-system-api/CoreService/Core.Business/Core.Business.csproj", "uni-leader-api/uni-system-api/CoreService/Core.Business/"]
COPY ["uni-leader-api/uni-system-api/CoreService/Core.DataLog/Core.DataLog.csproj", "uni-leader-api/uni-system-api/CoreService/Core.DataLog/"]
COPY ["uni-leader-api/uni-system-api/APIService/Core.API.Shared/Core.API.Shared.csproj", "uni-leader-api/uni-system-api/APIService/Core.API.Shared/"]
RUN dotnet restore "./uni-leader-api/Leader.API/Leader.API.csproj"
COPY . .
WORKDIR "/src/uni-leader-api/Leader.API"
RUN dotnet build "./Leader.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Leader.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Leader.API.dll"]