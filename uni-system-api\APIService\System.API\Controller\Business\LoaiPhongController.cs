﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/loai-phong")]
    [ApiExplorerSettings(GroupName = "83. Loại phòng")]
    [Authorize]
    public class LoaiPhongController : ApiControllerBase
    {
        public LoaiPhongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách loại phòng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiPhongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiPhongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách loại phòng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiPhongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_PHONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiPhongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiPhongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết loại phòng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiPhongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_PHONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiPhongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới loại phòng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_PHONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiPhongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_PHONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_PHONG_CREATE;


                return await _mediator.Send(new CreateLoaiPhongCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Sửa loại phòng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_PHONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLoaiPhongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_PHONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_PHONG_UPDATE;
                return await _mediator.Send(new UpdateLoaiPhongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa loại phòng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_PHONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_PHONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_PHONG_DELETE;

                return await _mediator.Send(new DeleteLoaiPhongCommand(id, u.SystemLog));
            });
        }

    }
}
