﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Thêm mới nhật ký hệ thống theo danh sách
    /// </summary>
    /// <param name="model">Model thêm mới nhật ký hệ thống</param>
    /// <returns></returns>
    public class SystemLogCreateMultipleCommand : IRequest<Unit>
    {
        public SystemLogModel Model { get; set; }

        public SystemLogCreateMultipleCommand(SystemLogModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<SystemLogCreateMultipleCommand, Unit>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly IServiceScopeFactory _serviceScopeFactory;

            public Handler(IMongoDBDatabaseSettings settings, IServiceScopeFactory serviceScopeFactory)
            {
                _settings = settings;
                _serviceScopeFactory = serviceScopeFactory;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<Unit> Handle(SystemLogCreateMultipleCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                if (model.ListAction.Count == 0)
                {
                    return Unit.Value;
                }
                List<SystemLog> systemLogs = new List<SystemLog>();

                foreach (var item in model.ListAction)
                {
                    var systemLog = AutoMapperUtils.AutoMap<SystemLogModel, SystemLog>(model);
                    systemLog.Id = null;
                    systemLog.ObjectCode = item.ObjectCode;
                    systemLog.ObjectId = item.ObjectId;
                    systemLog.MetaData = item.MetaData;
                    systemLog.Description = item.Description;
                    if (!string.IsNullOrEmpty(item.ActionCode))
                    {
                        systemLog.ActionCode = item.ActionCode;
                    }
                    if (!string.IsNullOrEmpty(item.ActionName))
                    {
                        systemLog.ActionName = item.ActionName;
                    }
                    if (item.CreatedDate.HasValue)
                    {
                        systemLog.CreatedDate = item.CreatedDate.Value;
                    }

                    systemLogs.Add(systemLog);
                }

                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    await _logs.InsertManyAsync(systemLogs);
                }
                else
                {
                    var entities = AutoMapperUtils.AutoMap<SystemLog, SystemLogEntity>(systemLogs);
                    // Vì hàm này chạy sau khi hoàn thành request nên phải tạo scope mới nếu không sẽ bị lỗi DataContext is disposed
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var context = scope.ServiceProvider.GetRequiredService<SystemDataContext>();
                        context.SystemLogs.AddRange(entities);
                        await context.SaveChangesAsync();
                    }
                }

                return Unit.Value;
            }
        }
    }
}
