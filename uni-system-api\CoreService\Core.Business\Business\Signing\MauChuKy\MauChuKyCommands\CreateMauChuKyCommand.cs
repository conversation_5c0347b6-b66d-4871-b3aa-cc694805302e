﻿using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateMauChuKyCommand : IRequest<Unit>
    {
        public CreateMauChuKyModel Model { get; set; }

        /// <summary>
        /// Thêm mới mẫu chữ ký
        /// </summary>
        /// <param name="model">Thông tin mẫu chữ ký cần thêm mới</param>
        public CreateMauChuKyCommand(CreateMauChuKyModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateMauChuKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateMauChuKyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {MauChuKyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateMauChuKyModel, SgMauChuKy>(model);

                // Kiểm tra trùng lặp Code cho cùng User
                var checkCode = await _dataContext.SgMauChuKys.AnyAsync(x => x.Code == entity.Code && x.UserId == entity.UserId);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["mau-chu-ky.code.existed"]}");
                }

                entity.CreatedUserId = _contextAccessor.UserId;
                entity.CreatedDate = DateTime.Now;

                await _dataContext.SgMauChuKys.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {MauChuKyConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới mẫu chữ ký mã: {entity.Code}",
                    ObjectCode = MauChuKyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MauChuKyConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
