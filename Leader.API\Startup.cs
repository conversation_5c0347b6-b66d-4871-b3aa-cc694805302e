using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Core.Shared;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.Collections.Generic;
using System.Linq;
using Core.API.Shared;
using Core.Data;
using Microsoft.EntityFrameworkCore;
using Leader.Data;

namespace Core.API
{
    public class Startup
    {
        public IConfiguration configuration { get; }

        private const string DefaultCorsPolicyName = "Default";

        public Startup(IWebHostEnvironment env)
        {
            var builder = StartupHelpers.CreateDefaultConfigurationBuilder(env);

            if (env.IsDevelopment())
            {
                //builder.AddUserSecrets<Startup>();
            }
            configuration = builder.Build();
        }


        // This method gets called by the runtime. Use this method to add services to the container.
        // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        public void ConfigureServices(IServiceCollection services)
        {
            services.RegisterCacheComponents();

            #region Config database
            // System
            services.AddDbContext<SystemDataContext>(x =>
            {
                x.UseSqlServer(Utils.GetConfig("Database:System:ConnectionString:MSSQLDatabase"));
                x.EnableSensitiveDataLogging();
            });
            services.AddDbContext<SystemReadDataContext>(x =>
            {
                x.UseSqlServer(Utils.GetConfig("Database:System:ConnectionString:MSSQLDatabaseRead"));
                x.EnableSensitiveDataLogging();
            });

            // Leader
            services.AddDbContext<LeaderDataContext>(x =>
            {
                x.UseSqlServer(Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase"));
                x.EnableSensitiveDataLogging();
            });
            services.AddDbContext<LeaderReadDataContext>(x =>
            {
                x.UseSqlServer(Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabaseRead"));
                x.EnableSensitiveDataLogging();
            });
            #endregion Config database

            services.RegisterMongoDBDataContextServiceComponents(configuration);

            services.RegisterCustomServiceComponents();

            services.AddMvcCore();

            services.AddOptions();

            services.AddCors(options =>
            {
                options.AddPolicy(DefaultCorsPolicyName, policy =>
                {
                    policy
                        .WithOrigins(
                            configuration["AppSettings:CorsOrigins"]
                                .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                .Select(o => o.RemovePostFix("/"))
                                .ToArray()
                        )
                        .WithExposedHeaders("_UniCoreErrFormat")
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
            });

            services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.IgnoreNullValues = true;
                    options.JsonSerializerOptions.Converters.Add(new TimeSpanToStringConverter());
                });

            services.RegisterLocalizationServiceComponents();
            
            services.RegisterAPIVersionServiceComponents();

            services.AddCustomAuthenServiceComponents();

            services.RegisterSwaggerServiceComponents();

            services.RegisterCustomerModelParsingServiceComponents();

            if (Utils.GetConfig("redis:enabled") == "true")
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = Utils.GetConfig("redis:configuration");
                    options.InstanceName = Utils.GetConfig("redis:instanceName");
                });
            }

            services.AddHealthChecks();

            services.AddSignalR();

            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

            services.AddMediatR(AppDomain.CurrentDomain.GetAssemblies());

            services.AddRouting();
            services.AddHttpClient();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            if (Utils.GetConfig("AppSettings:EnableSwagger") == "true")
            {
                // Enable middleware to serve generated Swagger as a JSON endpoint.
                app.UseSwagger(c =>
                {
                    //c.SerializeAsV2 = true; //Swagger 2.0
                });

                // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
                // specifying the Swagger JSON endpoint.
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Net Core API V1");
                    c.DocExpansion(DocExpansion.None);
                    c.DisplayRequestDuration();
                    // To serve SwaggerUI at application's root page, set the RoutePrefix property to an empty string.
                    c.RoutePrefix = "";
                });
            }

            app.UseSentryTracing();
            app.UseHttpsRedirection();

            #region Localizations
            var localizeOptions = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(localizeOptions.Value);
            #endregion

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseCors(DefaultCorsPolicyName);
            
            app.UseDefaultFiles(new DefaultFilesOptions()
            {
                DefaultFileNames = new List<string>() { "index.html" },
                RequestPath = new PathString("")
            });
            app.UseStaticFiles();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<SignalRHub>("/signalr_hub");
            });

            app.UseHealthChecks("/health");//your request URL will be health
        }
    }
}
