﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepLoaiRenLuyenCommand : IRequest<Unit>
    {
        public CreateXepLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepLoaiRenLuyenCommand(CreateXepLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepLoaiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepLoaiRenLuyenModel, SvXepLoaiRenLuyen>(model);

                var checkCode = await _dataContext.SvXepLoaiRenLuyens.AnyAsync(x =>  x.XepLoai == entity.XepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoaiRenLuyen.Existed", entity.XepLoai.ToString()]}");
                }

                await _dataContext.SvXepLoaiRenLuyens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepLoaiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới xếp loại rèn luyện: {entity.XepLoai}",
                    ObjectCode = XepLoaiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepLoaiRenLuyenCommand : IRequest<Unit>
    {
        public CreateManyXepLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepLoaiRenLuyenCommand(CreateManyXepLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepLoaiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXepLoaiRenLuyenAdd = model.listXepLoaiRenLuyenModels.Select(x => x.XepLoai).ToList(); 
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiRenLuyenModel, SvXepLoaiRenLuyen>(model);

                // Check data duplicate
                if (listXepLoaiRenLuyenAdd.Count() != listXepLoaiRenLuyenAdd.Distinct().Count() )
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepLoaiRenLuyens.AnyAsync(x => listXepLoaiRenLuyenAdd.Contains(x.XepLoai)) )
                {
                    throw new ArgumentException($"{_localizer["XepLoaiRenLuyen.Existed"]}");
                }

                var listEntity = model.listXepLoaiRenLuyenModels.Select(x => new SvXepLoaiRenLuyen()
                {
                    IdXepLoai = x.IdXepLoai,
                    XepLoai = x.XepLoai,
                    XepLoaiEn = x.XepLoaiEn,
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    HeSo = x.HeSo

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepLoai).ToList();

                Log.Information($"Create many {XepLoaiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import xếp loại rèn luyện từ file excel",
                    ObjectCode = XepLoaiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepLoaiRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiRenLuyenCommand(int id, UpdateXepLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepLoaiRenLuyens.FirstOrDefaultAsync(dt => dt.IdXepLoai == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvXepLoaiRenLuyens.AnyAsync(x => x.XepLoai == model.XepLoai && x.IdXepLoai != model.IdXepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoaiRenLuyen.Existed", model.XepLoai.ToString()]}");
                }

                Log.Information($"Before Update {XepLoaiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepLoaiRenLuyens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật xếp loại rèn luyện: {entity.XepLoai}",
                    ObjectCode = XepLoaiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey(entity.IdXepLoai.ToString()));
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiRenLuyenCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepLoaiRenLuyens.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                _dataContext.SvXepLoaiRenLuyens.Remove(entity);

                Log.Information($"Delete {XepLoaiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa xếp loại rèn luyện: {entity.XepLoai}",
                    ObjectCode = XepLoaiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey());
                _cacheService.Remove(XepLoaiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
