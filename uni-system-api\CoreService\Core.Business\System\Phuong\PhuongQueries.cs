﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxPhuongQuery : IRequest<List<PhuongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy phường cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxPhuongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxPhuongQuery, List<PhuongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<PhuongSelectItemModel>> Handle(GetComboboxPhuongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = PhuongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvPhuongs.OrderBy(x => x.TenPhuong)
                                select new PhuongSelectItemModel()
                                {
                                    IdPhuong = dt.IdPhuong,
                                    TenPhuong = dt.TenPhuong,
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenPhuong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterPhuongQuery : IRequest<PaginationList<PhuongBaseModel>>
    {
        public PhuongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách phường có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterPhuongQuery(PhuongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterPhuongQuery, PaginationList<PhuongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<PhuongBaseModel>> Handle(GetFilterPhuongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvPhuongs
                            select new PhuongBaseModel
                            {
                                IdPhuong = dt.IdPhuong,
                                TenPhuong = dt.TenPhuong,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenPhuong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<PhuongBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetPhuongByIdQuery : IRequest<PhuongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin phường theo id
        /// </summary>
        /// <param name="id">Id phường</param>
        public GetPhuongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetPhuongByIdQuery, PhuongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<PhuongModel> Handle(GetPhuongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = PhuongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvPhuongs.FirstOrDefaultAsync(x => x.IdPhuong == id);

                    return AutoMapperUtils.AutoMap<SvPhuong, PhuongModel>(entity);
                });
                return item;
            }
        }
    }
}
