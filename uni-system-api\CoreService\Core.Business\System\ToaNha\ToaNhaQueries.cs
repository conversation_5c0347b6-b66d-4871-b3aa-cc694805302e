﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxToaNhaQuery : IRequest<List<ToaNhaSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tòa nhà cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxToaNhaQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxToaNhaQuery, List<ToaNhaSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ToaNhaSelectItemModel>> Handle(GetComboboxToaNhaQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ToaNhaConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbToaNhas.OrderBy(x => x.TenNha)
                                select new ToaNhaSelectItemModel()
                                {
                                    IdNha = dt.IdNha,
                                    MaNha = dt.MaNha,
                                    TenNha = dt.TenNha
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenNha.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterToaNhaQuery : IRequest<PaginationList<ToaNhaBaseModel>>
    {
        public ToaNhaFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tòa nhà có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterToaNhaQuery(ToaNhaFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterToaNhaQuery, PaginationList<ToaNhaBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ToaNhaBaseModel>> Handle(GetFilterToaNhaQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbToaNhas
                            join cs in _dataContext.TkbCoSoDaoTaos on dt.IdCoSo equals cs.IdCoSo
                            select new ToaNhaBaseModel
                            {
                                IdNha = dt.IdNha,
                                MaNha = dt.MaNha,
                                TenNha = dt.TenNha,
                                IdCoSo = dt.IdCoSo,
                                TenCoSo = cs.TenCoSo

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenNha.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ToaNhaBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetToaNhaByIdQuery : IRequest<ToaNhaModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin tòa nhà theo id
        /// </summary>
        /// <param name="id">Id tòa nhà</param>
        public GetToaNhaByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetToaNhaByIdQuery, ToaNhaModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ToaNhaModel> Handle(GetToaNhaByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ToaNhaConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbToaNhas.FirstOrDefaultAsync(x => x.IdNha == id);

                    return AutoMapperUtils.AutoMap<TkbToaNha, ToaNhaModel>(entity);
                });
                return item;
            }
        }
    }
}
