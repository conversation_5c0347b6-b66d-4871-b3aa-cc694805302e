﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.System
{
    public class PhanHeModel
    {
        public int IdPh { get; set; }
        public string PhanHe { get; set; }
        public string MieuTa { get; set; }
        public string Url { get; set; }
        public string <PERSON>yHieuPhanHe { get; set; }
        public string MaPhanHe { get; set; }
        public string HinhAnhPhanHe { get; set; }
        public bool Active { get; set; }
        public int STT { get; set; }
    }
    public class PhanHeSelectItemModel
    {
        public int IdPh { get; set; }
        public string PhanHe { get; set; }
        public string MieuTa { get; set; }
        public string Url { get; set; }
        public string KyHieuPhanHe { get; set; }
        public string <PERSON>PhanHe { get; set; }
        public string HinhAnhPhanHe { get; set; }
        public bool Active { get; set; }
        public int STT { get; set; }
    }
}
