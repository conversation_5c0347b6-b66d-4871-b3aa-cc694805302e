# Note

### Đ<PERSON>ng nhập

```bash
docker login -u username -p password docker.io
```

### Build và push leader api

```bash
docker build -t thienanunisoft/leader-api:latest . -f Dockerfile/dockerfile_leader_api
docker push thienanunisoft/leader-api:latest
```

### Pull và run leader-api

```bash
docker pull thienanunisoft/leader-api
docker run -d --restart unless-stopped --name leader-api -p 8006:80 thienanunisoft/leader-api
```

## Danh sách các site

- API Gateway - Ocelot
	- http://localhost:8000/
- HRM API
	- http://localhost:8001/
	- Prometheus: http://localhost:1234/

## SubModule

**Lưu ý:**
- Khi bạn clone project này về, bạn cần phải chạy lệnh sau để clone các submodule về
```bash
git submodule update --init
git submodule update --recursive --remote
git pull --recurse-submodules
```

- Không code trực tiếp vào các submodule mà phải code vào project chính rồi các project import submodule sẽ pull về và sử dụng sau

- Hướng dẫn sử dụng git submodule tại đây 
```code
https://git-scm.com/book/en/v2/Git-Tools-Submodules
https://topdev.vn/blog/git-submodules-va-ung-dung-trong-viec-chia-se-tai-nguyen-dung-chung
```

- Sử dụng git submodule để quản lý các module con 
```bash
git submodule add https://gitlab.unisoft.edu.vn/thien-an-group/uni-system-api.git uni-system-api
```

- Khi mà bạn git clone project của bạn trên 1 máy khác, thì nó sẽ không tự động clone các submodule đã add xuống theo mà bạn cần phải chạy update và pull các submodule về.
```bash
git submodule update --init
git submodule update --recursive --remote
git pull --recurse-submodules
```

- Khi submodule đó không sử dụng, hoặc bị lỗi cần add lại thì ta phải remove như thế nào? Chạy các lệnh sau đây theo trình tự để remove submodule
```bash
git submodule deinit uni-system-api -f
git rm uni-system-api
git commit -m "Remove submodule" .
rm -rf .git/modules/uni-system-api
```

 
## Danh sách các tính năng của core
- [x] Phân chia rõ ràng tầng Data - Bussiness - Controller
- [x] Hỗ trợ docker
- [x] Đa ngôn ngữ - tính năng - data annotation
- [ ] Multi tenant
- [x] Api version
- [x] Swagger 
- [x] Tích hợp elastic search
- [ ] SignalR
- [ ] Multi database
- [ ] Multi object storage
- [x] Tích hợp RabbitMQ - Distributed event bus
- [x] Micro service
- [x] Object storage: MinIO - AWS S3
- [x] Authentication: WSO2 - JWT - Keycloak
- [x] CI-CD: gitlab
- [x] Cache: Redis - MemoryCache
- [x] Redis: Redis cache - Redis distributed lock
- [x] Database: Postgres - SQL Server
- [x] Logging: Serilog - Seq
- [ ] Monitoring: Prometheus - Grafana
- [x] Message queue: RabbitMQ
- [x] Sentry
- [ ] Distributed tracing: Jaeger
- [ ] Distributed lock: Redis distributed lock
- [ ] Distributed cache: Redis cache
- [ ] Distributed event bus: RabbitMQ
- [ ] Distributed file storage: MinIO - AWS S3
- [ ] Distributed database: Postgres - SQL Server
- [ ] Distributed authentication: WSO2 - JWT - Keycloak
- [ ] Distributed configuration: Consul
- [ ] Distributed communication: gRPC
- [ ] Distributed API gateway: Ocelot
- [ ] Distributed API documentation: Swagger
- [ ] Distributed API client: Refit
- [ ] Distributed API: ASP.NET Core
- [ ] Distributed API: ASP.NET Web API
- [ ] Distributed API: ASP.NET MVC
- [ ] Distributed API: ASP.NET Web Forms
- [ ] Distributed API: ASP.NET Web Pages
- [ ] Distributed API: ASP.NET SignalR
- [ ] Distributed API: ASP.NET Web Sockets
- [ ] HA with NginX
- [x] Cache 

- Tự động tăng số không trùng lặp sử dụng redis vì redis chạy dữ liệu đơn luồng, truy suất nhanh => đảm bảo không bị trùng lặp dữ liệu khi tăng dần => cách sử dụng tương tự cache

## Build docker
- docker build -t econtract-api:dev .
- docker run -d -p 30200:80 -p 30201:1234 --name econtract-api econtract-api:dev
- docker run -d -p 30200:80 --name econtract-api -e ASPNETCORE_ENVIRONMENT=Development econtract-api:dev
- docker push econtract-api:dev

## Docker compose
- docker-compose up -d
- docker-compose down
- docker-compose up -d --build
- docker-compose up -d --build --force-recreate

## Docker compose with dockerfile
- docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d --build
- docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d --build --force-recreate

## Migration
- dotnet ef migrations add init_data -Context HRMDataContext
- dotnet ef database update --context HRMDataContext
- dotnet ef migrations add init_data -Context HRMDataContext -o Data/Migrations
- dotnet ef database update --context HRMDataContext
- dotnet ef migrations script FromA FromB -o sample.sql

## CI/CD
- https://docs.gitlab.com/ee/ci/yaml/README.html

## Grafana
- https://grafana.com/docs/grafana/latest/installation/docker/

## Kestrel
- https://docs.microsoft.com/en-us/aspnet/core/fundamentals/servers/kestrel?view=aspnetcore-5.0

## Health check API
curl --location --request GET 'http://localhost:30200/health'

## Tạo model từ DB có sẵn
Scaffold-DbContext "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir Systems/Context/All

## Hướng dẫn chạy migration
- Vì có nhiều DataContext cho datacontext đọc và ghi nên cần chỉ định đúng datacontext ghi để chạy migration
- add-migration Init -Context LeaderDataContext
- update-database -Context LeaderDataContext
- Script-Migration 20231007034042_init -Context LeaderDataContext

- dotnet ef migrations add init_data -Context LeaderDataContext
- dotnet ef database update --context LeaderDataContext

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

