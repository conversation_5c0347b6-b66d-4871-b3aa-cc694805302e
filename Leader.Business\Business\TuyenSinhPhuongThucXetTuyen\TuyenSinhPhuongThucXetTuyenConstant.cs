﻿using Core.Shared;
using <PERSON>.Shared;

namespace Leader.Business
{
    public class TuyenSinhPhuongThucXetTuyenConstant
    {
        public const string CachePrefix = LeaderCacheConstants.TUYEN_SINH_PHUONG_THUC_XET_TUYEN;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
