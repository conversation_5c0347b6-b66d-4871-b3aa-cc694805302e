﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using System.Threading.Tasks;
using System;

namespace Core.API.Shared
{
    public class RouteDataRequestCultureProvider : RequestCultureProvider
    {
        public override Task<ProviderCultureResult> DetermineProviderCultureResult(HttpContext httpContext)
        {
            if (httpContext == null)
                throw new ArgumentNullException(nameof(httpContext));

            // Lấy thông tin cấu hình đa ngôn ngữ từ header (hoặc có thể lấy từ cấu hình chỗ khác, tùy thuộc cấu hình)
            string culture = httpContext.Request.Headers["x-language"];

            var providerResultCulture = new ProviderCultureResult(culture);

            return Task.FromResult(providerResultCulture);
        }
    }
}
