﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class ActionDetail
    {
        public ActionDetail()
        {
        }
        public ActionDetail(string description)
        {
            this.Description = description;
        }
        public ActionDetail(string objectCode, string objectId, string description)
        {
            this.ObjectCode = objectCode;
            this.ObjectId = objectId;
            this.Description = description;
        }
        public ActionDetail(string objectCode, string objectId, string description, string metaData)
        {
            this.ObjectCode = objectCode;
            this.ObjectId = objectId;
            this.Description = description;
            this.MetaData = metaData;
        }
        public string ActionCode { get; set; }
        public string ActionName { get; set; }
        public string ObjectCode { get; set; }
        public string ObjectId { get; set; }
        public string Description { get; set; }
        public string MetaData { get; set; }
        public string UserId { get; set; }
        public DateTime? CreatedDate { get; set; } = DateTime.Now;
    }
}
