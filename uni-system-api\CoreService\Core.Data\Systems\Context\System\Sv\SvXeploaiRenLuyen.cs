﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepLoaiRenLuyen")]
    public class SvXepLoaiRenLuyen
    {

        public SvXepLoaiRenLuyen()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai")]
        public int IdXepLoai { get; set; }

        [Column("xep_loai"), MaxLength(50)]
        public string XepLoai { get; set; }

        [Column("xep_loai_en"), MaxLength(50)]
        public string XepLoaiEn { get; set; }

        [Column("Tu_diem")]
        public int TuDiem { get; set; }

        [Column("Den_diem")]
        public int DenDiem { get; set; }


        [Column("He_so")]
        public float HeS<PERSON> { get; set; }




    }
}
