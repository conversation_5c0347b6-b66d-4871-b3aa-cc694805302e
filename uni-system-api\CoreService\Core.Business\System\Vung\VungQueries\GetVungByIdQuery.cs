﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetVungByIdQuery : IRequest<VungModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin vùng theo id
        /// </summary>
        /// <param name="id">Id vùng</param>
        public GetVungByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetVungByIdQuery, VungModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<VungModel> Handle(GetVungByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = VungConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvVungs.FirstOrDefaultAsync(x => x.IdVung == id);

                    return AutoMapperUtils.AutoMap<SvVung, VungModel>(entity);
                });
                return item;
            }
        }
    }
}
