﻿using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Leader.Data;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class GetKhBacDaoTaoByIdQuery : IRequest<KhBacDaoTaoModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// L<PERSON>y thông tin bậc đào tạo khoa học theo id
        /// </summary>
        /// <param name="id">Id bậc đào tạo khoa học</param>
        public GetKhBacDaoTaoByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetKhBacDaoTaoByIdQuery, KhBacDaoTaoModel>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<KhBacDaoTaoModel> Handle(GetKhBacDaoTaoByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = KhBacDaoTaoConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.KhBacDaoTaos.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<KhBacDaoTao, KhBacDaoTaoModel>(entity);
                });
                return item;
            }
        }
    }
}
