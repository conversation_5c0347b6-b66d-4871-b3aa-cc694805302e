﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Leader.Business;
using Microsoft.Extensions.Configuration;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("leader/v1/seed-data")]
    [ApiExplorerSettings(GroupName = "101. Seed Data", IgnoreApi = false)]
    [AllowAnonymous]
    public class SeedDataController : ApiControllerBase
    {
        public SeedDataController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// SeedDataAsync
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("seed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SeedDataAsync()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SeedDataCommand(u.SystemLog));
            });
        }

        /// <summary>
        /// RunMigrationsAsync
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("run-migrations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> RunMigrationsAsync()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new RunLeaderMigrationsCommand());
            });
        }
    }
}
