﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateMauChuKyCommand : IRequest<Unit>
    {
        public UpdateMauChuKyModel Model { get; set; }

        /// <summary>
        /// Cập nhật mẫu chữ ký
        /// </summary>
        /// <param name="model">Thông tin mẫu chữ ký cần cập nhật</param>
        public UpdateMauChuKyCommand(UpdateMauChuKyModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateMauChuKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateMauChuKyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {MauChuKyConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SgMauChuKys.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {MauChuKyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);
                
                entity.ModifiedUserId = _contextAccessor.UserId;
                _dataContext.SgMauChuKys.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {MauChuKyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(MauChuKyConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(MauChuKyConstant.BuildCacheKey());

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật mẫu chữ ký mã: {entity.Code}",
                    ObjectCode = MauChuKyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
