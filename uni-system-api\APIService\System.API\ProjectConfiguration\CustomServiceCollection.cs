using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace Core.API
{
    public static class CustomServiceCollection
    {
        /// <summary>
        /// RegisterCustomService
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCustomServiceComponents(this IServiceCollection services)
        {
            Core.Business.ServiceConfigure.Configure(services);

            services.AddSingleton(Log.Logger);

            return services;
        }
    }
}