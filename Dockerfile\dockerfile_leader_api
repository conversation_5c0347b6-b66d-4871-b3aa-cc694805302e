# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
RUN  ["rm", "-rf", "/etc/localtime"]
RUN  ["ln", "-s", "/usr/share/zoneinfo/Asia/Ho_Chi_Minh", "/etc/localtime"]

USER app
WORKDIR /app

EXPOSE 80
ENV ASPNETCORE_URLS=http://+:80
# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["APIService/Leader.API/Leader.API.csproj", "APIService/Leader.API/"]
COPY ["Leader.Business/Leader.Business.csproj", "Leader.Business/"]
COPY ["Leader.Data/Leader.Data.csproj", "Leader.Data/"]
COPY ["uni-system-api/CoreService/Core.Data/Core.Data.csproj", "uni-system-api/CoreService/Core.Data/"]
COPY ["uni-system-api/CoreService/Core.Shared/Core.Shared.csproj", "uni-system-api/CoreService/Core.Shared/"]
COPY ["Leader.Shared/Leader.Shared.csproj", "Leader.Shared/"]
COPY ["uni-system-api/CoreService/Core.Business/Core.Business.csproj", "uni-system-api/CoreService/Core.Business/"]
COPY ["uni-system-api/CoreService/Core.DataLog/Core.DataLog.csproj", "uni-system-api/CoreService/Core.DataLog/"]
COPY ["uni-system-api/APIService/Core.API.Shared/Core.API.Shared.csproj", "uni-system-api/APIService/Core.API.Shared/"]
RUN dotnet restore "./APIService/Leader.API/Leader.API.csproj"
COPY . .
WORKDIR "/src/APIService/Leader.API"
RUN dotnet build "./Leader.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Leader.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Leader.API.dll"]