﻿using System.Threading.Tasks;
using Core.Data;

namespace Core.Business
{
    public interface IAuthHandler
    {
        /// <summary>
        /// <PERSON><PERSON><PERSON> thực thông tin người dùng bằng tài khoản và mật khẩu
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<LoginResponseModel> Login(LoginRequestModel model);

        /// <summary>
        /// Lấy thông tin người dùng theo ID
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<UserResponseModel> GetUserInfoById(int userId);

        /// <summary>
        /// Lấy thông tin người dùng theo tên đăng nhập
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<UserResponseModel> GetUserInfoByUserName(string userName);

        /// <summary>
        /// Lấy thông tin người dùng theo email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<UserResponseModel> FindByExternalEmailAsync(string email, string issuer);

        /// <summary>
        /// Tạo người dùng từ thông tin người dùng từ bên ngoài
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<UserResponseModel> CreateExternalUserAsync(UserCreateModel user);
    }
}
