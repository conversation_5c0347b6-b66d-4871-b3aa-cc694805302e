﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chuyen-nganh")]
    [ApiExplorerSettings(GroupName = "23. Chuyên ngành")]
    [Authorize]
    public class ChuyenNganhController : ApiControllerBase
    {
        public ChuyenNganhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách chuyên ngành cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuyenNganhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxChuyenNganhQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách chuyên ngành có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChuyenNganhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ChuyenNganhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterChuyenNganhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết chuyên ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ChuyenNganhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChuyenNganhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới chuyên ngành
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateChuyenNganhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUYEN_NGANH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUYEN_NGANH_CREATE;


                return await _mediator.Send(new CreateChuyenNganhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel chuyên ngành
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyChuyenNganhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUYEN_NGANH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUYEN_NGANH_CREATE_MANY;


                return await _mediator.Send(new CreateManyChuyenNganhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa chuyên ngành
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateChuyenNganhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUYEN_NGANH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUYEN_NGANH_UPDATE;
                return await _mediator.Send(new UpdateChuyenNganhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chuyên ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUYEN_NGANH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUYEN_NGANH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUYEN_NGANH_DELETE;

                return await _mediator.Send(new DeleteChuyenNganhCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách chuyên ngành thô
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("list-chuyen-nganh-map-he-khoa")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuyenNganhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListRaw(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetListRawChuyenNganhQuery(count, ts));
            });
        }
    }
}
