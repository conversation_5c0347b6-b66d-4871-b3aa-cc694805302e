﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/toa-nha")]
    [ApiExplorerSettings(GroupName = "45. Tòa nhà")]
    [Authorize]
    public class ToaNhaController : ApiControllerBase
    {
        public ToaNhaController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách tòa nhà cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ToaNhaSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxToaNhaQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách tòa nhà có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ToaNhaBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ToaNhaFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterToaNhaQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết tòa nhà
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ToaNhaModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetToaNhaByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới tòa nhà
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateToaNhaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TOA_NHA_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TOA_NHA_CREATE;


                return await _mediator.Send(new CreateToaNhaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel tòa nhà
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyToaNhaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TOA_NHA_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TOA_NHA_CREATE_MANY;


                return await _mediator.Send(new CreateManyToaNhaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa tòa nhà
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateToaNhaModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TOA_NHA_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TOA_NHA_UPDATE;
                return await _mediator.Send(new UpdateToaNhaCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa tòa nhà
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TOA_NHA_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TOA_NHA_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TOA_NHA_DELETE;

                return await _mediator.Send(new DeleteToaNhaCommand(id, u.SystemLog));
            });
        }

    }
}
