﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/nhom-doi-tuong")]
    [ApiExplorerSettings(GroupName = "26. Nhóm đối tượng")]
    [Authorize]
    public class NhomDoiTuongController : ApiControllerBase
    {
        public NhomDoiTuongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách nhóm đối tượng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NhomDoiTuongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNhomDoiTuongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách nhóm đối tượng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<NhomDoiTuongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] NhomDoiTuongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterNhomDoiTuongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết nhóm đối tượng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<NhomDoiTuongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNhomDoiTuongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới nhóm đối tượng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateNhomDoiTuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_DOI_TUONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_DOI_TUONG_CREATE;


                return await _mediator.Send(new CreateNhomDoiTuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel nhóm đối tượng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyNhomDoiTuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_DOI_TUONG_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_DOI_TUONG_CREATE_MANY;


                return await _mediator.Send(new CreateManyNhomDoiTuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa nhóm đối tượng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateNhomDoiTuongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_DOI_TUONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_DOI_TUONG_UPDATE;
                return await _mediator.Send(new UpdateNhomDoiTuongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa nhóm đối tượng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_DOI_TUONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_DOI_TUONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_DOI_TUONG_DELETE;

                return await _mediator.Send(new DeleteNhomDoiTuongCommand(id, u.SystemLog));
            });
        }

    }
}
