﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.Core.LockDynamic
{
    public class LockDynamicModel
    {
        public string ServiceName { get; set; } = "LockDynamic";
        public string Key { get; set; } = "Key";
        public int LockInSeconds { get; set; } = 300;
        public int TimesToFail { get; set; } = 5;
        public bool IsIgnoreCheckCache { get; set; } = false;
    }

    public class LockDynamicCacheModel
    {
        public string Key { get; set; }
        public int Times { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
