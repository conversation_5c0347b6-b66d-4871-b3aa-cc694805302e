﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CoreService\Core.Business\Core.Business.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.DataLog\Core.DataLog.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.Data\Core.Data.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="IdentityModel" Version="6.2.0" />
    <PackageReference Include="NCrontab.Signed" Version="3.3.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="UAParser" Version="3.1.47" />
    <PackageReference Include="VaultSharp" Version="1.13.0.1" />
  </ItemGroup>
</Project>