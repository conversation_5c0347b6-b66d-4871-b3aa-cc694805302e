﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class NhomDoiTuongSelectItemModel
    {
        public int IdNhomDoiTuong { get; set; }
        public string MaNhom { get; set; }
        public string TenNhom{ get; set; }
    }

    public class NhomDoiTuongBaseModel
    {
        public int IdNhomDoiTuong { get; set; }
        public string MaNhom { get; set; }
        public string TenNhom { get; set; }
        public string TenNhomEn { get; set; }
    }


    public class NhomDoiTuongModel : NhomDoiTuongBaseModel
    {

    }

    public class NhomDoiTuongFilterModel : BaseQueryFilterModel
    {
        public NhomDoiTuongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNhomDoiTuong";
        }
    }

    public class CreateNhomDoiTuongModel
    {
        [Required(ErrorMessage = "NhomDoiTuong.IdNhomDoiTuong.NotRequire")]
        public int IdNhomDoiTuong { get; set; }

        [MaxLength(5, ErrorMessage = "NhomDoiTuong.MaNhom.MaxLength(5)")]
        public string MaNhom{ get; set; }

        [MaxLength(50, ErrorMessage = "NhomDoiTuong.TenNhom.MaxLength(100)")]
        [Required(ErrorMessage = "NhomDoiTuong.TenNhom.NotRequire")]
        public string TenNhom { get; set; }

        [MaxLength(50, ErrorMessage = "NhomDoiTuong.TenNhomEn.MaxLength(50)")]
        public string TenNhomEn { get; set; }

    }

    public class CreateManyNhomDoiTuongModel
    {
        public List<CreateNhomDoiTuongModel> listNhomDoiTuongModels { get; set; }
    }

    public class UpdateNhomDoiTuongModel : CreateNhomDoiTuongModel
    {
        public void UpdateEntity(SvNhomDoiTuong input)
        {
            input.IdNhomDoiTuong = IdNhomDoiTuong;
            input.MaNhom = MaNhom;
            input.TenNhom = TenNhom;
            input.TenNhomEn = TenNhomEn;

        }
    }
}
