﻿using Core.Shared;
using System;
using System.Data;

namespace Leader.Business
{
    public interface ILeaderCallStoreHelper
    {
        DataTable CallStoreKhoanThuAsync(string idLopList, string hocKyList, string namHoc);
        DataTable CallStoreKhoanThuByConditionsAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, DateTime? tuNgay, DateTime? denNgay);
        DataTable CallStoreDanhSachLopTinChiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot);
        DataTable CallStoreSoLuongLopTinChiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot);
        DataTable CallStoreThongKeToChucThiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot);
        DataTable CallStoreQuyMoSinhVienAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc);
        DataTable CallStoreThongKeNhapHocAsync(int idHe, int namHoc, int idNganh);
    }
}
