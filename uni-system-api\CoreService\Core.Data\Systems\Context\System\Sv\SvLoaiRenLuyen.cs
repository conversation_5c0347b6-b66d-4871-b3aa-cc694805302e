﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiRenLuyen")]
    public class SvLoaiRenLuyen
    {

        public SvLoaiRenLuyen()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_loai_rl")]
        public int IdLoaiRenLuyen { get; set; }

        [Column("ID_cap_rl")]
        public int IdCapRenLuyen { get; set; }

        [<PERSON>umn("Ky_hieu"), MaxLength(10)]
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }

        [Column("Ten_loai"), MaxLength(200)]
        public string TenLoai { get; set; }

        [Column("Diem")]
        public int Diem { get; set; }

        [Column("Diem_tru")]
        public bool DiemTru { get; set; }

        [Column("Hoc_tap")]
        public bool HocTap { get; set; }

        [Column("Tinh_diem")]
        public bool TinhDiem { get; set; }

        [Column("Hien_thi")]
        public bool HienThi { get; set; }



    }
}
