﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HocViSelectItemModel
    {
        public int IdHocVi { get; set; }
        public string MaHocVi { get; set; }
        public string HocVi { get; set; }
        public decimal HeSoBuKhoaHoc { get; set; }
    }

    public class HocViBaseModel : HocViSelectItemModel
    {

    }


    public class HocViModel : HocViSelectItemModel
    {

    }

    public class HocViFilterModel : BaseQueryFilterModel
    {
        public HocViFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHocVi";
        }
    }

    public class CreateHocViModel
    {
        [Required(ErrorMessage = "HocVi.IdHocVi.NotRequire")]
        public int IdHocVi { get; set; }

        [MaxLength(10, ErrorMessage = "HocVi.MaHocVi.MaxLength(10)")]
        [Required(ErrorMessage = "HocVi.MaHocVi.NotRequire")]
        public string MaHocVi { get; set; }

        [MaxLength(100, ErrorMessage = "HocVi.HocVi.MaxLength(100)")]
        [Required(ErrorMessage = "HocVi.HocVi.NotRequire")]
        public string HocVi { get; set; }
     
        public decimal HeSoBuKhoaHoc { get; set; }

    }

    public class CreateManyHocViModel
    {
        public List<CreateHocViModel> listHocViModels { get; set; }
    }

    public class UpdateHocViModel : CreateHocViModel
    {
        public void UpdateEntity(TkbHocVi input)
        {
            input.IdHocVi = IdHocVi;
            input.MaHocVi = MaHocVi;
            input.HocVi = HocVi;
            input.HeSoBuKhoaHoc = HeSoBuKhoaHoc;

        }
    }
}
