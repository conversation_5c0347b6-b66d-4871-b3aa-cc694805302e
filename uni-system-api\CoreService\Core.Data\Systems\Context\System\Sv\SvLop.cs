﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLop")]
    public class SvLop
    {
        public SvLop()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_lop")]
        public int IdLop { get; set; }

        [Column("Ten_lop"), MaxLength(50)]
        public string TenLop { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_khoa")]
        public int IdKhoa { get; set; }

        [Column("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [Column("Khoa_hoc")]
        public int KhoaHoc { get; set; }

        [Column("Nien_khoa"), <PERSON><PERSON><PERSON><PERSON>(15)]
        public string NienKhoa { get; set; }

        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("So_sv")]
        public int SoSv { get; set; }

        [Column("Ra_truong")]
        public bool RaTruong { get; set; }

        [Column("ID_phong")]
        public int? IdPhong { get; set; }

        [Column("Ca_hoc")]
        public int? CaHoc { get; set; }

        public void UpdateLopHoc( int khoaHoc, int idHe, int idKhoa, int idChuyenNganh, string nienKhoa, string tenLop, int soSinhVien)
        {
            KhoaHoc = khoaHoc;
            IdHe = idHe;
            IdKhoa = idKhoa;
            IdChuyenNganh = idChuyenNganh;
            NienKhoa = nienKhoa;
            TenLop = tenLop;
            SoSv = soSinhVien;
        }

        public void UpdateSoSV(int soSinhVien)
        {
            SoSv = soSinhVien;
        }

        public void UpdateRatruong(bool raTruong)
        {
            RaTruong = raTruong;
        }
    }
}
