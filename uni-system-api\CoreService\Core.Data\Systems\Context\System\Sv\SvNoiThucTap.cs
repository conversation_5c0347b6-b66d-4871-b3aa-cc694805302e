﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svNoiThucTap")]
    public class SvNoiThucTap
    {

        public SvNoiThucTap()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_noi_thuc_tap")]
        public int IdNoiThucTap { get; set; }

        [Column("Ma_noi_thuc_tap"), MaxLength(20)]
        public string <PERSON>NoiThucTap { get; set; }

        [Column("noi_thuc_tap"), Max<PERSON>ength(200)]
        public string NoiThucTap { get; set; }

        [Column("Dia_chi_thuc_tap"), MaxLength(250)]
        public string DiaChithucTap { get; set; }


    }
}
