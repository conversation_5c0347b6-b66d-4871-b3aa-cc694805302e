﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-hang-tot-nghiep-thang-diem-10")]
    [ApiExplorerSettings(GroupName = "72. Xếp hạng tốt nghiệp thang điểm 10")]
    [Authorize]
    public class XepHangTotNghiepThangDiem10Controller : ApiControllerBase
    {
        public XepHangTotNghiepThangDiem10Controller(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON><PERSON> danh sách Xếp hạng tốt nghiệp thang điểm 10 cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepHangTotNghiepThangDiem10SelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepHangTotNghiepThangDiem10Query(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Xếp hạng tốt nghiệp thang điểm 10 có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepHangTotNghiepThangDiem10BaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepHangTotNghiepThangDiem10FilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepHangTotNghiepThangDiem10Query(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Xếp hạng tốt nghiệp thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepHangTotNghiepThangDiem10Model>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepHangTotNghiepThangDiem10ByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Xếp hạng tốt nghiệp thang điểm 10
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepHangTotNghiepThangDiem10Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE;


                return await _mediator.Send(new CreateXepHangTotNghiepThangDiem10Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Xếp hạng tốt nghiệp thang điểm 10
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepHangTotNghiepThangDiem10Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepHangTotNghiepThangDiem10Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Xếp hạng tốt nghiệp thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepHangTotNghiepThangDiem10Model request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_UPDATE;
                return await _mediator.Send(new UpdateXepHangTotNghiepThangDiem10Command(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Xếp hạng tốt nghiệp thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_DELETE;

                return await _mediator.Send(new DeleteXepHangTotNghiepThangDiem10Command(id, u.SystemLog));
            });
        }

    }
}
