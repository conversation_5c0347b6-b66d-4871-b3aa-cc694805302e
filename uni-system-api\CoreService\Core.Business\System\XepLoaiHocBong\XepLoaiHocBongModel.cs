﻿using Core.Data;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;


namespace Core.Business
{
    public class XepLoaiHocBongSelectItemModel
    {
        public int IdXepLoaiHb { get; set; }

        public string TenXepLoai { get; set; }

        public string MaXepLoai { get; set; }

        public int IdHe { get; set; }
    }

    public class XepLoaiHocBongBaseModel
    {
        public int IdXepLoaiHb { get; set; }

        public string TenXepLoai { get; set; }

        public float TuDiemHt { get; set; }

        public float TuDiemRl { get; set; }

        public int IdHe { get; set; }

        public string TenHe { get; set; }

        public float TuDiemHt4 { get; set; }

        public string MaXepLoai { get; set; }

        public float SoTien { get; set; }
    }


    public class XepLoaiHocBongModel : XepLoaiHocBongBaseModel
    {

    }

    public class XepLoaiHocBongFilterModel : BaseQueryFilterModel
    {
        public XepLoaiHocBongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoaiHb";
        }
    }

    public class CreateXepLoaiHocBongModel
    {
        [Required(ErrorMessage = "XepLoaiHocBong.IdXepLoaiHb.NotRequire")]
        public int IdXepLoaiHb { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiHocBong.TenXepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiHocBong.TenXepLoai.NotRequire")]
        public string TenXepLoai { get; set; }

        [Required(ErrorMessage = "XepLoaiHocBong.TuDiemHt.NotRequire")]
        public float TuDiemHt { get; set; }

        [Required(ErrorMessage = "XepLoaiHocBong.TuDiemRl.NotRequire")]
        public float TuDiemRl { get; set; }

        [Required(ErrorMessage = "XepLoaiHocBong.IdHe.NotRequire")]
        public int IdHe { get; set; }

        [Required(ErrorMessage = "XepLoaiHocBong.TuDiemHt4.NotRequire")]
        public float TuDiemHt4 { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiHocBong.MaXepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiHocBong.MaXepLoai.NotRequire")]
        public string MaXepLoai { get; set; }

        [Required(ErrorMessage = "XepLoaiHocBong.SoTien.NotRequire")]
        public float SoTien { get; set; }

    }

    public class CreateManyXepLoaiHocBongModel
    {
        public List<CreateXepLoaiHocBongModel> listXepLoaiHocBongModels { get; set; }
    }

    public class UpdateXepLoaiHocBongModel : CreateXepLoaiHocBongModel
    {
        public void UpdateEntity(SvXepLoaiHocBong input)
        {
            input.IdXepLoaiHb = IdXepLoaiHb;
            input.TenXepLoai = TenXepLoai;
            input.TuDiemHt = TuDiemHt;
            input.TuDiemRl = TuDiemRl;
            input.IdHe = IdHe;
            input.TuDiemHt4 = TuDiemHt4;
            input.MaXepLoai = MaXepLoai;
            input.SoTien = SoTien;
        }
    }
}
