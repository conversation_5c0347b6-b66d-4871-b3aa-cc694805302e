﻿using Core.API.Shared;
using Core.Business;
using Core.Business.System;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/tham-so-he-thong")]
    [ApiExplorerSettings(GroupName = "14. Tham số hệ thống")]
    [Authorize]
    public class ThamSoHeThongController : ApiControllerBaseV2
    {
        public ThamSoHeThongController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới tham số hệ thống
        /// </summary>
        /// <param name="model">Thông tin tham số hệ thống</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateThamSoHeThongModel model)
        {
            return await ExecuteFunction(async () =>
            {
                //_contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_THAM_SO_HE_THONG_CREATE);
                //_contextAccessor.SystemLog.ActionName = LogConstants.ACTION_THAM_SO_HE_THONG_CREATE;

                return await _mediator.Send(new CreateThamSoHeThongCommand(model));
            });
        }

        /// <summary>
        /// Lấy tham số hệ thống theo ID
        /// </summary>
        /// <param name="id">ID tham số hệ thống</param>
        /// <returns></returns>
        [HttpGet, Route("{idPh}/{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<ThamSoHeThongModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromRoute] string id, [FromRoute] int idPh)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetThamSoHeThongByIdQuery(id, idPh));
            });
        }

        /// <summary>
        /// Cập nhật tham số hệ thống
        /// </summary>
        /// <param name="model">Thông tin tham số hệ thống cần cập nhật</param>
        /// <param name="id">ID tham số hệ thống</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] string id, [FromBody] UpdateThamSoHeThongModel model)
        {
            return await ExecuteFunction(async () =>
            {
                //_contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_THAM_SO_HE_THONG_UPDATE);
                //_contextAccessor.SystemLog.ActionName = LogConstants.ACTION_THAM_SO_HE_THONG_UPDATE;

                model.IdThamSo = id; // Ensure the ID matches the route parameter
                return await _mediator.Send(new UpdateThamSoHeThongCommand(model));
            });
        }

        /// <summary>
        /// Xóa tham số hệ thống
        /// </summary>
        /// <param name="id">ID tham số hệ thống cần xóa</param>
        /// <returns></returns>
        [HttpDelete, Route("{idPh}/{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete([FromRoute] string id, [FromRoute] int idPh)
        {
            return await ExecuteFunction(async () =>
            {
                //_contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_THAM_SO_HE_THONG_DELETE);
                //_contextAccessor.SystemLog.ActionName = LogConstants.ACTION_THAM_SO_HE_THONG_DELETE;

                return await _mediator.Send(new DeleteThamSoHeThongCommand(id, idPh));
            });
        }

        /// <summary>
        /// Lấy danh sách tham số hệ thống theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThamSoHeThongModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] ThamSoHeThongQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterThamSoHeThongQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách tham số hệ thống cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <param name="idPh">ID phân hệ</param>
        /// <param name="nhomThamSo">Nhóm tham số</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ThamSoHeThongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "", int? idPh = null, string nhomThamSo = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxThamSoHeThongQuery(count, ts, idPh, nhomThamSo));
                return result;
            });
        }
        #endregion

        /// <summary>
        /// Lấy tham số hệ thống theo phân hệ
        /// </summary>
        /// <param name="phanHe">Mã phân hệ</param>
        /// <returns></returns>
        [HttpGet, Route("by-phan-he")]
        [ProducesResponseType(typeof(ResponseObject<List<ThamSoHeThongModel>>), StatusCodes.Status200OK)]
        // [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.THAM_SO_HE_THONG_VIEW))]
        public async Task<IActionResult> GetByPhanHe(string phanHe)
        {
            try
            {
                var listPhanHe = await _mediator.Send(new GetComboboxPhanHeQuery());
                var idPhanHe = listPhanHe.FirstOrDefault(x => x.PhanHe == phanHe)?.IdPh;
                if (idPhanHe == null)
                {
                    throw new ArgumentException("PhanHe.Not-Existed");
                }
                return await ExecuteFunction(async () => await _mediator.Send(new GetThamSoHeThongByIdPhQuery(idPhanHe.Value)));
            }
            catch (ArgumentException agrEx)
            {
                return Helper.TransformData(new ResponseError(Code.BadRequest, agrEx.Message));
            }
        }
    }
}
