﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nest;
using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Core.Business
{
    public class GetSystemApplicationByIdQuery : MediatR.IRequest<SystemApplicationModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin ứng dụng theo id
        /// </summary>
        /// <param name="id">Id ứng dụng</param>
        public GetSystemApplicationByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetSystemApplicationByIdQuery, SystemApplicationModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<SystemApplicationModel> Handle(GetSystemApplicationByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = SystemApplicationConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SystemApplication.FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<SystemApplication, SystemApplicationModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetSystemApplicationByIdFromElasticQuery : MediatR.IRequest<SystemApplicationModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin ứng dụng theo id từ elasticsearch
        /// </summary>
        /// <param name="id">Id ứng dụng</param>
        public GetSystemApplicationByIdFromElasticQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetSystemApplicationByIdFromElasticQuery, SystemApplicationModel>
        {
            private readonly IConfiguration _config;
            
            public Handler(IConfiguration config)
            {
                _config = config;
            }

            public async Task<SystemApplicationModel> Handle(GetSystemApplicationByIdFromElasticQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                var client = ElasticClientUtils.GetElasticClient(elasticConfig);

                var rs = await client.GetAsync<SystemApplicationElasticModel>(id, u => u
                      .Index(SystemApplicationConstant.CachePrefix));
                var entity = rs.Source;
                return AutoMapperUtils.AutoMap<SystemApplicationElasticModel, SystemApplicationModel>(entity);
            }
        }
    }

    public class GetFilterSystemApplicationQuery : MediatR.IRequest<PaginationList<SystemApplicationBaseModel>>
    {
        public SystemApplicationQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách ứng dụng theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterSystemApplicationQuery(SystemApplicationQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterSystemApplicationQuery, PaginationList<SystemApplicationBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<SystemApplicationBaseModel>> Handle(GetFilterSystemApplicationQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SystemApplication
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Name.ToLower().Contains(ts) || x.Code.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                var listResult = AutoMapperUtils.AutoMap<SystemApplication, SystemApplicationBaseModel>(listData);

                return new PaginationList<SystemApplicationBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }

    public class GetFilterSystemApplicationFromElasticQuery : MediatR.IRequest<PaginationList<SystemApplicationBaseModel>>
    {
        public SystemApplicationQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách ứng dụng theo điều kiện lọc từ elastic
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterSystemApplicationFromElasticQuery(SystemApplicationQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterSystemApplicationFromElasticQuery, PaginationList<SystemApplicationBaseModel>>
        {
            private readonly IConfiguration _config;
            
            public Handler(SystemReadDataContext dataContext, IConfiguration config)
            {
                _config = config;
            }

            public async Task<PaginationList<SystemApplicationBaseModel>> Handle(GetFilterSystemApplicationFromElasticQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                var client = ElasticClientUtils.GetElasticClient(elasticConfig);

                //Full text search - multiMatch
                var multiSearch = new List<Func<MultiMatchQueryDescriptor<SystemApplicationElasticModel>, IMultiMatchQuery>>();

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    multiSearch.Add(m => m.Fields(f => f
                        .Field(p => p.Name)
                        .Field(p => p.Code)
                        .Field(p => p.Description)
                        .Field(p => p.IsActive)
                    )
                    .Query(filter.TextSearch)
                    .Operator(Operator.And));
                }
                if (filter.IsActive.HasValue)
                {

                }

                Func<QueryContainerDescriptor<SystemApplicationElasticModel>, QueryContainer> query =
                    q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                     .Field(f => f.IsActive)
                                     .Query(filter.IsActive.Value.ToString())),
                                bm => bm.MultiMatch(m => m.Fields(f => f
                                        .Field(p => p.Name)
                                        .Field(p => p.Code)
                                        .Field(p => p.Description)
                                        .Field(p => p.IsActive)
                                    )
                                    .Query(filter.TextSearch)
                                    .Operator(Operator.And))
                            )
                        );


                var countResponse = await client.CountAsync<SystemApplicationElasticModel>(s => s.Query(query));

                int totalCount = (int)countResponse.Count;

                // Pagination
                if (filter.PageSize <= 0)
                    filter.PageSize = QueryFilter.DefaultPageSize;
                if (filter.PageNumber <= 0)
                    filter.PageNumber = QueryFilter.DefaultPageNumber;

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                var data = await client.SearchAsync<SystemApplicationElasticModel>(s => s
                        .Index(SystemApplicationConstant.CachePrefix)
                        .From(excludedRows)
                        .Size(filter.PageSize)
                        .Query(query)
                    );

                var listData = data.Documents.ToList();

                List<SystemApplicationBaseModel> listResult = AutoMapperUtils.AutoMap<SystemApplicationElasticModel, SystemApplicationBaseModel>(listData);

                return new PaginationList<SystemApplicationBaseModel>()
                {
                    DataCount = listResult.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }

    public class GetComboboxSystemApplicationQuery : MediatR.IRequest<List<SystemApplicationSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách ứng dụng cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxSystemApplicationQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxSystemApplicationQuery, List<SystemApplicationSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<SystemApplicationSelectItemModel>> Handle(GetComboboxSystemApplicationQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = SystemApplicationConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SystemApplication.Where(x => x.IsActive).OrderBy(x => x.Order).ThenBy(x => x.Name)
                                select new SystemApplicationSelectItemModel()
                                {
                                    Id = item.Id,
                                    Code = item.Code,
                                    Name = item.Name
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(textSearch) || x.Note.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class SyncDataSystemApplicationToElasticQuery : MediatR.IRequest<Unit>
    {
        /// <summary>
        /// Đồng bộ dữ liệu ứng dụng từ database sang elastic
        /// </summary>
        public SyncDataSystemApplicationToElasticQuery()
        {
        }

        public class Handler : IRequestHandler<SyncDataSystemApplicationToElasticQuery, Unit>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IConfiguration _config;

            public Handler(SystemReadDataContext dataContext, IConfiguration config)
            {
                _config = config;
                _dataContext = dataContext;
            }

            public async Task<Unit> Handle(SyncDataSystemApplicationToElasticQuery request, CancellationToken cancellationToken)
            {
                var data = (from entity in _dataContext.SystemApplication
                            select new SystemApplicationElasticModel()
                            {
                                Id = entity.Id,
                                Code = entity.Code,
                                Name = entity.Name,
                                IsActive = entity.IsActive,
                                CreatedDate = entity.CreatedDate,
                                Order = entity.Order,
                                Description = entity.Description
                            });

                var listResult = await data.ToListAsync();

                var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                var client = ElasticClientUtils.GetElasticClient(elasticConfig);

                await client.Indices.DeleteAsync(SystemApplicationConstant.CachePrefix);

                //// Insert one
                //foreach (var item in listResult)
                //{
                //    var indexResponse = await client.CreateAsync(item, c => c
                //        .Index(elasticIndex) // index
                //        .Id(item.Id) // document id
                //    );
                //}

                //// insert bulk
                await client.BulkAsync(b => b
                    .Index(SystemApplicationConstant.CachePrefix)
                    .IndexMany(listResult, (d, doc) => d.Document(doc).Id(doc.Id))
                );

                return Unit.Value;
            }
        }
    }
}
