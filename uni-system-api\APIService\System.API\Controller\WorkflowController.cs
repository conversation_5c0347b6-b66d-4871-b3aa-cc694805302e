﻿using Core.API.Shared;
using Core.Business.Workflow;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System;
using System.Linq;
using OptimaJet.Workflow;
using Microsoft.AspNetCore.Authorization;
using Core.Shared.ContextAccessor;
using OptimaJet.Workflow.Core.Runtime;

namespace Core.API.Controller
{
    [ApiController]
    [Authorize]
    [Route("system/v1/workflow")]
    [ApiExplorerSettings(GroupName = "Workflow")]
    public class WorkflowController : ApiControllerBaseV2
    {
        public WorkflowController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        [HttpGet, Route("API")]
        [AllowAnonymous]
        public async Task<ActionResult> APIGetAsync()
        {
            Stream filestream = null;
            var isPost = Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase);
            if (isPost && Request.Form.Files != null && Request.Form.Files.Count > 0)
                filestream = Request.Form.Files[0].OpenReadStream();

            var pars = new NameValueCollection();
            foreach (var q in Request.Query)
            {
                pars.Add(q.Key, q.Value.First());
            }


            if (isPost)
            {
                var parsKeys = pars.AllKeys;
                //foreach (var key in Request.Form.AllKeys)
                foreach (string key in Request.Form.Keys)
                {
                    if (!parsKeys.Contains(key))
                    {
                        pars.Add(key, Request.Form[key]);
                    }
                }
            }

            (string res, bool hasError) = await WorkflowInit.Runtime.DesignerAPIAsync(pars, filestream);

            var operation = pars["operation"].ToLower();
            if (operation == "downloadscheme" && !hasError)
                return File(Encoding.UTF8.GetBytes(res), "text/xml");

            res = res.Replace("For non-commercial use only", "");

            return Content(res);
        }

        [HttpPost, Route("API")]
        [AllowAnonymous]
        public async Task<ActionResult> APIPostAsync()
        {
            Stream filestream = null;
            var isPost = Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase);
            if (isPost && Request.Form.Files != null && Request.Form.Files.Count > 0)
                filestream = Request.Form.Files[0].OpenReadStream();

            var pars = new NameValueCollection();
            foreach (var q in Request.Query)
            {
                pars.Add(q.Key, q.Value.First());
            }


            if (isPost)
            {
                var parsKeys = pars.AllKeys;
                //foreach (var key in Request.Form.AllKeys)
                foreach (string key in Request.Form.Keys)
                {
                    if (!parsKeys.Contains(key))
                    {
                        pars.Add(key, Request.Form[key]);
                    }
                }
            }

            (string res, bool hasError) = await WorkflowInit.Runtime.DesignerAPIAsync(pars, filestream);

            var operation = pars["operation"].ToLower();
            if (operation == "downloadscheme" && !hasError)
                return File(Encoding.UTF8.GetBytes(res), "text/xml");

            return Content(res);
        }

        /// <summary>  
        /// Lấy danh sách có lọc  
        /// </summary>  
        /// <param name="filter"></param>  
        /// <returns></returns>  
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<WorkflowBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.WORKFLOW_VIEW))]
        public async Task<IActionResult> Filter([FromBody] BaseQueryFilterModel filter)
        {
            return await ExecuteFunction(async () =>
            {
                var schemes = await WorkflowInit.Runtime.GetSchemeCodesAsync();
                var data = schemes.Select(x => new WorkflowBaseModel
                {
                    WorkflowName = x
                });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.WorkflowName.ToLower().Contains(ts));
                }

                data = data.OrderBy(x => x.WorkflowName);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                return new PaginationList<WorkflowBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data.ToList()
                };
            });
        }

        /// <summary>
        /// Lấy lịch sử theo id
        /// </summary>
        /// <param name="id">Id quy trình</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("history/{id}")]
        [ProducesResponseType(typeof(List<ProcessHistoryItem>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetWorkflowHistoryById([FromRoute] Guid id)
        {
            return await ExecuteFunction(async () =>
            {
                var histories = await WorkflowInit.Runtime.GetProcessHistoryAsync(id);
                return histories;
            });
        }

        /// <summary>
        /// Lấy lịch sử approval id
        /// </summary>
        /// <param name="id">Id quy trình</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("history-approval/{id}")]
        [ProducesResponseType(typeof(List<HistoryApprovalModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetWorkflowHistoryApprovalById([FromRoute] Guid id)
        {
            return await ExecuteFunction(async () =>
            {
                var historyApprovals = await WorkflowInit.PersistenceProviderContainer.Provider.GetApprovalHistoryByProcessIdAsync(id);
                var users = await _mediator.Send(new GetComboboxUserQuery());

                var result = (from dt in historyApprovals
                              from u in users.Where(x => !string.IsNullOrEmpty(dt.IdentityId) && x.UserId == Int32.Parse(dt.IdentityId)).DefaultIfEmpty()
                              select new HistoryApprovalModel
                              {
                                  IdentityId = dt.IdentityId,
                                  AllowedTo = dt.AllowedTo,
                                  Commentary = dt.Commentary,
                                  DestinationState = dt.DestinationState,
                                  Id = dt.Id,
                                  InitialState = dt.InitialState,
                                  ProcessId = dt.ProcessId,
                                  Sort = dt.Sort,
                                  TransitionTime = dt.TransitionTime,
                                  TriggerName = dt.TriggerName,
                                  UserName = u?.UserName
                              }).ToList();

                return result;
            });
        }

        ///// <summary>
        ///// Xóa quy trình
        ///// </summary>
        ///// <param name="name">Xóa quy trình</param>
        ///// <returns></returns>
        //[HttpDelete, Route("{id}")]
        //[ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.WORKFLOW_DELETE))]
        //[ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> Delete([FromQuery] string name)
        //{
        //    return await ExecuteFunction(async () =>
        //    {
        //        var histories = await WorkflowInit.Runtime.RemoveWorkflowSchemeAsync(schemeCode);
        //        return Unit.Value;
        //    });
        //}

        /// <summary>
        /// Lấy danh sách cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<object>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var schemes = await WorkflowInit.Runtime.GetSchemeCodesAsync();
                var list = schemes.Select(x => (object)new
                {
                    WorkflowName = x
                }).ToList();
                return list;
            });
        }

        internal class WorkflowBaseModel
        {
            public string WorkflowName { get; set; }
        }

        [HttpPost, Route("start-workflow")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> StartWorkflow([FromBody] StartWorkFlowModel model)
        {
            return await ExecuteFunction(async () =>
            {
                // Lấy thông tin workflow name mặc định từ cấu hình hoặc do người dùng chọn
                if (string.IsNullOrEmpty(model.WorkflowName))
                {
                    // Tên quy trình được lấy trong chức năng quy trình trên trang system
                    // TODO: Thực hiện gán thông tin này vào cấu hình
                    model.WorkflowName = "Quy trình phê duyệt bài báo";
                }

                // Gán quy trình cho bài báo
                string userId = _contextAccessor.UserId.HasValue ? _contextAccessor.UserId.Value.ToString() : string.Empty;
                await WorkflowInit.Runtime.CreateInstanceAsync(
                model.WorkflowName,
                    model.ProcessId,
                    identityId: userId,
                    userId
                );

                // Lấy state hiện tại gán vào dữ liệu
                var workflowState = await WorkflowInit.Runtime.GetCurrentStateNameAsync(model.ProcessId);

                return workflowState;
            });
        }

        [HttpPost, Route("process-workflow")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ProcessWorkflow([FromBody] ProcessWorkFlowModel processModel)
        {
            return await ExecuteFunction(async () =>
            {
                string userId = _contextAccessor.UserId.HasValue ? _contextAccessor.UserId.Value.ToString() : string.Empty;
                var commands = await WorkflowInit.Runtime.GetAvailableCommandsAsync(processModel.ProcessId, userId);

                var command =
                commands.FirstOrDefault(
                        c => c.CommandName.Equals(processModel.CommandName, StringComparison.CurrentCultureIgnoreCase));

                if (command == null)
                    throw new ArgumentException($"{_localizer["bai-bao.quy-trinh-xu-ly-khong-hop-le"]}");

                var rsWF = await WorkflowInit.Runtime.ExecuteCommandAsync(command, userId, userId);

                return rsWF;
            });
        }

        public class StartWorkFlowModel
        {
            public Guid ProcessId { get; set; }
            public string WorkflowName { get; set; }
        }

        public class ProcessWorkFlowModel
        {
            public Guid ProcessId { get; set; }
            public string CommandName { get; set; }
        }
    }
}