﻿using Core.Business;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class GetComboboxHocKyDangKyQuery : IRequest<List<HocKyDangKySelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y học kỳ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHocKyDangKyQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHocKyDangKyQuery, List<HocKyDangKySelectItemModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HocKyDangKySelectItemModel>> Handle(GetComboboxHocKyDangKyQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;

                string cacheKey = HocKyDangKyConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbHocKyDangKys.OrderBy(x => x.HocKy)
                                select new HocKyDangKySelectItemModel()
                                {
                                    HocKy = dt.HocKy,
                                    NamHoc = dt.NamHoc,
                                    TuNgay = dt.TuNgay,
                                    DenNgay = dt.DenNgay
                                });

                    return await data.ToListAsync();
                });

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
