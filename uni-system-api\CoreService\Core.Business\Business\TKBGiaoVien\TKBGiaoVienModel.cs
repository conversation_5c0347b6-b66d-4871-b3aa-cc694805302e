﻿using Core.Data;
using Core.Shared;
using FluentEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Business
{
    public class TKBGiaoVienBaseModel
    {
        public int Id { get; set; }
        public string MaCB { get; set; }
        public string HoTen { get; set; }
    }

    public class TKBGiaoVienModel : TKBGiaoVienBaseModel
    {
        public string Ten { get; set; }
        public int? GioiTinhID { get; set; }
        public DateTime? NgaySinh { get; set; }
        public int KhoaID { get; set; }
        public int HocHamID { get; set; }
        public int HocViID { get; set; }
        public int ChucDanhID { get; set; }
        public int ChucVuID { get; set; }
        public bool ThinhGiang { get; set; }
        public string TenDangNhap { get; set; }
        public string MatKhau { get; set; }
        public bool? KhongHoatDong { get; set; }
        public float HeSoLuong { get; set; }
        public string SoDienThoai { get; set; }
        public string Email { get; set; }
        public string DiaChiLienHe { get; set; }
        public bool? CoHuu { get; set; }
        public bool? KiemGiang { get; set; }
        public string CMTND { get; set; }
        public DateTime? NgayCapCMND { get; set; }
        public string NoiCapCMND { get; set; }
        public string TenNganHang { get; set; }
        public string ChuyenMonDaoTao { get; set; }
        public string ThamNienGiangDay { get; set; }
        public string SoTaiKhoan { get; set; }
        public string ChuTaiKhoan { get; set; }
        public string MaSoThue { get; set; }
        public string ChiNhanhNganHang { get; set; }
        public string TenTinhNganHang { get; set; }
        public bool? DaNopHD { get; set; }
        public int? BmChinhID { get; set; }
        public int? DonViQuanLyID { get; set; }
        public int TonGiaoID { get; set; }
        public int DanTocID { get; set; }
        public int QuocTichID { get; set; }
        public string ChuyenNganhGiangDay { get; set; }
        public string PhanLoai { get; set; }
        public string SoSoBaoHiem { get; set; }
        public int DonViCongTacID { get; set; }
        public string DonViCongTac { get; set; }
    }

    public class CreateTKBGiaoVienModel : TKBGiaoVienModel
    {
    }

    public class UpdateTKBGiaoVienModel : TKBGiaoVienModel
    {
        public void UpdateEntity(TKBGiaoVien entity)
        {
          
        }
    }

    public class TKBGiaoVienSelectItemModel : SelectItemModel
    {
        public int Id { get; set; }
        public string MaCB { get; set; }
        public string HoTen { get; set; }
    }

    public class TKBGiaoVienQueryFilter : BaseQueryFilterModel
    {
    }

    public class GiaoVienModel
    {
        public int Id { get; set; }
        public string MaCB { get; set; }
        public string HoTen { get; set; }
        public string SoDienThoai { get; set; }
        public int IdKhoa { get; set; }
        public int IdBoMon { get; set; }
        public string BoMon { get; set; }
        public int IdChucDanh { get; set; }
        public string ChucDanh { get; set; }
        public int IdHocVi { get; set; }
        public string HocVi { get; set; }
    }
}
