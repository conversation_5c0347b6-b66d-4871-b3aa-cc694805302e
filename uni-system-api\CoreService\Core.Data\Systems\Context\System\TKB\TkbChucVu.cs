﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbChucVu")]
    public class TkbChucVu
    {

        public TkbChucVu()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_chuc_vu")]
        public int IdChucVu { get; set; }

        [Column("Ma_chuc_vu"), MaxLength(5)]
        public string MaChucVu { get; set; }

        [Column("chuc_vu"), MaxLength(100)]
        public string ChucVu { get; set; }


    }
}
