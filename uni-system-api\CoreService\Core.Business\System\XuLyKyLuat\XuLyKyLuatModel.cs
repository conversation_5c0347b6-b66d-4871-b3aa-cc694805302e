﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XuLySelectItemModel
    {
        public int IdXuLy { get; set; }
        public int IdCap { get; set; }
        public string XuLy { get; set; }
    }

    public class XuLyBaseModel
    {
        public int IdXuLy { get; set; }
        public int IdCap { get; set; }
        public string TenCap { get; set; }
        public string XuLy { get; set; }
        public int SoThang { get; set; }
        public float DiemPhat { get; set; }
        public int MucXuLy { get; set; }

    }


    public class XuLyModel : XuLyBaseModel
    {

    }

    public class XuLyFilterModel : BaseQueryFilterModel
    {
        public XuLyFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXuLy";
        }
    }

    public class CreateXuLyModel
    {
        [Required(ErrorMessage = "XuLy.IdXuLy.NotRequire")]
        public int IdXuLy { get; set; }


        [Required(ErrorMessage = "XuLy.IdCap.NotRequire")]
        public int IdCap { get; set; }

        [Required(ErrorMessage = "XuLy.SoThang.NotRequire")]
        public int SoThang { get; set; }

        [MaxLength(50, ErrorMessage = "XuLy.XuLy.MaxLength(50)")]
        [Required(ErrorMessage = "XuLy.XuLy.NotRequire")]
        public string XuLy { get; set; }

        [Required(ErrorMessage = "XuLy.DiemPhat.NotRequire")]
        public float DiemPhat { get; set; }

        [Required(ErrorMessage = "XuLy.MucXuLy.NotRequire")]
        public int MucXuLy { get; set; }

    }

    public class CreateManyXuLyModel
    {
        public List<CreateXuLyModel> listXuLyModels { get; set; }
    }

    public class UpdateXuLyModel : CreateXuLyModel
    {
        public void UpdateEntity(SvXuLy input)
        {
            input.IdXuLy = IdXuLy;
            input.IdCap = IdCap;
            input.SoThang = SoThang;
            input.XuLy = XuLy;
            input.DiemPhat = DiemPhat;
            input.MucXuLy = MucXuLy;

        }
    }
}
