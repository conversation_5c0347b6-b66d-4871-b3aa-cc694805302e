﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxChucVuQuery : IRequest<List<ChucVuSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y chức vụ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxChucVuQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxChucVuQuery, List<ChucVuSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChucVuSelectItemModel>> Handle(GetComboboxChucVuQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChucVuConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbChucVus.OrderBy(x => x.ChucVu)
                                select new ChucVuSelectItemModel()
                                {
                                    IdChucVu = dt.IdChucVu,
                                    MaChucVu = dt.MaChucVu,
                                    ChucVu = dt.ChucVu
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.ChucVu.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterChucVuQuery : IRequest<PaginationList<ChucVuBaseModel>>
    {
        public ChucVuFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách chức vụ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChucVuQuery(ChucVuFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChucVuQuery, PaginationList<ChucVuBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChucVuBaseModel>> Handle(GetFilterChucVuQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbChucVus
                            select new ChucVuBaseModel
                            {
                                IdChucVu = dt.IdChucVu,
                                MaChucVu = dt.MaChucVu,
                                ChucVu = dt.ChucVu

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.ChucVu.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<ChucVuBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetChucVuByIdQuery : IRequest<ChucVuModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin chức vụ theo id
        /// </summary>
        /// <param name="id">Id chức vụ</param>
        public GetChucVuByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChucVuByIdQuery, ChucVuModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChucVuModel> Handle(GetChucVuByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ChucVuConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbChucVus.FirstOrDefaultAsync(x => x.IdChucVu == id);

                    return AutoMapperUtils.AutoMap<TkbChucVu, ChucVuModel>(entity);
                });
                return item;
            }
        }
    }
}
