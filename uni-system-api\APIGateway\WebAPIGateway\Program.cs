﻿using Ocelot.DependencyInjection;
using Ocelot.Middleware;
using Serilog;
using Serilog.Exceptions;
using WebAPIGateway.Middleware;
using WebAPIGateway.Share;
using Prometheus;
using Microsoft.AspNetCore.HttpOverrides;

var MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

var builder = WebApplication.CreateBuilder(args);

IConfiguration configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json", true, true)
                .AddCommandLine(args)
                .AddEnvironmentVariables()
                .Build();


// Lấy thông tin vault từ configuration
var vaultOptions = configuration.GetSection("Vault").Get<VaultOptions>();
if (vaultOptions?.Enabled == true)
{
    // Thêm cấu hình của Vault vào Configuration
    configuration = new ConfigurationBuilder()
        .AddConfiguration(configuration)
        .AddVault(options =>
        {
            options.Address = vaultOptions.Address;
            options.Token = vaultOptions.Token;
            options.Mount = vaultOptions.Mount;
            options.SecretCommon = vaultOptions.SecretCommon;
            options.SecretDetail = vaultOptions.SecretDetail;
        })
        .Build();
}

var corsOrigin = configuration["AppSettings:CorsOrigins"];
string[] listCorsOrigin = string.IsNullOrEmpty(corsOrigin)
    ? Array.Empty<string>()
    : corsOrigin
        .Split(",", StringSplitOptions.RemoveEmptyEntries)
        .Select(o => o.RemovePostFix("/"))
        .ToArray();

builder.Services.AddCors(options =>
{
    options.AddPolicy(MyAllowSpecificOrigins, policy =>
    {
        if (listCorsOrigin.Contains("*"))
        {
            policy.SetIsOriginAllowed(origin => true)
                  .AllowAnyHeader()
                  .AllowAnyMethod()
                  .AllowCredentials();
        }
        else
        {
            policy.WithOrigins(listCorsOrigin)
                  .WithExposedHeaders("_UniGWErrFormat")
                  .AllowAnyHeader()
                  .AllowAnyMethod()
                  .AllowCredentials();
        }
    });
});

// Cấu hình Serilog
var logger = new LoggerConfiguration()
        .ReadFrom.Configuration(configuration)
        .Enrich.FromLogContext()
        .Enrich.WithExceptionDetails()
        .Enrich.WithMachineName();

Log.Logger = logger.CreateLogger();

builder.Host.UseSerilog(); // Sử dụng Serilog

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddSwaggerForOcelot(builder.Configuration);
builder.Services.AddOcelot(configuration).AddDCDRLoadBalancer();

builder.Services.AddHealthChecks();

var app = builder.Build();

app.UseMetricServer();//Starting the metrics exporter, will expose "/metrics"

app.UseHealthChecks("/health");//your request URL will be health

// Configure the HTTP request pipeline.
if (configuration["AppSettings:EnableSwagger"] == "true")
{
    app.UseSwaggerForOcelotUI();
}

// Ghi log request
if (configuration["AppSettings:UseSerilogRequestLogging"] == "true")
{
    app.UseSerilogRequestLogging();
}

// Thêm middleware RequestResponseLoggingMiddleware
if (configuration["AppSettings:EnableRequestResponseLoggingMiddleware"] == "true")
{
    app.UseMiddleware<RequestResponseLoggingMiddleware>();
}

app.UseHttpsRedirection();
app.UseDefaultFiles();
app.UseStaticFiles();
app.UseRouting();

app.UseCors(MyAllowSpecificOrigins);

// global cors policy
//app.UseCors(x => x
//   .AllowAnyOrigin()
//   .AllowAnyMethod()
//   .AllowAnyHeader());

// https://ocelot.readthedocs.io/en/latest/features/websockets.html
app.UseWebSockets();

app.UseOcelot().Wait();

//adding metrics related to HTTP
app.UseHttpMetrics(options =>
{
    options.AddCustomLabel("host", context => context.Request.Host.Host);
});

app.UseAuthorization();

app.MapControllers();

var forwardOptions = new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto,
    RequireHeaderSymmetry = false
};

forwardOptions.KnownNetworks.Clear();
forwardOptions.KnownProxies.Clear();

// ref: https://github.com/aspnet/Docs/issues/2384
app.UseForwardedHeaders(forwardOptions);

try
{
    var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
    Log.Information($"API started in folder: {AppDomain.CurrentDomain.BaseDirectory} - (with evirontment - {environment}) at: " + DateTime.Now.ToString());

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "The service terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

