﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class TinhSelectItemModel
    {
        public string IdTinh { get; set; }
        public string TenTinh { get; set; }
    }

    public class TinhBaseModel
    {
        public string IdTinh { get; set; }
        public string TenTinh { get; set; }
        public string TenTinhEn { get; set; }
    }


    public class TinhModel : TinhBaseModel
    {

    }

    public class TinhFilterModel : BaseQueryFilterModel
    {
        public TinhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTinh";
        }
    }

    public class CreateTinhModel
    {
        [MaxLength(5, ErrorMessage = "Tinh.IdTinh.MaxLength(5)")]
        [Required(ErrorMessage = "Tinh.IdTinh.NotRequire")]
        public string IdTinh { get; set; }


        [MaxLength(50, ErrorMessage = "Tinh.Tinh.MaxLength(50)")]
        [Required(ErrorMessage = "Tinh.Tinh.NotRequire")]
        public string TenTinh { get; set; }

        [MaxLength(50, ErrorMessage = "Tinh.TinhEn.MaxLength(50)")]
        public string TenTinhEn { get; set; }

    }

    public class CreateManyTinhModel
    {
        public List<CreateTinhModel> listTinhModels { get; set; }
    }

    public class UpdateTinhModel : CreateTinhModel
    {
        public void UpdateEntity(SvTinh input)
        {
            input.IdTinh = IdTinh;
            input.TenTinh = TenTinh;
            input.TenTinhEn = TenTinhEn;

        }
    }
}
