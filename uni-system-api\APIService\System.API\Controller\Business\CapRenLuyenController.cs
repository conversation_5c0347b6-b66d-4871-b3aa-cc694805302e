﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/cap-ren-luyen")]
    [ApiExplorerSettings(GroupName = "80. Cấp rèn luyện")]
    [Authorize]
    public class CapRenLuyenController : ApiControllerBase
    {
        public CapRenLuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách cấp rèn luyện cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<CapRenLuyenSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxCapRenLuyenQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách cấp rèn luyện có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<CapRenLuyenBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_REN_LUYEN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] CapRenLuyenFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterCapRenLuyenQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết cấp rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<CapRenLuyenModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_REN_LUYEN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetCapRenLuyenByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới cấp rèn luyện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_REN_LUYEN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateCapRenLuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_REN_LUYEN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_REN_LUYEN_CREATE;


                return await _mediator.Send(new CreateCapRenLuyenCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Sửa cấp rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_REN_LUYEN_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateCapRenLuyenModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_REN_LUYEN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_REN_LUYEN_UPDATE;
                return await _mediator.Send(new UpdateCapRenLuyenCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa cấp rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_REN_LUYEN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_REN_LUYEN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_REN_LUYEN_DELETE;

                return await _mediator.Send(new DeleteCapRenLuyenCommand(id, u.SystemLog));
            });
        }

    }
}
