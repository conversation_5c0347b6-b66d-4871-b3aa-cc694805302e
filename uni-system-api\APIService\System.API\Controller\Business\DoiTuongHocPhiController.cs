﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/doi-tuong-hoc-phi")]
    [ApiExplorerSettings(GroupName = "48. Đôi tượng học phí")]
    [Authorize]
    public class DoiTuongHocPhiController : ApiControllerBase
    {
        public DoiTuongHocPhiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách đôi tượng học phí cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<DoiTuongHocPhiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxDoiTuongHocPhiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách đôi tượng học phí có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<DoiTuongHocPhiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] DoiTuongHocPhiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterDoiTuongHocPhiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết đôi tượng học phí
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<DoiTuongHocPhiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetDoiTuongHocPhiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới đôi tượng học phí
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateDoiTuongHocPhiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_CREATE;


                return await _mediator.Send(new CreateDoiTuongHocPhiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel đôi tượng học phí
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyDoiTuongHocPhiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_CREATE_MANY;


                return await _mediator.Send(new CreateManyDoiTuongHocPhiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa đôi tượng học phí
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDoiTuongHocPhiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_UPDATE;
                return await _mediator.Send(new UpdateDoiTuongHocPhiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa đôi tượng học phí
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_PHI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_PHI_DELETE;

                return await _mediator.Send(new DeleteDoiTuongHocPhiCommand(id, u.SystemLog));
            });
        }

    }
}
