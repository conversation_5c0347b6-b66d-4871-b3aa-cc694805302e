﻿using System.Threading;

namespace Core.Shared.ContextAccessor
{
    public class MessageQueueContextAccessor : IContextAccessor
    {
        private static readonly AsyncLocal<MessageQueueContext> _context = new();

        public string CorrelationId
        {
            get => _context.Value?.CorrelationId;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.CorrelationId = value;
            }
        }
        public string TraceId
        {
            get => _context.Value?.TraceId;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.TraceId = value;
            }
        }

        public int? UserId
        {
            get => _context.Value.UserId;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.UserId = value;
            }
        }

        public string UserName
        {
            get => _context.Value?.UserName;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.UserName = value;
            }
        }

        public string MaCB
        {
            get => _context.Value?.MaCB;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.MaCB = value;
            }
        }

        public int? GiaoVienId
        {
            get => _context.Value?.GiaoVienId;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.GiaoVienId = value;
            }
        }

        public string Language
        {
            get => _context.Value?.Language;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.Language = value;
            }
        }

        public SystemLogModel SystemLog
        {
            get => _context.Value?.SystemLog;
            set
            {
                if (_context.Value == null) _context.Value = new MessageQueueContext();
                _context.Value.SystemLog = value;
            }
        }

        private class MessageQueueContext
        {
            public string CorrelationId { get; set; }
            public string TraceId { get; set; }
            public int? UserId { get; set; }
            public string UserName { get; set; }
            public string MaCB { get; set; } = "";
            public int? GiaoVienId { get; set; }
            public string Language { get; set; }
            public SystemLogModel SystemLog { get; set; } = new SystemLogModel();
        }
    }
}
