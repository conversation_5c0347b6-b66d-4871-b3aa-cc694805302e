﻿using Core.Business;
using System.Collections.Generic;

namespace Leader.Business
{
    public class QuyMoSinhVienBaseModel
    {
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public string TenNganh { get; set; }
        public int? TongSoSV { get; set; }
        public int TongSoSVNam { get; set; }
        public int TongSoSVNu { get; set; }
        public double TiLeSVNam { get; set; }
        public double TiLeSVNu { get; set; }
    }
    public class QuyMoSinhVienModel : QuyMoSinhVienBaseModel
    {

    }

    public class QuyMoSinhVienQueryFilter : BaseQueryFilterModel
    {
        public int IDHe { get; set; }
        public int IDKhoa { get; set; }
        public int IDNganh { get; set; }
        public int HocKy { get; set; }
        public string NamHoc { get; set; }
        public int KhoaHoc { get; set; }
    }

    public class TongHopQuyMoBaseModel
    {
        public string TenHe { get; set; }
        public int? Count { get; set; }
    }

    public class TongHopQuyMoModel
    {
        public string TenKhoa { get; set; }
        public List<TongHopQuyMoBaseModel> ListTH { get; set; }
    }
}

