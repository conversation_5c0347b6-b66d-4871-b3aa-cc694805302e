﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXuLyCommand : IRequest<Unit>
    {
        public CreateXuLyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXuLyCommand(CreateXuLyModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXuLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXuLyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XuLyKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXuLyModel, SvXuLy>(model);

                var checkCode = await _dataContext.SvXuLys.AnyAsync(x => x.IdXuLy == entity.IdXuLy || x.XuLy == entity.XuLy );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XuLy.Existed", entity.XuLy.ToString()]}");
                }

                await _dataContext.SvXuLys.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XuLyKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới xử lý kỷ luật: {entity.XuLy}",
                    ObjectCode = XuLyKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdXuLy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXuLyCommand : IRequest<Unit>
    {
        public CreateManyXuLyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXuLyCommand(CreateManyXuLyModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXuLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXuLyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XuLyKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXuLyAdd = model.listXuLyModels.Select(x => x.XuLy).ToList();

                var entity = AutoMapperUtils.AutoMap<CreateManyXuLyModel, SvXuLy>(model);

                // Check data duplicate
                if (listXuLyAdd.Count() != listXuLyAdd.Distinct().Count() )
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXuLys.AnyAsync(x => listXuLyAdd.Contains(x.XuLy)) )
                {
                    throw new ArgumentException($"{_localizer["XuLy.Existed"]}");
                }

                var listEntity = model.listXuLyModels.Select(x => new SvXuLy()
                {
                    IdXuLy = x.IdXuLy,
                    IdCap = x.IdCap,
                    XuLy = x.XuLy,
                    DiemPhat = x.DiemPhat,
                    SoThang = x.SoThang,
                    MucXuLy = x.MucXuLy

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXuLy).ToList();

                Log.Information($"Create many {XuLyKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import xử lý kỷ luật từ file excel",
                    ObjectCode = XuLyKyLuatConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXuLyCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXuLyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXuLyCommand(int id, UpdateXuLyModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXuLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXuLyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XuLyKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXuLys.FirstOrDefaultAsync(dt => dt.IdXuLy == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvXuLys.AnyAsync(x => x.XuLy == model.XuLy  && x.IdXuLy != model.IdXuLy);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XuLy.Existed", model.XuLy.ToString()]}");
                }

                Log.Information($"Before Update {XuLyKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXuLys.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XuLyKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XuLyKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật xử lý kỷ luật: {entity.XuLy}",
                    ObjectCode = XuLyKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdXuLy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey(entity.IdXuLy.ToString()));
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXuLyCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXuLyCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXuLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXuLyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XuLyKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXuLys.FirstOrDefaultAsync(x => x.IdXuLy == id);

                _dataContext.SvXuLys.Remove(entity);

                Log.Information($"Delete {XuLyKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa xử lý kỷ luật: {entity.XuLy}",
                    ObjectCode = XuLyKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdXuLy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey());
                _cacheService.Remove(XuLyKyLuatConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
