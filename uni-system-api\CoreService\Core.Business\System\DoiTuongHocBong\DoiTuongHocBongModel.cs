﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DoiTuongHocBongSelectItemModel
    {
        public int IdDoiTuongHocBong { get; set; }
        public string MaDoiTuongHocBong { get; set; }
        public string DoiTuongHocBong { get; set; }
        public int SoTienTroCap { get; set; }
    }

    public class DoiTuongHocBongBaseModel
    {
        public int IdDoiTuongHocBong { get; set; }
        public string MaDoiTuongHocBong { get; set; }
        public string DoiTuongHocBong { get; set; }
        public int SoTienTroCap { get; set; }
        public int PhanTramTroCap { get; set; }
    }


    public class DoiTuongHocBongModel : DoiTuongHocBongBaseModel
    {

    }

    public class DoiTuongHocBongFilterModel : BaseQueryFilterModel
    {
        public DoiTuongHocBongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdDoiTuongHocBong";
        }
    }

    public class CreateDoiTuongHocBongModel
    {
        [Required(ErrorMessage = "DoiTuongHocBong.IdDoiTuongHocBong.NotRequire")]
        public int IdDoiTuongHocBong { get; set; }

        [MaxLength(5, ErrorMessage = "DoiTuongHocBong.MaDoiTuongHocBong.MaxLength(5)")]
        [Required(ErrorMessage = "DoiTuongHocBong.MaDoiTuongHocBong.NotRequire")]
        public string MaDoiTuongHocBong { get; set; }

        [MaxLength(50, ErrorMessage = "DoiTuongHocBong.DoiTuongHocBong.MaxLength(50)")]
        [Required(ErrorMessage = "DoiTuongHocBong.DoiTuongHocBong.NotRequire")]
        public string DoiTuongHocBong { get; set; }

        [Required(ErrorMessage = "DoiTuongHocBong.SoTienTroCap.NotRequire")]
        public int SoTienTroCap { get; set; }

        [Required(ErrorMessage = "DoiTuongHocBong.PhanTramTroCap.NotRequire")]
        public int PhanTramTroCap { get; set; }

    }

    public class CreateManyDoiTuongHocBongModel
    {
        public List<CreateDoiTuongHocBongModel> listDoiTuongHocBongModels { get; set; }
    }

    public class UpdateDoiTuongHocBongModel : CreateDoiTuongHocBongModel
    {
        public void UpdateEntity(SvDoiTuongHocBong input)
        {
            input.IdDoiTuongHocBong = IdDoiTuongHocBong;
            input.MaDoiTuongHocBong = MaDoiTuongHocBong;
            input.DoiTuongHocBong = DoiTuongHocBong;
            input.SoTienTroCap = SoTienTroCap;
            input.PhanTramTroCap = PhanTramTroCap;

        }
    }
}
