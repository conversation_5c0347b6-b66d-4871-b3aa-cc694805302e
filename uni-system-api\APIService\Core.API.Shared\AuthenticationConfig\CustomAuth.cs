using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Core.Business;
using Core.Shared;
using Serilog;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using Microsoft.Extensions.Configuration;

namespace Core.API.Shared
{
    public static class CustomAuthExtensions
    {
        public static AuthenticationBuilder AddCustomAuth(this AuthenticationBuilder builder,
            Action<CustomAuthOptions> configureOptions)
        {
            return builder.AddScheme<CustomAuthOptions, CustomAuthHandler>("Custom Scheme", "Custom Auth",
                configureOptions);
        }
    }

    public class CustomAuthOptions : AuthenticationSchemeOptions
    {
        public CustomAuthOptions()
        {
        }
    }

    internal class CustomAuthHandler : AuthenticationHandler<CustomAuthOptions>
    {
        private readonly ICacheService _cacheService;
        private readonly IMediator _mediator;
        private readonly IConfiguration _config;

        public CustomAuthHandler(
            IOptionsMonitor<CustomAuthOptions> options,
            IConfiguration config,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISystemClock clock,
            IMediator mediator,
            ICacheService cacheService
        ) : base(options, logger, encoder, clock)
        {
            _cacheService = cacheService;
            _mediator = mediator;
            _config = config;
            // store custom services here...
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            try
            {
                string authHeader = Request.Headers["Authorization"].ToString();
                // Bổ sung cho trường hợp xác thực SignalR, accesstoken không được truyền qua header, sẽ truyền theo query để xác thực
                if (string.IsNullOrEmpty(authHeader))
                {
                    authHeader = Request.Query["access_token"].ToString();
                }

                if (string.IsNullOrEmpty(authHeader))
                {
                    return AuthenticateResult.Fail("Không xác thực");
                }

                AuthenticateResult result = null;

                #region Basic

                if (_config["Authentication:Basic:Enable"] == "true")
                {
                    if (authHeader != null && authHeader.StartsWith("Basic "))
                    {
                        // Get the encoded username and password
                        var encodedUsernamePassword =
                            authHeader.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)[1]?.Trim();
                        // Decode from Base64 to string
                        var decodedUsernamePassword =
                            Encoding.UTF8.GetString(Convert.FromBase64String(encodedUsernamePassword));
                        // Split username and password
                        var username = decodedUsernamePassword.Split(':', 2)[0];
                        var password = decodedUsernamePassword.Split(':', 2)[1];
                        // Check if login is correct
                        if (username == _config["Authentication:AdminUser"] &&
                            password == _config["Authentication:AdminPassWord"])
                        {
                            var claimsBasic = new List<Claim>();
                            claimsBasic.Add(new Claim(ClaimTypes.NameIdentifier,
                                UserConstants.AdministratorId.ToString()));
                            ClaimsIdentity claimsIdentityBasic = new ClaimsIdentity(claimsBasic, "Basic");
                            ClaimsPrincipal claimsPrincipalBasic = new ClaimsPrincipal(claimsIdentityBasic);

                            result = AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipalBasic,
                                new AuthenticationProperties(), "Basic"));
                            return result;
                        }
                    }
                }

                #endregion

                #region NoAuth

                if (_config["Authentication:NoAuth:Enable"] == "true")
                {
                    var claims = new List<Claim>();
                    // claims.Add(new Claim(ClaimTypes.NameIdentifier, UserConstants.AdministratorId.ToString()));
                    ClaimsIdentity claimsIdentity = new ClaimsIdentity(claims, "NONE_AUTH");
                    ClaimsPrincipal claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

                    result = AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipal,
                        new AuthenticationProperties(), "NONE_AUTH"));
                    return result;
                }

                #endregion

                #region JWT

                if (_config["Authentication:Jwt:Enable"] == "true")
                {
                    if (authHeader != null && authHeader.StartsWith("Bearer "))
                    {
                        // Get the token
                        var token = authHeader.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)[1]?.Trim();

                        var claimsJwt = JwtService.ValidateToken(_config, token);

                        if (claimsJwt != null)
                        {
                            var claims = new List<Claim>();
                            var sub = claimsJwt.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier).Value;
                            int userId = 0;

                            Int32.TryParse(sub, out userId);

                            claims.AddRange(claimsJwt.Claims);

                            #region Bổ sung Role & Permission của người dùng vào Claims

                            var userName = Helper.GetValueFromJWTTokenByKey(token, "x-user-name");
                            if (!string.IsNullOrEmpty(userName))
                            {
                                claims.Add(new Claim(ClaimTypes.Name, userName));
                            }

                            if (_config["AppSettings:IgnoreLoadPermission"] != "true")
                            {
                                var permissions = await _mediator.Send(new GetUserPermissionQuery(userId, 0));
                                if (!permissions.IsActive)
                                {
                                    return AuthenticateResult.Fail("Không xác thực");
                                }

                                claims.Add(new Claim(ClaimConstants.ROLES, string.Join(",", permissions.Roles)));
                                claims.Add(new Claim(ClaimConstants.PERMISSTTIONS,
                                    string.Join(",", permissions.Permissions)));
                            }

                            #endregion

                            // claims.Add(new Claim(ClaimTypes.NameIdentifier, UserConstants.AdministratorId.ToString()));
                            ClaimsIdentity claimsIdentityJwt = new ClaimsIdentity(claims, "Jwt");
                            ClaimsPrincipal claimsPrincipalJwt = new ClaimsPrincipal(claimsIdentityJwt);


                            result = AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipalJwt,
                                new AuthenticationProperties(), "Jwt"));

                            return result;
                        }
                    }
                }

                #endregion

                #region IdentityServer or Keycloak

                var isEnableIdentityServer = _config["Authentication:IdentityServer:Enable"] == "true";
                var isEnableKeycloak = _config["Authentication:Keycloak:Enable"] == "true";

                if ((isEnableIdentityServer || isEnableKeycloak) && !string.IsNullOrEmpty(authHeader))
                {
                    // Get token
                    string token = string.Empty;
                    // Bổ sung cho trường hợp SignalR: token sẽ truyền theo query param
                    if (authHeader.Contains(" "))
                    {
                        token = authHeader.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)[1]?.Trim();
                    }
                    else
                    {
                        token = authHeader;
                    }

                    if (!string.IsNullOrEmpty(token))
                    {
                        #region Kiểm tra thời hạn của token

                        var handler = new JwtSecurityTokenHandler();

                        if (!handler.CanReadToken(token))
                        {
                            return AuthenticateResult.Fail("Invalid JWT format.");
                        }

                        var tokenInfo = handler.ReadJwtToken(token);

                        var exp = tokenInfo.Claims.FirstOrDefault(x => x.Type == "exp").Value;

                        long expLong = long.Parse(exp);

                        var expDate = Utils.UnixTimeStampToDateTime(expLong);

                        // Thêm 10p cho token
                        if (expDate < DateTime.Now)
                        {
                            Log.Information("Token đã hết hạn");
                            return AuthenticateResult.Fail("Token đã hết hạn");
                        }

                        #endregion

                        #region Lấy thông tin key của Identy Server/Keycloak && Xác thực token dựa vào thông tin JWKS key

                        bool checkValidToken = false;

                        var jWKSKeysSSO = await GetJWKSKeysSSO();

                        var key = jWKSKeysSSO.Keys.FirstOrDefault(x => x.Kid == (string)tokenInfo.Header["kid"]);

                        if (key == null)
                        {
                            Log.Information("Không tìm thấy key trong JWKS");
                            return AuthenticateResult.Fail("Không tìm thấy key trong JWKS");
                        }

                        if (!Helper.ValidateTokenByJWKS(token, key))
                        {
                            return AuthenticateResult.Fail("Token không hợp lệ");
                        }

                        #endregion

                        var claims = new List<Claim>();
                        int userId = 0;
                        if (isEnableIdentityServer)
                        {
                            var sub = tokenInfo.Claims.FirstOrDefault(x => x.Type == "sub").Value;

                            Int32.TryParse(sub, out userId);
                        }
                        else if (isEnableKeycloak)
                        {
                            var userName = tokenInfo.Claims.FirstOrDefault(x => x.Type == "preferred_username").Value;

                            var userInfo = await _mediator.Send(new GetUserBaseForAuthQueryFromUserName(userName));

                            userId = userInfo.UserId;
                        }

                        claims.Add(new Claim(ClaimTypes.NameIdentifier, userId.ToString()));
                        claims.AddRange(tokenInfo.Claims);

                        #region Bổ sung Role & Permission của người dùng vào Claims

                        var permissions = await _mediator.Send(new GetUserPermissionQuery(userId, 0));
                        if (!permissions.IsActive)
                        {
                            return AuthenticateResult.Fail("Không xác thực");
                        }

                        claims.Add(new Claim(ClaimConstants.USER_NAME,
                            Helper.GetValueFromJWTTokenByKey(token, "user_name")));
                        claims.Add(new Claim(ClaimTypes.Name, Helper.GetValueFromJWTTokenByKey(token, "user_name")));
                        claims.Add(new Claim(ClaimConstants.ROLES, string.Join(",", permissions.Roles)));
                        claims.Add(new Claim(ClaimConstants.PERMISSTTIONS, string.Join(",", permissions.Permissions)));

                        #endregion

                        // claims.Add(new Claim(ClaimTypes.NameIdentifier, UserConstants.AdministratorId.ToString()));
                        ClaimsIdentity claimsIdentity = new ClaimsIdentity(claims, "IdentityServer");
                        ClaimsPrincipal claimsPrincipalJwt = new ClaimsPrincipal(claimsIdentity);

                        result = AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipalJwt,
                            new AuthenticationProperties(), "IdentityServer"));

                        return result;
                    }
                }

                #endregion

                //Log.Error(authHeader);
                return AuthenticateResult.Fail("Không xác thực");
            }
            catch (Exception ex)
            {
                return AuthenticateResult.Fail("Không xác thực");
            }
        }

        private async Task<JWKSKeysSSO> GetJWKSKeysSSO()
        {
            try
            {
                var enableCacheJWKS =
                    (_config["Authentication:IdentityServer:EnableCacheJWKS"] == "true" &&
                     _config["Authentication:IdentityServer:Enable"] == "true") ||
                    (_config["Authentication:Keycloak:EnableCacheJWKS"] == "true" &&
                     _config["Authentication:Keycloak:Enable"] == "true");
                if (!enableCacheJWKS)
                {
                    return JsonSerializer.Deserialize<JWKSKeysSSO>(await GetJWKSFromSSO());
                }
                else
                {
                    string cacheKey = "jwks-ids";
                    string jwksKeyBase64 = await _cacheService.GetOrCreate(cacheKey, () => { return ""; });

                    // Nếu key trống thì call lên ID4 để lấy key về lưu lại
                    if (string.IsNullOrEmpty(jwksKeyBase64))
                    {
                        jwksKeyBase64 = Utils.Base64Encode(await GetJWKSFromSSO());

                        // Lưu thông tin jwks vào cache
                        await _cacheService.Set(cacheKey, () => { return jwksKeyBase64; });
                    }

                    return JsonSerializer.Deserialize<JWKSKeysSSO>(Utils.Base64Decode(jwksKeyBase64));
                }
            }
            catch (Exception ex)
            {
                Log.Error("{Message} - {Error}", ex.Message, ex.ToString());
                throw;
            }
        }

        private async Task<string> GetJWKSFromSSO()
        {
            var client = new HttpClient();
            string jwksUrl = _config["Authentication:IdentityServer:Uri"] + "/.well-known/openid-configuration/jwks";
            if (!string.IsNullOrEmpty(_config["Authentication:Keycloak:Uri"]) && _config["Authentication:Keycloak:Enable"] == "true")
            {
                jwksUrl =
                    $"{_config["Authentication:Keycloak:Uri"]}/realms/{_config["Authentication:Keycloak:Realm"]}/protocol/openid-connect/certs";
            }

            var response = await client.GetAsync(jwksUrl);
            if (response.Content == null)
            {
                Log.Error("Không thể kết nối đến Identity Server");
                throw new Exception("Không thể kết nối đến Identity Server");
            }

            var jwks = await response.Content.ReadAsStringAsync();

            if (string.IsNullOrEmpty(jwks))
            {
                Log.Error("Không thể lấy được thông tin jwks");
                throw new Exception("Không thể lấy được thông tin jwks");
            }

            return jwks;
        }
    }
}