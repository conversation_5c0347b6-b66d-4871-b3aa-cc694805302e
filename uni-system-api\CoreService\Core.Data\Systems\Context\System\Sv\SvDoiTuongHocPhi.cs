﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDoiTuongHocPhi")]
    public class SvDoiTuongHocPhi
    {

        public SvDoiTuongHocPhi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_doi_tuong_hoc_phi")]
        public int IdDoiTuongHocPhi { get; set; }

        [Column("Ten_doi_tuong_hoc_phi"), MaxLength(50)]
        public string DoiTuongHocPhi { get; set; }


    }
}
