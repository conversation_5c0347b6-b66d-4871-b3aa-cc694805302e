﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32922.545
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "solution-item", "solution-item", "{3C000DE9-DA66-47DF-AE3D-38D031D1E92E}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.gitignore = .gitignore
		.gitlab-ci.yml = .gitlab-ci.yml
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIGateway", "APIGateway", "{2F6F98E4-1E94-4F18-B15A-615DA1477F7D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIService", "APIService", "{F5D9D630-74A6-4B37-A5D3-960AB03321B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Leader.API", "APIService\Leader.API\Leader.API.csproj", "{B244EED5-D893-45CE-A2EE-17075863726B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CoreService", "CoreService", "{B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "LeaderService", "LeaderService", "{D8E56F85-79BD-4DFD-BDCF-70F0EAAFBF4C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Business", "uni-system-api\CoreService\Core.Business\Core.Business.csproj", "{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Data", "uni-system-api\CoreService\Core.Data\Core.Data.csproj", "{D0197539-5321-4924-B1A6-D0F2B2196967}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Shared", "uni-system-api\CoreService\Core.Shared\Core.Shared.csproj", "{9F35ECF9-F150-43D2-8577-7FCCFE40696B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.DataLog", "uni-system-api\CoreService\Core.DataLog\Core.DataLog.csproj", "{C4651B97-C6C0-45F0-9678-20295A43595E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.API.Shared", "uni-system-api\APIService\Core.API.Shared\Core.API.Shared.csproj", "{47131CC3-4025-430B-B02C-B0425F10C8C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "System.API", "uni-system-api\APIService\System.API\System.API.csproj", "{BBAB028A-BD85-4E8A-822E-591937DACAC8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebAPIGateway", "uni-system-api\APIGateway\WebAPIGateway\WebAPIGateway.csproj", "{1A410E03-5D3F-484B-8172-599E9FDFB368}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Leader.Business", "Leader.Business\Leader.Business.csproj", "{9E45C5BC-7897-41F1-9B7A-9B1618345FC6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Leader.Data", "Leader.Data\Leader.Data.csproj", "{6FFA73E1-3FFC-40B7-BE9A-356563E63B00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Leader.Shared", "Leader.Shared\Leader.Shared.csproj", "{089B4F6A-AFD5-42F8-958F-DF477CF5A314}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "db_script", "db_script", "{C689736F-C953-4167-8961-01BC3082869C}"
	ProjectSection(SolutionItems) = preProject
		2025-05-07-them-bang-tkbBacDaoTao.sql = 2025-05-07-them-bang-tkbBacDaoTao.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalClients", "ExternalClients", "{0459EAEC-1351-4171-A006-99242627BED3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HouHRMClient", "ExternalClients\HouHRMClient\HouHRMClient.csproj", "{57FF2A55-3F10-4612-8086-5ADAFBD38277}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B244EED5-D893-45CE-A2EE-17075863726B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B244EED5-D893-45CE-A2EE-17075863726B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B244EED5-D893-45CE-A2EE-17075863726B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B244EED5-D893-45CE-A2EE-17075863726B}.Release|Any CPU.Build.0 = Release|Any CPU
		{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0197539-5321-4924-B1A6-D0F2B2196967}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0197539-5321-4924-B1A6-D0F2B2196967}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0197539-5321-4924-B1A6-D0F2B2196967}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0197539-5321-4924-B1A6-D0F2B2196967}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F35ECF9-F150-43D2-8577-7FCCFE40696B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F35ECF9-F150-43D2-8577-7FCCFE40696B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F35ECF9-F150-43D2-8577-7FCCFE40696B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F35ECF9-F150-43D2-8577-7FCCFE40696B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4651B97-C6C0-45F0-9678-20295A43595E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4651B97-C6C0-45F0-9678-20295A43595E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4651B97-C6C0-45F0-9678-20295A43595E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4651B97-C6C0-45F0-9678-20295A43595E}.Release|Any CPU.Build.0 = Release|Any CPU
		{47131CC3-4025-430B-B02C-B0425F10C8C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47131CC3-4025-430B-B02C-B0425F10C8C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47131CC3-4025-430B-B02C-B0425F10C8C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47131CC3-4025-430B-B02C-B0425F10C8C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBAB028A-BD85-4E8A-822E-591937DACAC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBAB028A-BD85-4E8A-822E-591937DACAC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBAB028A-BD85-4E8A-822E-591937DACAC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBAB028A-BD85-4E8A-822E-591937DACAC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A410E03-5D3F-484B-8172-599E9FDFB368}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A410E03-5D3F-484B-8172-599E9FDFB368}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A410E03-5D3F-484B-8172-599E9FDFB368}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A410E03-5D3F-484B-8172-599E9FDFB368}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E45C5BC-7897-41F1-9B7A-9B1618345FC6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E45C5BC-7897-41F1-9B7A-9B1618345FC6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E45C5BC-7897-41F1-9B7A-9B1618345FC6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E45C5BC-7897-41F1-9B7A-9B1618345FC6}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FFA73E1-3FFC-40B7-BE9A-356563E63B00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FFA73E1-3FFC-40B7-BE9A-356563E63B00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FFA73E1-3FFC-40B7-BE9A-356563E63B00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FFA73E1-3FFC-40B7-BE9A-356563E63B00}.Release|Any CPU.Build.0 = Release|Any CPU
		{089B4F6A-AFD5-42F8-958F-DF477CF5A314}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{089B4F6A-AFD5-42F8-958F-DF477CF5A314}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{089B4F6A-AFD5-42F8-958F-DF477CF5A314}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{089B4F6A-AFD5-42F8-958F-DF477CF5A314}.Release|Any CPU.Build.0 = Release|Any CPU
		{57FF2A55-3F10-4612-8086-5ADAFBD38277}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57FF2A55-3F10-4612-8086-5ADAFBD38277}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57FF2A55-3F10-4612-8086-5ADAFBD38277}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57FF2A55-3F10-4612-8086-5ADAFBD38277}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B244EED5-D893-45CE-A2EE-17075863726B} = {F5D9D630-74A6-4B37-A5D3-960AB03321B7}
		{861DA72D-8C4B-4D5D-AC87-DB25D0E9DA65} = {B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}
		{D0197539-5321-4924-B1A6-D0F2B2196967} = {B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}
		{9F35ECF9-F150-43D2-8577-7FCCFE40696B} = {B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}
		{C4651B97-C6C0-45F0-9678-20295A43595E} = {B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}
		{47131CC3-4025-430B-B02C-B0425F10C8C1} = {B4F0C779-B0DB-44EE-9F6E-53D13B5C47AB}
		{BBAB028A-BD85-4E8A-822E-591937DACAC8} = {F5D9D630-74A6-4B37-A5D3-960AB03321B7}
		{1A410E03-5D3F-484B-8172-599E9FDFB368} = {2F6F98E4-1E94-4F18-B15A-615DA1477F7D}
		{9E45C5BC-7897-41F1-9B7A-9B1618345FC6} = {D8E56F85-79BD-4DFD-BDCF-70F0EAAFBF4C}
		{6FFA73E1-3FFC-40B7-BE9A-356563E63B00} = {D8E56F85-79BD-4DFD-BDCF-70F0EAAFBF4C}
		{089B4F6A-AFD5-42F8-958F-DF477CF5A314} = {D8E56F85-79BD-4DFD-BDCF-70F0EAAFBF4C}
		{57FF2A55-3F10-4612-8086-5ADAFBD38277} = {0459EAEC-1351-4171-A006-99242627BED3}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {50B04301-C84D-4A31-A458-81DF35F2F6F7}
	EndGlobalSection
EndGlobal
