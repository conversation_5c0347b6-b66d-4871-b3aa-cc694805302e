﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class TangSelectItemModel
    {
        public int IdTang { get; set; }
        public string MaTang { get; set; }
        public string TenTang { get; set; }
    }

    public class TangBaseModel
    {
        public int IdTang { get; set; }
        public string MaTang { get; set; }
        public string TenTang { get; set; }
    }


    public class TangModel : TangBaseModel
    {

    }

    public class TangFilterModel : BaseQueryFilterModel
    {
        public TangFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTang";
        }
    }

    public class CreateTangModel
    {
        [Required(ErrorMessage = "Tang.IdTang.NotRequire")]
        public int IdTang { get; set; }

        [MaxLength(5, ErrorMessage = "Tang.MaTang.MaxLength(5)")]
        [Required(ErrorMessage = "Tang.MaTang.NotRequire")]
        public string MaTang { get; set; }

        [MaxLength(50, ErrorMessage = "Tang.TenTang.MaxLength(50)")]
        [Required(ErrorMessage = "Tang.TenTang.NotRequire")]
        public string TenTang { get; set; }

    }

    public class CreateManyTangModel
    {
        public List<CreateTangModel> listTangModels { get; set; }
    }

    public class UpdateTangModel : CreateTangModel
    {
        public void UpdateEntity(TkbTang input)
        {
            input.IdTang = IdTang;
            input.MaTang = MaTang;
            input.TenTang = TenTang;


        }
    }
}
