﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiKhenThuongSelectItemModel
    {
        public int IdLoaiKhenThuong { get; set; }
        public int IdCap { get; set; }
        public string LoaiKhenThuong { get; set; }
    }

    public class LoaiKhenThuongBaseModel
    {
        public int IdLoaiKhenThuong { get; set; }
        public int IdCap { get; set; }
        public string TenCap { get; set; }
        public string LoaiKhenThuong { get; set; }
        public float DiemThuong { get; set; }
    }


    public class LoaiKhenThuongModel : LoaiKhenThuongBaseModel
    {

    }

    public class LoaiKhenThuongFilterModel : BaseQueryFilterModel
    {
        public LoaiKhenThuongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdLoaiKhenThuong";
        }
    }

    public class CreateLoaiKhenThuongModel
    {
        [Required(ErrorMessage = "LoaiKhenThuong.IdLoaiKhenThuong.NotRequire")]
        public int IdLoaiKhenThuong { get; set; }

        [Required(ErrorMessage = "LoaiKhenThuong.IdCap.NotRequire")]
        public int IdCap { get; set; }

        [MaxLength(100, ErrorMessage = "LoaiKhenThuong.LoaiKhenThuong.MaxLength(100)")]
        [Required(ErrorMessage = "LoaiKhenThuong.LoaiKhenThuong.NotRequire")]
        public string LoaiKhenThuong { get; set; }

        [Required(ErrorMessage = "LoaiKhenThuong.DiemThuong.NotRequire")]
        public float DiemThuong { get; set; }

    }

    public class CreateManyLoaiKhenThuongModel
    {
        public List<CreateLoaiKhenThuongModel> listLoaiKhenThuongModels { get; set; }
    }

    public class UpdateLoaiKhenThuongModel : CreateLoaiKhenThuongModel
    {
        public void UpdateEntity(SvLoaiKhenThuong input)
        {
            input.IdLoaiKhenThuong = IdLoaiKhenThuong;
            input.IdCap = IdCap;
            input.LoaiKhenThuong = LoaiKhenThuong;
            input.DiemThuong = DiemThuong;

        }
    }
}
