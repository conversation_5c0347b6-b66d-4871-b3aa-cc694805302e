﻿using Core.Business.Workflow;
using Core.Data;
using Core.Shared.ContextAccessor;
using MediatR;
using Minio.DataModel;
using OptimaJet.Workflow.Core.Parser;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetInfoWorkFlowQuery : IRequest<GetInfoWorkFlowModel>
    {
        public Guid ProcessId { get; set; }

        /// <summary>
        /// Lấy command tiếp theo trong quy trình
        /// </summary>
        /// <param name="workflowName"></param>
        public GetInfoWorkFlowQuery(Guid processId)
        {
            ProcessId = processId;
        }

        public class Handler : IRequestHandler<GetInfoWorkFlowQuery, GetInfoWorkFlowModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<GetInfoWorkFlowModel> Handle(GetInfoWorkFlowQuery request, CancellationToken cancellationToken)
            {
                var result = new GetInfoWorkFlowModel();

                var processId = request.ProcessId;
                string userId = _contextAccessor.UserId.Value.ToString();

                var workflowState = await WorkflowInit.Runtime.GetCurrentStateAsync(processId);
                result.State = workflowState.Name;

                // Commands
                var commands = WorkflowInit.Runtime.GetAvailableCommands(processId, userId);
                result.CommandAvailables = new List<ProcessWorkFlowCommandModel>();
                foreach (var workflowCommand in commands)
                {
                    
                    if (result.CommandAvailables.Count(c => c.key == workflowCommand.CommandName) == 0)
                        result.CommandAvailables.Add(new ProcessWorkFlowCommandModel() { key = workflowCommand.CommandName, value = workflowCommand.LocalizedName, Classifier = workflowCommand.Classifier, Params = workflowCommand.Parameters.Select(x => x.ParameterName).ToList() });
                }

                return result;
            }
        }
    }
}
