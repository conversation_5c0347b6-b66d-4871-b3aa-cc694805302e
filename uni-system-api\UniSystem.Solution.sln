﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32922.545
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "solution-item", "solution-item", "{3C000DE9-DA66-47DF-AE3D-38D031D1E92E}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.gitignore = .gitignore
		.gitlab-ci.yml = .gitlab-ci.yml
		dockerfile_system_api = dockerfile_system_api
		dockerfile_web_api_gw = dockerfile_web_api_gw
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIGateway", "APIGateway", "{2F6F98E4-1E94-4F18-B15A-615DA1477F7D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIService", "APIService", "{F5D9D630-74A6-4B37-A5D3-960AB03321B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Business", "CoreService\Core.Business\Core.Business.csproj", "{A9EB8EEA-B52F-4EA9-853C-3389C41FC3FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Data", "CoreService\Core.Data\Core.Data.csproj", "{42FD9A4C-ACAA-413B-9087-696251009BEB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.DataLog", "CoreService\Core.DataLog\Core.DataLog.csproj", "{D2CE95E0-DAA5-4874-9517-56F189ED1E30}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Shared", "CoreService\Core.Shared\Core.Shared.csproj", "{BA5704C5-D094-43C3-B556-2834934CE779}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebAPIGateway", "APIGateway\WebAPIGateway\WebAPIGateway.csproj", "{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "System.API", "APIService\System.API\System.API.csproj", "{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.API.Shared", "APIService\Core.API.Shared\Core.API.Shared.csproj", "{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{84BCD945-B388-4B9E-BB01-6260F53EA9D2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Business.Tests", "Core.Business.Test\Core.Business.Tests.csproj", "{DB0E2155-641A-41ED-9740-72FAE2E56CA1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{BC050A4E-6861-4C51-9E14-97BA6C6B9A8A}"
	ProjectSection(SolutionItems) = preProject
		UnitTestGuide.md = UnitTestGuide.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A9EB8EEA-B52F-4EA9-853C-3389C41FC3FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9EB8EEA-B52F-4EA9-853C-3389C41FC3FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9EB8EEA-B52F-4EA9-853C-3389C41FC3FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9EB8EEA-B52F-4EA9-853C-3389C41FC3FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{42FD9A4C-ACAA-413B-9087-696251009BEB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42FD9A4C-ACAA-413B-9087-696251009BEB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42FD9A4C-ACAA-413B-9087-696251009BEB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42FD9A4C-ACAA-413B-9087-696251009BEB}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2CE95E0-DAA5-4874-9517-56F189ED1E30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2CE95E0-DAA5-4874-9517-56F189ED1E30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2CE95E0-DAA5-4874-9517-56F189ED1E30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2CE95E0-DAA5-4874-9517-56F189ED1E30}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA5704C5-D094-43C3-B556-2834934CE779}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA5704C5-D094-43C3-B556-2834934CE779}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA5704C5-D094-43C3-B556-2834934CE779}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA5704C5-D094-43C3-B556-2834934CE779}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351}.Release|Any CPU.Build.0 = Release|Any CPU
		{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB0E2155-641A-41ED-9740-72FAE2E56CA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB0E2155-641A-41ED-9740-72FAE2E56CA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB0E2155-641A-41ED-9740-72FAE2E56CA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB0E2155-641A-41ED-9740-72FAE2E56CA1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3FBD1F38-7652-45E1-A5AF-1CE39DBF8B1D} = {2F6F98E4-1E94-4F18-B15A-615DA1477F7D}
		{1E92790C-DA58-4F3E-BCB1-AF33A8C7E351} = {F5D9D630-74A6-4B37-A5D3-960AB03321B7}
		{2ED7DA37-A8CA-41FE-953F-06F84E8C09D2} = {F5D9D630-74A6-4B37-A5D3-960AB03321B7}
		{DB0E2155-641A-41ED-9740-72FAE2E56CA1} = {84BCD945-B388-4B9E-BB01-6260F53EA9D2}
		{BC050A4E-6861-4C51-9E14-97BA6C6B9A8A} = {84BCD945-B388-4B9E-BB01-6260F53EA9D2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {50B04301-C84D-4A31-A458-81DF35F2F6F7}
	EndGlobalSection
EndGlobal
