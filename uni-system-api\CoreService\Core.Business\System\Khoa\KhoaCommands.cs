﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateKhoaCommand : IRequest<Unit>
    {
        public CreateKhoaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateKhoaCommand(CreateKhoaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateKhoaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateKhoaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {KhoaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateKhoaModel, SvKhoa>(model);

                var checkCode = await _dataContext.SvKhoas.AnyAsync(x => x.IdKhoa == entity.IdKhoa || x.TenKhoa == entity.TenKhoa || x.MaKhoa == entity.MaKhoa);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Khoa.Existed", entity.TenKhoa.ToString()]}");
                }

                await _dataContext.SvKhoas.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {KhoaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới khoa: {entity.TenKhoa}",
                    ObjectCode = KhoaConstant.CachePrefix,
                    ObjectId = entity.IdKhoa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyKhoaCommand : IRequest<Unit>
    {
        public CreateManyKhoaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyKhoaCommand(CreateManyKhoaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyKhoaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyKhoaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {KhoaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listKhoaAdd = model.listKhoaModels.Select(x => x.TenKhoa).ToList();
                var listMaKhoaAdd = model.listKhoaModels.Select(x => x.MaKhoa).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyKhoaModel, SvKhoa>(model);

                // Check data duplicate
                if (listKhoaAdd.Count() != listKhoaAdd.Distinct().Count() || listMaKhoaAdd.Count() != listMaKhoaAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvKhoas.AnyAsync(x => listKhoaAdd.Contains(x.TenKhoa)) || await _dataContext.SvKhoas.AnyAsync(x => listMaKhoaAdd.Contains(x.MaKhoa)))
                {
                    throw new ArgumentException($"{_localizer["Khoa.Existed"]}");
                }

                var listEntity = model.listKhoaModels.Select(x => new SvKhoa()
                {
                    IdKhoa = x.IdKhoa,
                    MaKhoa = x.MaKhoa,
                    TenKhoa = x.TenKhoa,
                    TenKhoaEn = x.TenKhoaEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdKhoa).ToList();

                Log.Information($"Create many {KhoaConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import khoa từ file excel",
                    ObjectCode = KhoaConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(KhoaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateKhoaCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateKhoaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateKhoaCommand(int id, UpdateKhoaModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateKhoaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateKhoaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {KhoaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvKhoas.FirstOrDefaultAsync(dt => dt.IdKhoa == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvKhoas.AnyAsync(x => (x.TenKhoa == model.TenKhoa || x.MaKhoa == model.MaKhoa) && x.IdKhoa != model.IdKhoa);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Khoa.Existed", model.TenKhoa.ToString()]}");
                }

                Log.Information($"Before Update {KhoaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvKhoas.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {KhoaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {KhoaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật khoa: {entity.TenKhoa}",
                    ObjectCode = KhoaConstant.CachePrefix,
                    ObjectId = entity.IdKhoa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoaConstant.BuildCacheKey(entity.IdKhoa.ToString()));
                _cacheService.Remove(KhoaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteKhoaCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteKhoaCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteKhoaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteKhoaCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {KhoaConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvKhoas.FirstOrDefaultAsync(x => x.IdKhoa == id);

                _dataContext.SvKhoas.Remove(entity);

                Log.Information($"Delete {KhoaConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa khoa: {entity.TenKhoa}",
                    ObjectCode = KhoaConstant.CachePrefix,
                    ObjectId = entity.IdKhoa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoaConstant.BuildCacheKey());
                _cacheService.Remove(KhoaConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
