﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


        public class CreateXepLoaiHocTapThangDiem10Command : IRequest<Unit>
        {
            public CreateXepLoaiHocTapThangDiem10Model Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateXepLoaiHocTapThangDiem10Command(CreateXepLoaiHocTapThangDiem10Model model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateXepLoaiHocTapThangDiem10Command, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateXepLoaiHocTapThangDiem10Command request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {XepLoaiHocTapThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateXepLoaiHocTapThangDiem10Model, SvXepLoaiHocTapThangDiem10>(model);

                    var checkCode = await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => x.XepLoai == entity.XepLoai && x.IdHe == entity.IdHe);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["XepLoai.Existed", entity.XepLoai.ToString()]}");
                    }

                     var checkCodeMaXepLoai = await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => x.MaXepLoai == entity.MaXepLoai && x.IdHe == entity.IdHe);
                     if (checkCodeMaXepLoai)
                     {
                        throw new ArgumentException($"{_localizer["MaXepLoai.Existed", entity.MaXepLoai.ToString()]}");
                     }

                await _dataContext.SvXepLoaiHocTapThangDiem10s.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {XepLoaiHocTapThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới Xếp loại học tập thang 10: {entity.XepLoai}",
                        ObjectCode = XepLoaiHocTapThangDiem10Constant.CachePrefix,
                        ObjectId = entity.IdXepLoai.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class CreateManyXepLoaiHocTapThangDiem10Command : IRequest<Unit>
        {
            public CreateManyXepLoaiHocTapThangDiem10Model Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateManyXepLoaiHocTapThangDiem10Command(CreateManyXepLoaiHocTapThangDiem10Model model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateManyXepLoaiHocTapThangDiem10Command, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateManyXepLoaiHocTapThangDiem10Command request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create many {XepLoaiHocTapThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var listXepLoaiAdd = model.listXepLoaiHocTapThangDiem10Models.Select(x => x.XepLoai).ToList();
                    var listMaXepLoaiAdd = model.listXepLoaiHocTapThangDiem10Models.Select(x => x.MaXepLoai).ToList();
                    var listIdHeAdd = model.listXepLoaiHocTapThangDiem10Models.Select(x => x.IdHe).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiHocTapThangDiem10Model, SvXepLoaiHocTapThangDiem10>(model);

                    // Check data duplicate
                    if (listXepLoaiAdd.Count() != listXepLoaiAdd.Distinct().Count() || listMaXepLoaiAdd.Count() != listMaXepLoaiAdd.Distinct().Count())
                    {
                        throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                    }
                // Check data exits DB
                     if (await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => listXepLoaiAdd.Contains(x.XepLoai))
                        && await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                     {
                        throw new ArgumentException($"{_localizer["XepLoai.Existed"]}");
                     }

                     if (await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => listMaXepLoaiAdd.Contains(x.MaXepLoai))
                      && await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                     {
                        throw new ArgumentException($"{_localizer["MaXepLoai.Existed"]}");
                     }

                var listEntity = model.listXepLoaiHocTapThangDiem10Models.Select(x => new SvXepLoaiHocTapThangDiem10()
                    {
                        XepLoai = x.XepLoai,
                        TuDiem = x.TuDiem,
                        DenDiem = x.DenDiem,
                        MaXepLoai = x.MaXepLoai,
                        XepLoaiEn = x.XepLoaiEn,
                        IdHe = x.IdHe


                    }).ToList();

                    await _dataContext.AddRangeAsync(listEntity);
                    await _dataContext.SaveChangesAsync();

                    var createdIds = listEntity.Select(e => e.IdXepLoai).ToList();

                    Log.Information($"Create many {XepLoaiHocTapThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Import Xếp loại học tập thang 10 từ file excel",
                        ObjectCode = XepLoaiHocTapThangDiem10Constant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(createdIds)
                    });

                    //Xóa cache
                    _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class UpdateXepLoaiHocTapThangDiem10Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiHocTapThangDiem10Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiHocTapThangDiem10Command(int id, UpdateXepLoaiHocTapThangDiem10Model model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiHocTapThangDiem10Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiHocTapThangDiem10Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiHocTapThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepLoaiHocTapThangDiem10s.FirstOrDefaultAsync(dt => dt.IdXepLoai == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               

                var checkCode = await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => x.IdXepLoai != entity.IdXepLoai && x.XepLoai == model.XepLoai && x.IdHe == model.IdHe  );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoai.Existed", model.XepLoai.ToString()]}");
                }

                var checkCodeMaXepLoai = await _dataContext.SvXepLoaiHocTapThangDiem10s.AnyAsync(x => x.IdXepLoai != entity.IdXepLoai && x.MaXepLoai == model.MaXepLoai && x.IdHe == model.IdHe );
                if (checkCodeMaXepLoai)
                {
                    throw new ArgumentException($"{_localizer["MaXepLoai.Existed", model.MaXepLoai.ToString()]}");
                }

                Log.Information($"Before Update {XepLoaiHocTapThangDiem10Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepLoaiHocTapThangDiem10s.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiHocTapThangDiem10Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiHocTapThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp loại học tập thang 10: {entity.XepLoai}",
                    ObjectCode = XepLoaiHocTapThangDiem10Constant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey(entity.IdXepLoai.ToString()));
                _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiHocTapThangDiem10Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiHocTapThangDiem10Command(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiHocTapThangDiem10Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiHocTapThangDiem10Command request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiHocTapThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepLoaiHocTapThangDiem10s.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                _dataContext.SvXepLoaiHocTapThangDiem10s.Remove(entity);

                Log.Information($"Delete {XepLoaiHocTapThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp loại học tập thang 10: {entity.XepLoai}",
                    ObjectCode = XepLoaiHocTapThangDiem10Constant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey());
                _cacheService.Remove(XepLoaiHocTapThangDiem10Constant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
