﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/benh-vien")]
    [ApiExplorerSettings(GroupName = "112. Bệnh viện")]
    [Authorize]
    public class BenhVienController : ApiControllerBase
    {
        public BenhVienController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách phương án cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<BenhVienSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxBenhVienQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phương án có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<BenhVienBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BENH_VIEN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] BenhVienQueryFilter filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterBenhVienQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<BenhVienModel>), StatusCodes.Status200OK)]
       // [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BENH_VIEN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetBenhVienByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới bênh viện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BENH_VIEN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateBenhVienModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BENH_VIEN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BENH_VIEN_CREATE;


                return await _mediator.Send(new CreateBenhVienCommand(model, u));
            });
        }

      
        /// <summary>
        /// Sửa bênh viện
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{request.id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BENH_VIEN_EDIT))]
        public async Task<IActionResult> Update( [FromBody] UpdateBenhVienModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BENH_VIEN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BENH_VIEN_UPDATE;
                return await _mediator.Send(new UpdateBenhVienCommand(request, u));
            });
        }

        /// <summary>
        /// Xóa bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BENH_VIEN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BENH_VIEN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BENH_VIEN_DELETE;

                return await _mediator.Send(new DeleteBenhVienCommand(id, u));
            });
        }

    }
}
