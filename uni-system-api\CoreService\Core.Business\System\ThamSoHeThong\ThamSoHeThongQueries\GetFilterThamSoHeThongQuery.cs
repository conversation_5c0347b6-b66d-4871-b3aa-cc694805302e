using Core.Business.System;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterThamSoHeThongQuery : IRequest<PaginationList<ThamSoHeThongModel>>
    {
        public ThamSoHeThongQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tham số hệ thống có lọc và phân trang
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterThamSoHeThongQuery(ThamSoHeThongQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterThamSoHeThongQuery, PaginationList<ThamSoHeThongModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ThamSoHeThongModel>> Handle(GetFilterThamSoHeThongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var lstIdThamSo = WorkFlowSeedConstants.WorkflowNameMapFunction.Keys.ToList();

                var query = from dt in _dataContext.HtThamSoHeThongs.AsNoTracking().AsQueryable()
                              join ph in _dataContext.HtPhanHes.AsNoTracking().AsQueryable() on dt.IdPh equals ph.IdPh
                              select new ThamSoHeThongModel
                              {
                                  IdPh = dt.IdPh,
                                  PhanHe = ph.PhanHe,
                                  DateModify = dt.DateModify,
                                  NhomThamSo = dt.NhomThamSo,
                                  IdThamSo = dt.IdThamSo,
                                  GiaTri = dt.GiaTri,
                                  TenThamSo = dt.TenThamSo,
                                  GhiChu = dt.GhiChu,
                                  UserName = dt.UserName,
                                  Active = dt.Active
                              };

                // Apply filters
                if (filter.IdPh.HasValue)
                {
                    query = query.Where(x => x.IdPh == filter.IdPh.Value);
                }

                if (!string.IsNullOrEmpty(filter.NhomThamSo))
                {
                    query = query.Where(x => x.NhomThamSo.Contains(filter.NhomThamSo));
                }

                if (filter.Active.HasValue)
                {
                    query = query.Where(x => x.Active == filter.Active.Value);
                }

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    var textSearch = filter.TextSearch.ToLower().Trim();
                    query = query.Where(x => x.IdThamSo.ToLower().Contains(textSearch) ||
                                           x.TenThamSo.ToLower().Contains(textSearch) ||
                                           x.GiaTri.ToLower().Contains(textSearch) ||
                                           x.NhomThamSo.ToLower().Contains(textSearch));
                }

                if (filter.FilterThamSoWorkFlow)
                    query = query.Where(x => lstIdThamSo.Contains(x.IdThamSo));

                // Apply sorting
                if (!string.IsNullOrEmpty(filter.PropertyName))
                {
                    query = query.OrderByField(filter.PropertyName, filter.Ascending);
                }
                else
                {
                    query = query.OrderBy(x => x.IdThamSo);
                }

                var totalCount = await query.CountAsync();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                var items = await query.Skip(excludedRows)
                                     .Take(filter.PageSize)
                                     .ToListAsync();

                return new PaginationList<ThamSoHeThongModel>()
                {
                    DataCount = items.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = items
                };
            }
        }
    }
}
