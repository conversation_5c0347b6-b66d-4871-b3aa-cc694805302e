using Microsoft.Extensions.DependencyInjection;
using Core.Business.Core;
using Leader.Business;

namespace Core.API
{
    public static class CustomServiceCollection
    {
        /// <summary>
        /// RegisterCustomService
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCustomServiceComponents(this IServiceCollection services)
        {
            // services.AddTransient<IElasticHandler, ElasticHandler>();
            services.AddTransient<ISendMailHandler, SendMailHandler>();
            services.AddTransient<IRedisHandler, RedisHandler>();
            services.AddTransient<ILeaderCallStoreHelper, LeaderCallStoreHelper>();

            return services;
        }
    }
}