﻿using MediatR;
using Core.Shared;
using Serilog;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;

namespace Core.Business.Core
{
    #region Send message to SignalR by Hub
    public class SendNotificationToUserByHubCommand : IRequest<Unit>
    {
        public NotificationModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> thông báo đến Client trực tiếp qua Hub
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendNotificationToUserByHubCommand(NotificationModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendNotificationToUserByHubCommand, Unit>
        {
            private readonly IHubContext<SignalRHub> _hub;

            public Handler(IHubContext<SignalRHub> hub)
            {
                _hub = hub;
            }

            public async Task<Unit> Handle(SendNotificationToUserByHubCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Send Notification from BE to User {request.Data.UserId}");

                await _hub.Clients.User(request.Data.UserId.ToString()).SendAsync(SignalRMethodEnum.SystemReceiveNotify.ToString(), JsonSerializer.Serialize(request.Data.Data));

                request.RequestUser.SystemLog.ListAction.Add(new ActionDetail("Gửi thông báo đến Client qua Hub"));

                return Unit.Value;
            }
        }
    }

    public class SendBroadcastNotificationByHubCommand : IRequest<Unit>
    {
        public NotificationBroadcastModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Gửi thông báo đến tất cả client trực tiếp qua Hub
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendBroadcastNotificationByHubCommand(NotificationBroadcastModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendBroadcastNotificationByHubCommand, Unit>
        {
            private readonly IHubContext<SignalRHub> _hub;

            public Handler(IHubContext<SignalRHub> hub)
            {
                _hub = hub;
            }

            public async Task<Unit> Handle(SendBroadcastNotificationByHubCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Send Broadcast Notification from BE");

                await _hub.Clients.All.SendAsync(SignalRMethodEnum.SystemReceiveNotify.ToString(), JsonSerializer.Serialize(request.Data.Data));

                return Unit.Value;
            }
        }
    }
    #endregion

    #region Send message to SignalR by Message Queue
    public class SendNotificationToUserByMQCommand : IRequest<Unit>
    {
        public NotificationModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Gửi thông báo đến Client qua queue (dùng cho các service kết nối để gửi thông báo đến client)
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendNotificationToUserByMQCommand(NotificationModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendNotificationToUserByMQCommand, Unit>
        {
            private readonly IConfiguration _config;

            public Handler(IConfiguration config)
            {
                _config = config;
            }

            public async Task<Unit> Handle(SendNotificationToUserByMQCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"PublishMessage Notification from BE to User by MQ: {request.Data.UserId}");

                RabbitMQUtils.PublishMessage(_config["RabbitMQ:Uri"], typeof(NotificationModel), request.Data, request.RequestUser.SystemLog.TraceId, request.RequestUser.UserId);
                
                request.RequestUser.SystemLog.ListAction.Add(new ActionDetail("Gửi thông báo đến Client qua MQ"));

                return Unit.Value;
            }
        }
    }

    public class SendBroadcastNotificationByMQCommand : IRequest<Unit>
    {
        public NotificationBroadcastModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Gửi thông báo đến tất cả client trực tiếp qua Hub
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendBroadcastNotificationByMQCommand(NotificationBroadcastModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendBroadcastNotificationByMQCommand, Unit>
        {
            private readonly IConfiguration _config;

            public Handler(IConfiguration config)
            {
                _config = config;
            }

            public async Task<Unit> Handle(SendBroadcastNotificationByMQCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"PublishMessage Broadcast Notification from BE by MQ");

                RabbitMQUtils.PublishMessage(_config["RabbitMQ:Uri"], typeof(NotificationBroadcastModel), request.Data, request.RequestUser.SystemLog.TraceId, request.RequestUser.UserId);

                return Unit.Value;
            }
        }
    }
    #endregion

    #region Teacher message
    public class SendTeacherNotificationToUserByHubCommand : IRequest<Unit>
    {
        public TeacherNotificationModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Gửi thông báo đến Client trực tiếp qua Hub
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendTeacherNotificationToUserByHubCommand(TeacherNotificationModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendTeacherNotificationToUserByHubCommand, Unit>
        {
            private readonly IHubContext<SignalRHub> _hub;

            public Handler(IHubContext<SignalRHub> hub)
            {
                _hub = hub;
            }

            public async Task<Unit> Handle(SendTeacherNotificationToUserByHubCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Send Teacher Notification from BE to User {request.Data.UserId}");

                await _hub.Clients.User(request.Data.UserId.ToString()).SendAsync(SignalRMethodEnum.TeacherReceiveNotify.ToString(), JsonSerializer.Serialize(request.Data.Data));

                return Unit.Value;
            }
        }
    }
   
    public class SendTeacherNotificationToUserByMQCommand : IRequest<Unit>
    {
        public TeacherNotificationModel Data { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Gửi thông báo đến Client qua queue (dùng cho các service kết nối để gửi thông báo đến client)
        /// </summary>
        /// <param name="data">Object cần gửi thông báo</param>
        /// <param name="u">Thông tin người dùng</param>
        public SendTeacherNotificationToUserByMQCommand(TeacherNotificationModel data, RequestUser u)
        {
            Data = data;
            RequestUser = u;
        }

        public class Handler : IRequestHandler<SendTeacherNotificationToUserByMQCommand, Unit>
        {
            private readonly IConfiguration _config;

            public Handler(IConfiguration config)
            {
                _config = config;
            }

            public async Task<Unit> Handle(SendTeacherNotificationToUserByMQCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"PublishMessage Teacher Notification from BE to User by MQ: {request.Data.UserId}");

                RabbitMQUtils.PublishMessage(_config["RabbitMQ:Uri"], typeof(TeacherNotificationModel), request.Data, request.RequestUser.SystemLog.TraceId, request.RequestUser.UserId);

                return Unit.Value;
            }
        }
    }
    #endregion
}