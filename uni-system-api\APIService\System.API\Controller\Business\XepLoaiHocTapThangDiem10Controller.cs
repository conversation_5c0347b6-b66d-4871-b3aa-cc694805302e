﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-loai-hoc-tap-thang-diem-10")]
    [ApiExplorerSettings(GroupName = "67. Xếp loại học tập thang điểm 10")]
    [Authorize]
    public class XepLoaiHocTapThangDiem10Controller : ApiControllerBase
    {
        public XepLoaiHocTapThangDiem10Controller(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách Xếp loại học tập thang điểm 10 cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiHocTapThangDiem10SelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiHocTapThangDiem10Query(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang điểm 10 có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepLoaiHocTapThangDiem10BaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepLoaiHocTapThangDiem10FilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepLoaiHocTapThangDiem10Query(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Xếp loại học tập thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepLoaiHocTapThangDiem10Model>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepLoaiHocTapThangDiem10ByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Xếp loại học tập thang điểm 10
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepLoaiHocTapThangDiem10Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE;


                return await _mediator.Send(new CreateXepLoaiHocTapThangDiem10Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Xếp loại học tập thang điểm 10
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepLoaiHocTapThangDiem10Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepLoaiHocTapThangDiem10Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Xếp loại học tập thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepLoaiHocTapThangDiem10Model request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_UPDATE;
                return await _mediator.Send(new UpdateXepLoaiHocTapThangDiem10Command(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Xếp loại học tập thang điểm 10
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_HOC_TAP_THANG_DIEM_10_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_DELETE;

                return await _mediator.Send(new DeleteXepLoaiHocTapThangDiem10Command(id, u.SystemLog));
            });
        }

    }
}
