﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateDanTocCommand : IRequest<Unit>
    {
        public CreateDanTocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDanTocCommand(CreateDanTocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDanTocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDanTocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DanTocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDanTocModel, SvDanToc>(model);

                var checkCode = await _dataContext.SvDanTocs.AnyAsync(x => x.IdDanToc == entity.IdDanToc || x.DanToc == entity.DanToc || x.MaDanToc == entity.MaDanToc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DanToc.Existed", entity.DanToc.ToString()]}");
                }

                await _dataContext.SvDanTocs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DanTocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới dân tộc: {entity.DanToc}",
                    ObjectCode = DanTocConstant.CachePrefix,
                    ObjectId = entity.IdDanToc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DanTocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDanTocCommand : IRequest<Unit>
    {
        public CreateManyDanTocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDanTocCommand(CreateManyDanTocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDanTocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDanTocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DanTocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDanTocAdd = model.listDanTocModels.Select(x => x.DanToc).ToList();
                var listMaDanTocAdd = model.listDanTocModels.Select(x => x.MaDanToc).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDanTocModel, SvDanToc>(model);

                // Check data duplicate
                if (listDanTocAdd.Count() != listDanTocAdd.Distinct().Count() || listMaDanTocAdd.Count() != listMaDanTocAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvDanTocs.AnyAsync(x => listDanTocAdd.Contains(x.DanToc)) || await _dataContext.SvDanTocs.AnyAsync(x => listMaDanTocAdd.Contains(x.MaDanToc)))
                {
                    throw new ArgumentException($"{_localizer["DanToc.Existed"]}");
                }

                var listEntity = model.listDanTocModels.Select(x => new SvDanToc()
                {
                    IdDanToc = x.IdDanToc,
                    MaDanToc = x.MaDanToc,
                    DanToc = x.DanToc,
                    DanTocEn = x.DanTocEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdDanToc).ToList();

                Log.Information($"Create many {DanTocConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import dân tộc từ file excel",
                    ObjectCode = DanTocConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DanTocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDanTocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDanTocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDanTocCommand(int id, UpdateDanTocModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDanTocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDanTocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DanTocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDanTocs.FirstOrDefaultAsync(dt => dt.IdDanToc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvDanTocs.AnyAsync(x => (x.DanToc == model.DanToc || x.MaDanToc == model.MaDanToc) && x.IdDanToc != model.IdDanToc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DanToc.Existed", model.DanToc.ToString()]}");
                }

                Log.Information($"Before Update {DanTocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDanTocs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DanTocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DanTocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật dân tộc: {entity.DanToc}",
                    ObjectCode = DanTocConstant.CachePrefix,
                    ObjectId = entity.IdDanToc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DanTocConstant.BuildCacheKey(entity.IdDanToc.ToString()));
                _cacheService.Remove(DanTocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDanTocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDanTocCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDanTocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDanTocCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DanTocConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDanTocs.FirstOrDefaultAsync(x => x.IdDanToc == id);

                _dataContext.SvDanTocs.Remove(entity);

                Log.Information($"Delete {DanTocConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa dân tộc: {entity.DanToc}",
                    ObjectCode = DanTocConstant.CachePrefix,
                    ObjectId = entity.IdDanToc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DanTocConstant.BuildCacheKey());
                _cacheService.Remove(DanTocConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
