﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxXepLoaiChungChiQuery : IRequest<List<XepLoaiChungChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">S<PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiChungChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiChungChiQuery, List<XepLoaiChungChiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiChungChiSelectItemModel>> Handle(GetComboboxXepLoaiChungChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiChungChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepLoaiChungChis.OrderBy(x => x.XepHang)
                                select new XepLoaiChungChiSelectItemModel()
                                {
                                    IdXepHang = dt.IdXepHang,
                                    XepHang = dt.XepHang,
                                    XepHangEn = dt.XepHangEn
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepHang.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiChungChiQuery : IRequest<PaginationList<XepLoaiChungChiBaseModel>>
    {
        public XepLoaiChungChiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại chứng chỉ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiChungChiQuery(XepLoaiChungChiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiChungChiQuery, PaginationList<XepLoaiChungChiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiChungChiBaseModel>> Handle(GetFilterXepLoaiChungChiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepLoaiChungChis
                            select new XepLoaiChungChiBaseModel
                            {
                                IdXepHang = dt.IdXepHang,
                                XepHang = dt.XepHang,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                XepHangEn = dt.XepHangEn,
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepHang.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<XepLoaiChungChiBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiChungChiByIdQuery : IRequest<XepLoaiChungChiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại chứng chỉ theo id
        /// </summary>
        /// <param name="id">Id Xếp loại chứng chỉ</param>
        public GetXepLoaiChungChiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiChungChiByIdQuery, XepLoaiChungChiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiChungChiModel> Handle(GetXepLoaiChungChiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiChungChiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepLoaiChungChis.FirstOrDefaultAsync(x => x.IdXepHang == id);

                    return AutoMapperUtils.AutoMap<SvXepLoaiChungChi, XepLoaiChungChiModel>(entity);
                });
                return item;
            }
        }
    }
}
