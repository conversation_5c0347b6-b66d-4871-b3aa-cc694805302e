﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateHanhViCommand : IRequest<Unit>
    {
        public CreateHanhViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHanhViCommand(CreateHanhViModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHanhViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHanhViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HanhViKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHanhViModel, SvHanhVi>(model);

                var checkCode = await _dataContext.SvHanhVis.AnyAsync(x => x.IdHanhVi == entity.IdHanhVi || x.HanhVi == entity.HanhVi || x.MaHanhVi == entity.MaHanhVi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HanhVi.Existed", entity.HanhVi.ToString()]}");
                }

                await _dataContext.SvHanhVis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HanhViKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới hành vi kỷ luật: {entity.HanhVi}",
                    ObjectCode = HanhViKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdHanhVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHanhViCommand : IRequest<Unit>
    {
        public CreateManyHanhViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHanhViCommand(CreateManyHanhViModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHanhViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHanhViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HanhViKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHanhViAdd = model.listHanhViModels.Select(x => x.HanhVi).ToList();
                var listMaHanhViAdd = model.listHanhViModels.Select(x => x.MaHanhVi).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyHanhViModel, SvHanhVi>(model);

                // Check data duplicate
                if (listHanhViAdd.Count() != listHanhViAdd.Distinct().Count() || listMaHanhViAdd.Count() != listMaHanhViAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvHanhVis.AnyAsync(x => listHanhViAdd.Contains(x.HanhVi)) || await _dataContext.SvHanhVis.AnyAsync(x => listMaHanhViAdd.Contains(x.MaHanhVi)))
                {
                    throw new ArgumentException($"{_localizer["HanhVi.Existed"]}");
                }

                var listEntity = model.listHanhViModels.Select(x => new SvHanhVi()
                {
                    IdHanhVi = x.IdHanhVi,
                    MaHanhVi = x.MaHanhVi,
                    HanhVi = x.HanhVi

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHanhVi).ToList();

                Log.Information($"Create many {HanhViKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import hành vi kỷ luật từ file excel",
                    ObjectCode = HanhViKyLuatConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHanhViCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHanhViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHanhViCommand(int id, UpdateHanhViModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHanhViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHanhViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HanhViKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvHanhVis.FirstOrDefaultAsync(dt => dt.IdHanhVi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvHanhVis.AnyAsync(x => (x.HanhVi == model.HanhVi || x.MaHanhVi == model.MaHanhVi) && x.IdHanhVi != model.IdHanhVi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HanhVi.Existed", model.HanhVi.ToString()]}");
                }

                Log.Information($"Before Update {HanhViKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvHanhVis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HanhViKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HanhViKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật hành vi kỷ luật: {entity.HanhVi}",
                    ObjectCode = HanhViKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdHanhVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey(entity.IdHanhVi.ToString()));
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHanhViCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHanhViCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHanhViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHanhViCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HanhViKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvHanhVis.FirstOrDefaultAsync(x => x.IdHanhVi == id);

                _dataContext.SvHanhVis.Remove(entity);

                Log.Information($"Delete {HanhViKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa hành vi kỷ luật: {entity.HanhVi}",
                    ObjectCode = HanhViKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdHanhVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey());
                _cacheService.Remove(HanhViKyLuatConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
