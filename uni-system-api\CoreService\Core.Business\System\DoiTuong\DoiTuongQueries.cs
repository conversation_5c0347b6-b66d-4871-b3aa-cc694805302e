﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxDoiTuongQuery : IRequest<List<DoiTuongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy đối tượng cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDoiTuongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDoiTuongQuery, List<DoiTuongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DoiTuongSelectItemModel>> Handle(GetComboboxDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DoiTuongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDoiTuongs.OrderBy(x => x.TenDoiTuong)
                                select new DoiTuongSelectItemModel()
                                {
                                    IdDoiTuong = dt.IdDoiTuong,
                                    MaDoiTuong = dt.MaDoiTuong,
                                    TenDoiTuong = dt.TenDoiTuong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenDoiTuong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDoiTuongQuery : IRequest<PaginationList<DoiTuongBaseModel>>
    {
        public DoiTuongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách đối tượng có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDoiTuongQuery(DoiTuongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDoiTuongQuery, PaginationList<DoiTuongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DoiTuongBaseModel>> Handle(GetFilterDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDoiTuongs
                            select new DoiTuongBaseModel
                            {
                                IdDoiTuong = dt.IdDoiTuong,
                                MaDoiTuong = dt.MaDoiTuong,
                                TenDoiTuong = dt.TenDoiTuong,
                                PhanTramMienGiam = dt.PhanTramMienGiam

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenDoiTuong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<DoiTuongBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDoiTuongByIdQuery : IRequest<DoiTuongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin đối tượng theo id
        /// </summary>
        /// <param name="id">Id đối tượng</param>
        public GetDoiTuongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDoiTuongByIdQuery, DoiTuongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DoiTuongModel> Handle(GetDoiTuongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DoiTuongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDoiTuongs.FirstOrDefaultAsync(x => x.IdDoiTuong == id);

                    return AutoMapperUtils.AutoMap<SvDoiTuong, DoiTuongModel>(entity);
                });
                return item;
            }
        }
    }
}
