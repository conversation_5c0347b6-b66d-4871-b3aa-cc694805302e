﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxHocHamQuery : IRequest<List<HocHamSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y học hàm cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHocHamQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHocHamQuery, List<HocHamSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HocHamSelectItemModel>> Handle(GetComboboxHocHamQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HocHamConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbHocHams.OrderBy(x => x.HocHam)
                                select new HocHamSelectItemModel()
                                {
                                    IdHocHam = dt.IdHocHam,
                                    MaHocHam = dt.MaHocHam,
                                    HocHam = dt.HocHam
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.HocHam.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHocHamQuery : IRequest<PaginationList<HocHamBaseModel>>
    {
        public HocHamFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách học hàm có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHocHamQuery(HocHamFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHocHamQuery, PaginationList<HocHamBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HocHamBaseModel>> Handle(GetFilterHocHamQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbHocHams
                            select new HocHamBaseModel
                            {
                                IdHocHam = dt.IdHocHam,
                                MaHocHam = dt.MaHocHam,
                                HocHam = dt.HocHam,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.HocHam.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HocHamBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHocHamByIdQuery : IRequest<HocHamModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin học hàm theo id
        /// </summary>
        /// <param name="id">Id học hàm</param>
        public GetHocHamByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHocHamByIdQuery, HocHamModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HocHamModel> Handle(GetHocHamByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HocHamConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbHocHams.FirstOrDefaultAsync(x => x.IdHocHam == id);

                    return AutoMapperUtils.AutoMap<TkbHocHam, HocHamModel>(entity);
                });
                return item;
            }
        }
    }
}
