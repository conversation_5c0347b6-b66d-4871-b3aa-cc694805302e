﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxXepLoaiRenLuyenQuery : IRequest<List<XepLoaiRenLuyenSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy xếp loại rèn luyện cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiRenLuyenQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiRenLuyenQuery, List<XepLoaiRenLuyenSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiRenLuyenSelectItemModel>> Handle(GetComboboxXepLoaiRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepLoaiRenLuyens.OrderBy(x => x.XepLoai)
                                select new XepLoaiRenLuyenSelectItemModel()
                                {
                                    IdXepLoai = dt.IdXepLoai,
                                    XepLoai = dt.XepLoai
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiRenLuyenQuery : IRequest<PaginationList<XepLoaiRenLuyenBaseModel>>
    {
        public XepLoaiRenLuyenFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách xếp loại rèn luyện có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiRenLuyenQuery(XepLoaiRenLuyenFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiRenLuyenQuery, PaginationList<XepLoaiRenLuyenBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiRenLuyenBaseModel>> Handle(GetFilterXepLoaiRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepLoaiRenLuyens
                            select new XepLoaiRenLuyenBaseModel
                            {
                                IdXepLoai = dt.IdXepLoai,
                                XepLoai = dt.XepLoai,
                                XepLoaiEn = dt.XepLoaiEn,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                HeSo = dt.HeSo

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepLoaiRenLuyenBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiRenLuyenByIdQuery : IRequest<XepLoaiRenLuyenModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin xếp loại rèn luyện theo id
        /// </summary>
        /// <param name="id">Id xếp loại rèn luyện</param>
        public GetXepLoaiRenLuyenByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiRenLuyenByIdQuery, XepLoaiRenLuyenModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiRenLuyenModel> Handle(GetXepLoaiRenLuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepLoaiRenLuyens.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                    return AutoMapperUtils.AutoMap<SvXepLoaiRenLuyen, XepLoaiRenLuyenModel>(entity);
                });
                return item;
            }
        }
    }
}
