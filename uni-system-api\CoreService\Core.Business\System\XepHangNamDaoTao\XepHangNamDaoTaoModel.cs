﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepHangNamDaoTaoSelectItemModel
    {
        public int IdXepHang { get; set; }
        public int NamThu { get; set; }
        public int TuTinChi { get; set; }
        public int DenTinChi { get; set; }
    }

    public class XepHangNamDaoTaoBaseModel
    {
        public int IdXepHang { get; set; }
        public int NamThu { get; set; }
        public int TuTinChi { get; set; }
        public int DenTinChi { get; set; }
        public string NamThuEn { get; set; }
    }


    public class XepHangNamDaoTaoModel : XepHangNamDaoTaoBaseModel
    {
      
    }

    public class XepHangNamDaoTaoFilterModel : BaseQueryFilterModel
    {
        public XepHangNamDaoTaoFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepHang";
        }
    }

    public class CreateXepHangNamDaoTaoModel
    {
        [Required(ErrorMessage = "XepHangNamDaoTao.NamThu.NotRequire")]
        public int NamThu { get; set; }

        [Required(ErrorMessage = "XepHangNamDaoTao.TuTinChi.NotRequire")]
        public int TuTinChi { get; set; }

        [Required(ErrorMessage = "XepHangNamDaoTao.DenTinChi.NotRequire")]
        public int DenTinChi { get; set; }

        [MaxLength(50, ErrorMessage = "XepHangNamDaoTao.NamThuEn.MaxLength(50)")]
        public string NamThuEn { get; set; }
    }

    public class CreateManyXepHangNamDaoTaoModel
    {
        public List<CreateXepHangNamDaoTaoModel> listXepHangNamDaoTaoModels { get; set; }
    }

    public class UpdateXepHangNamDaoTaoModel : CreateXepHangNamDaoTaoModel
    {
        public void UpdateEntity(SvXepHangNamDaoTao input)
        {
            input.NamThu = NamThu;
            input.TuTinChi = TuTinChi;
            input.DenTinChi = DenTinChi;
            input.NamThuEn = NamThuEn;
        }
    }
}
