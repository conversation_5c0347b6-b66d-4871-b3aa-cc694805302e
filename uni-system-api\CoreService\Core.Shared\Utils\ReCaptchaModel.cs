﻿using System.Text.Json;
using System.Threading.Tasks;
using System.Text.Json.Serialization;
using System.Net.Http;

namespace Core.Shared
{
    public class ReCaptchaModel
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        public async Task<bool> Validate(string token, string privateKey)
        {
            var client = new HttpClient();
            //string privateKey = Utils.GetConfig("Recaptcha:SecretKey");
            var response = await client.GetStringAsync($"https://www.google.com/recaptcha/api/siteverify?secret={privateKey}&response={token}");
            var captchaResponse = JsonSerializer.Deserialize<ReCaptchaModel>(response);

            return captchaResponse.Success;
        }
    }
}
