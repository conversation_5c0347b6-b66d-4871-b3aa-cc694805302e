﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chuc-danh")]
    [ApiExplorerSettings(GroupName = "16. Chức danh")]
    [Authorize]
    public class ChucDanhController : ApiControllerBase
    {
        public ChucDanhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Lấy danh sách chức danh cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ChucDanhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxChucDanhQuery(count, ts));
            });
        }


        /// <summary>
        /// Lấy danh sách chức danh có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChucDanhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ChucDanhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterChucDanhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết chức danh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ChucDanhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChucDanhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới chức danh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateChucDanhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_DANH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_DANH_CREATE;


                return await _mediator.Send(new CreateChucDanhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel chức danh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyChucDanhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_DANH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_DANH_CREATE_MANY;


                return await _mediator.Send(new CreateManyChucDanhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa chức danh
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateChucDanhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_DANH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_DANH_UPDATE;
                return await _mediator.Send(new UpdateChucDanhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chức danh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_DANH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_DANH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_DANH_DELETE;

                return await _mediator.Send(new DeleteChucDanhCommand(id, u.SystemLog));
            });
        }

    }
}
