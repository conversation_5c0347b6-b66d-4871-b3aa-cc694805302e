﻿using Leader.Shared;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Core.Shared.Infrastructure;
using Leader.Business.ThirdPartyBussiness.Hrm;
using Leader.Shared.Constants;

namespace Leader.Business
{
    public static class ServiceConfigure
    {
        public static void Configure(IServiceCollection services, IConfiguration configuration)
        {
            HouHRMClient.ServiceConfigure.Configure(services, configuration);

            // Apply MediaR Scanning to find all assemblies Requests and Responses
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ServiceConfigure).Assembly));

            services.AddTransient<ILeaderCallStoreHelper, LeaderCallStoreHelper>();

            services.AddNamedTransient<IHrmService, HouHrmService>(HrmServiceContants.Hou);

            services.AddTransient<EmployeeService>();
        }
    }
}
