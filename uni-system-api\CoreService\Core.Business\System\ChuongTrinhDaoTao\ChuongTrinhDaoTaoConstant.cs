﻿using Core.Shared;

namespace Core.Business
{
    public class ChuongTrinhDaoTaoConstant
    {
        public const string CachePrefix = CacheConstants.CHUONG_TRINH_DAO_TAO;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
