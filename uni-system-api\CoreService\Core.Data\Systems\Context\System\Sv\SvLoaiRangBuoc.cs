﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiRangBuoc")]
    public class SvLoaiRangBuoc
    {

        public SvLoaiRangBuoc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("Loai_rang_buoc")]
        public int LoaiRangBuoc { get; set; }

        [Column("Ten_rang_buoc"), MaxLength(50)]
        public string TenRangBuoc { get; set; }

    }
}
