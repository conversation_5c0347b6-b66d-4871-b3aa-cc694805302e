﻿using Core.Data;
using Core.Shared;
using FluentEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class BenhVienBaseModel
    {
        public int IdBenhVien { get; set; }

        [Required(ErrorMessage = "BenhVien.MaBenhVien.NotRequire")]
        [MaxLength(50, ErrorMessage = "BenhVien.MaBenhVien.MaxLength(50)")]
        public string MaBenhVien { get; set; }

        [Required(ErrorMessage = "BenhVien.TenBenhVien.NotRequire")]
        [MaxLength(255, ErrorMessage = "BenhVien.TenBenhVien.MaxLength(255)")]
        public string TenBenhVien { get; set; }

        [MaxLength(255, ErrorMessage = "BenhVien.TuyenBVTruoc.MaxLength(255)")]
        public string TuyenBvTruoc2025 { get; set; }

        public string DangKyKcbBanDau { get; set; }

        public string DiaChi { get; set; }

        public string Ghi<PERSON>hu { get; set; }
    }

    public class BenhVienModel : BenhVienBaseModel
    {
    }

    public class CreateBenhVienModel
    {
        [Required(ErrorMessage = "BenhVien.MaBenhVien.NotRequire")]
        [MaxLength(50, ErrorMessage = "BenhVien.MaBenhVien.MaxLength(50)")]
        public string MaBenhVien { get; set; }

        [Required(ErrorMessage = "BenhVien.TenBenhVien.NotRequire")]
        [MaxLength(255, ErrorMessage = "BenhVien.TenBenhVien.MaxLength(255)")]
        public string TenBenhVien { get; set; }

        [MaxLength(255, ErrorMessage = "BenhVien.TuyenBVTruoc.MaxLength(255)")]
        public string TuyenBvTruoc2025 { get; set; }

        public string DangKyKcbBanDau { get; set; }

        public string DiaChi { get; set; }

        public string GhiChu { get; set; }
    }

    public class CreateManyBenhVienModel
    {
        public List<CreateBenhVienModel> ListBenhVien { get; set; }
    }

    public class UpdateBenhVienModel : CreateBenhVienModel
    {
        [Required(ErrorMessage = "BenhVien.IdBenhVien.NotRequire")]
        public int IdBenhVien { get; set; }

        public void UpdateEntity(SvBenhVien entity)
        {
            entity.IdBenhVien = IdBenhVien;
            entity.MaBenhVien = MaBenhVien;
            entity.TenBenhVien = TenBenhVien;
            entity.TuyenBvTruoc2025 = TuyenBvTruoc2025;
            entity.DangKyKcbBanDau = DangKyKcbBanDau;
            entity.DiaChi = DiaChi;
            entity.GhiChu = GhiChu;
        }
    }

    public class BenhVienSelectItemModel 
    {
        public int IdBenhVien { get; set; }
        public string MaBenhVien { get; set; }
        public string TenBenhVien { get; set; }
    }

    public class BenhVienQueryFilter : BaseQueryFilterModel
    {
    }

}
