﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/nhom-chung-chi")]
    [ApiExplorerSettings(GroupName = "75. Nhóm chứng chỉ")]
    [Authorize]
    public class NhomChungChiController : ApiControllerBase
    {
        public NhomChungChiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Nhóm chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NhomChungChiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNhomChungChiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Nhóm chứng chỉ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<NhomChungChiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] NhomChungChiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterNhomChungChiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Nhóm chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<NhomChungChiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNhomChungChiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Nhóm chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateNhomChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_CHUNG_CHI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_CHUNG_CHI_CREATE;


                return await _mediator.Send(new CreateNhomChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Nhóm chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyNhomChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_CHUNG_CHI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_CHUNG_CHI_CREATE_MANY;


                return await _mediator.Send(new CreateManyNhomChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Nhóm chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateNhomChungChiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_CHUNG_CHI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_CHUNG_CHI_UPDATE;
                return await _mediator.Send(new UpdateNhomChungChiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Nhóm chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_CHUNG_CHI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NHOM_CHUNG_CHI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NHOM_CHUNG_CHI_DELETE;

                return await _mediator.Send(new DeleteNhomChungChiCommand(id, u.SystemLog));
            });
        }

    }
}
