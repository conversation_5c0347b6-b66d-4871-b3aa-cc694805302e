﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ThanhPhanMonTheoHeSelectItemModel
    {
        public int IdThanhPhan { get; set; }
        public int IdHe { get; set; }
    }

    public class ThanhPhanMonTheoHeBaseModel
    {
        public int IdThanhPhan { get; set; }
        public string TenThanhPhan { get; set; }
        public int IdHe { get; set; }
        public string TenHe { get; set; }
        public int Stt { get; set; }
        public int TyLe { get; set; }
        public int TyLeNhom { get; set; }
        public int NhomThanhPhan { get; set; }
        public bool ChonMacDinh { get; set; }
    }


    public class ThanhPhanMonTheoHeModel : ThanhPhanMonTheoHeBaseModel
    {

    }

    public class ThanhPhanMonTheoHeFilterModel : BaseQueryFilterModel
    {
        public ThanhPhanMonTheoHeFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdThanhPhan";
        }
    }

    public class CreateThanhPhanMonTheoHeModel
    {
        [Required(ErrorMessage = "ThanhPhanMonTheoHe.IdThanhPhan.NotRequire")]
        public int IdThanhPhan { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.IdHe.NotRequire")]
        public int IdHe { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.Stt.NotRequire")]
        public int Stt { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.TyLe.NotRequire")]
        public int TyLe { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.TyLeNhom.NotRequire")]
        public int TyLeNhom { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.NhomThanhPhan.NotRequire")]
        public int NhomThanhPhan { get; set; }

        [Required(ErrorMessage = "ThanhPhanMonTheoHe.ChonMacDinh.NotRequire")]
        public bool ChonMacDinh { get; set; }

    }

    public class CreateManyThanhPhanMonTheoHeModel
    {
        public List<CreateThanhPhanMonTheoHeModel> listThanhPhanMonTheoHeModels { get; set; }
    }

    public class UpdateThanhPhanMonTheoHeModel : CreateThanhPhanMonTheoHeModel
    {
        public void UpdateEntity(SvThanhPhanMonTheoHe input)
        {
            input.IdThanhPhan = IdThanhPhan;
            input.IdHe = IdHe;
            input.Stt = Stt;
            input.TyLe = TyLe;
            input.TyLeNhom = TyLeNhom;
            input.NhomThanhPhan = NhomThanhPhan;
            input.ChonMacDinh = ChonMacDinh;

        }
    }
}
