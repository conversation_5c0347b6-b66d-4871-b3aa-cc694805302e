﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbHocHam")]
    public class TkbHocHam
    {
        public TkbHocHam()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_hoc_ham")]
        public int IdHocHam { get; set; }

        [Column("Ma_hoc_ham"), MaxLength(10)]
        public string MaHocHam { get; set; }

        [Column("Hoc_ham"), MaxLength(100)]
        public string HocHam { get; set; }

    }
}

