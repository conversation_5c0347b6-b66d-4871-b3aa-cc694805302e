﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svChuongTrinhDaoTaoKienThuc")]
    public class SvChuongTrinhDaoTaoKienThuc
    {

        public SvChuongTrinhDaoTaoKienThuc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_kien_thuc")]
        public int IdKienThuc { get; set; }

        [Column("Mon_chuyen_nganh")]
        public bool MonChuyenNganh { get; set; }

        [Column("ten_kien_thuc"), MaxLength(100)]
        public string TenKienThuc { get; set; }

        [Column("ten_kien_thuc_en"), MaxLength(200)]
        public string TenKienThucEn { get; set; }


    }
}
