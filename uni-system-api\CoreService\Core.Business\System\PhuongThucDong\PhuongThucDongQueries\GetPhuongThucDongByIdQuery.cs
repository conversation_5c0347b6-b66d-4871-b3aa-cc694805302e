﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetPhuongThucDongByIdQuery : IRequest<PhuongThucDongModel>
    {
        public int IdPhuongThucDong { get; set; }

        /// <summary>
        /// L<PERSON>y thông tin Phương thức đóng theo id
        /// </summary>
        /// <param name="id">Id Phương thức đóng</param>
        public GetPhuongThucDongByIdQuery(int idPhuongThucDong)
        {
            IdPhuongThucDong = idPhuongThucDong;
        }

        public class Handler : IRequestHandler<GetPhuongThucDongByIdQuery, PhuongThucDongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<PhuongThucDongModel> Handle(GetPhuongThucDongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.IdPhuongThucDong;
                string cacheKey = PhuongThucDongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvPhuongThucDongs.AsNoTracking().FirstOrDefaultAsync(x => x.IdPhuongThucDong == id);

                    return AutoMapperUtils.AutoMap<SvPhuongThucDong, PhuongThucDongModel>(entity);
                });
                return item;
            }
        }
    }
}
