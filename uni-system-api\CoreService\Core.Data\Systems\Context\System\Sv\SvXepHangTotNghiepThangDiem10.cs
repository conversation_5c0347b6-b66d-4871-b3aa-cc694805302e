﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepHangTotNghiepThangDiem10")]
    public class SvXepHangTotNghiepThangDiem10
    {
        
        public SvXepHangTotNghiepThangDiem10()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_Xep_hang")]
        public int IdXepHang { get; set; }

        [Column("Ma_xep_hang"), MaxLength(50)]
        public string MaXepHang { get; set; }

        [Column("Xep_hang"), MaxLength(50)]
        public string XepHang { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Xep_hang_en"), MaxLength(50)]
        public string XepHangEn { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

    }
}
