﻿using Core.Shared;
using Elasticsearch.Net;
using Microsoft.Extensions.Configuration;
using Novell.Directory.Ldap;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.Core
{

    /// <summary>
    /// Ldap related tasks manager
    /// </summary>
    public class LdapManager : ILdapManager
    {
        private readonly LdapModel _ldap;
        private readonly IConfiguration _config;

        public LdapManager(IConfiguration config)
        {
            _config = config;
            _ldap = _config.GetSection("ldap").Get<LdapModel>();
        }

        /// <summary>
        /// Check if user in Ldap 
        /// </summary>
        /// <param name="userName">Ldap user name without domain name</param>
        /// <param name="password">Ldap passsword</param>
        public bool ValidateAuthentication(string userName, string password)
        {
            userName = string.Format(_ldap.DefaultDNSyntax, userName);

            //String loginDN = @"cn=haind,dc=local,dc=com";
            //String loginDN = @"uid=test,dc=unisoft,dc=com";
            //String password = "123123";
            try
            {
                using (LdapConnection lc = new LdapConnection())
                {
                    // connect to the server
                    lc.Connect(_ldap.Host, _ldap.Port);

                    // authenticate to the server
                    lc.Bind(userName, password);
                }
            }
            catch (LdapException e)
            {
                if (e.ResultCode == LdapException.InvalidCredentials)
                {
                    Log.Information("Sai tên đăng nhập hoặc mật khẩu");
                }
                else
                {
                    Log.Error(e.Message, e);
                }
                return false;
            }
            return true;
        }
    }
}
