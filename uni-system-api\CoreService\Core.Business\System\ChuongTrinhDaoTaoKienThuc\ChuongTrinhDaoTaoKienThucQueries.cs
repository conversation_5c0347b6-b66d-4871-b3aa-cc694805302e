﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxChuongTrinhDaoTaoKienThucQuery : IRequest<List<ChuongTrinhDaoTaoKienThucSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tôn giáo cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxChuongTrinhDaoTaoKienThucQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxChuongTrinhDaoTaoKienThucQuery, List<ChuongTrinhDaoTaoKienThucSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChuongTrinhDaoTaoKienThucSelectItemModel>> Handle(GetComboboxChuongTrinhDaoTaoKienThucQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvChuongTrinhDaoTaoKienThucs.OrderBy(x => x.TenKienThuc)
                                select new ChuongTrinhDaoTaoKienThucSelectItemModel()
                                {
                                    IdKienThuc = dt.IdKienThuc,
                                    TenKienThuc = dt.TenKienThuc
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenKienThuc.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterChuongTrinhDaoTaoKienThucQuery : IRequest<PaginationList<ChuongTrinhDaoTaoKienThucBaseModel>>
    {
        public ChuongTrinhDaoTaoKienThucFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tôn giáo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChuongTrinhDaoTaoKienThucQuery(ChuongTrinhDaoTaoKienThucFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChuongTrinhDaoTaoKienThucQuery, PaginationList<ChuongTrinhDaoTaoKienThucBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChuongTrinhDaoTaoKienThucBaseModel>> Handle(GetFilterChuongTrinhDaoTaoKienThucQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvChuongTrinhDaoTaoKienThucs
                            select new ChuongTrinhDaoTaoKienThucBaseModel
                            {
                                IdKienThuc = dt.IdKienThuc,
                                MonChuyenNganh = dt.MonChuyenNganh,
                                TenKienThuc = dt.TenKienThuc,
                                TenKienThucEn = dt.TenKienThucEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenKienThuc.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ChuongTrinhDaoTaoKienThucBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetChuongTrinhDaoTaoKienThucByIdQuery : IRequest<ChuongTrinhDaoTaoKienThucModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Tôn giáo theo id
        /// </summary>
        /// <param name="id">Id tôn giáo</param>
        public GetChuongTrinhDaoTaoKienThucByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChuongTrinhDaoTaoKienThucByIdQuery, ChuongTrinhDaoTaoKienThucModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChuongTrinhDaoTaoKienThucModel> Handle(GetChuongTrinhDaoTaoKienThucByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvChuongTrinhDaoTaoKienThucs.FirstOrDefaultAsync(x => x.IdKienThuc == id);

                    return AutoMapperUtils.AutoMap<SvChuongTrinhDaoTaoKienThuc, ChuongTrinhDaoTaoKienThucModel>(entity);
                });
                return item;
            }
        }
    }
}
