﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDoiTuongCommand : IRequest<Unit>
    {
        public CreateDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDoiTuongCommand(CreateDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDoiTuongModel, SvDoiTuong>(model);

                var checkCode = await _dataContext.SvDoiTuongs.AnyAsync(x => x.IdDoiTuong == entity.IdDoiTuong || x.TenDoiTuong == entity.TenDoiTuong || x.MaDoiTuong == entity.MaDoiTuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuong.Existed", entity.TenDoiTuong.ToString()]}");
                }

                await _dataContext.SvDoiTuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới đối tượng: {entity.TenDoiTuong}",
                    ObjectCode = DoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDoiTuongCommand : IRequest<Unit>
    {
        public CreateManyDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDoiTuongCommand(CreateManyDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDoiTuongAdd = model.listDoiTuongModels.Select(x => x.TenDoiTuong).ToList();
                var listMaDoiTuongAdd = model.listDoiTuongModels.Select(x => x.MaDoiTuong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDoiTuongModel, SvDoiTuong>(model);

                // Check data duplicate
                if (listDoiTuongAdd.Count() != listDoiTuongAdd.Distinct().Count() || listMaDoiTuongAdd.Count() != listMaDoiTuongAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvDoiTuongs.AnyAsync(x => listDoiTuongAdd.Contains(x.TenDoiTuong)) || await _dataContext.SvDoiTuongs.AnyAsync(x => listMaDoiTuongAdd.Contains(x.MaDoiTuong)))
                {
                    throw new ArgumentException($"{_localizer["DoiTuong.Existed"]}");
                }

                var listEntity = model.listDoiTuongModels.Select(x => new SvDoiTuong()
                {
                    IdDoiTuong = x.IdDoiTuong,
                    MaDoiTuong = x.MaDoiTuong,
                    TenDoiTuong = x.TenDoiTuong,
                    PhanTramMienGiam = x.PhanTramMienGiam,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdDoiTuong).ToList();

                Log.Information($"Create many {DoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import đối tượng từ file excel",
                    ObjectCode = DoiTuongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDoiTuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDoiTuongCommand(int id, UpdateDoiTuongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDoiTuongs.FirstOrDefaultAsync(dt => dt.IdDoiTuong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.SvDoiTuongs.AnyAsync(x => (x.TenDoiTuong == model.TenDoiTuong || x.MaDoiTuong == model.MaDoiTuong) && x.IdDoiTuong != model.IdDoiTuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuong.Existed", model.TenDoiTuong.ToString()]}");
                }

                Log.Information($"Before Update {DoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDoiTuongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật đối tượng: {entity.TenDoiTuong}",
                    ObjectCode = DoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey(entity.IdDoiTuong.ToString()));
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDoiTuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDoiTuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDoiTuongs.FirstOrDefaultAsync(x => x.IdDoiTuong == id);

                _dataContext.SvDoiTuongs.Remove(entity);

                Log.Information($"Delete {DoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa đối tượng: {entity.TenDoiTuong}",
                    ObjectCode = DoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey());
                _cacheService.Remove(DoiTuongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
