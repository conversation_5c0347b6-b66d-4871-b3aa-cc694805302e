﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Shared.ContextAccessor;
using Leader.Business;
using Leader.Shared;
using Core.API.Shared;
using Core.Shared;

namespace Leader.API.Controller
{
    [ApiController]
    [Route("leader/v1/kh-bac-dao-tao")]
    [ApiExplorerSettings(GroupName = "53. Bậc đào tạo khoa học")]
    [Authorize]
    public class KhBacDaoTaoController : ApiControllerBaseV2
    {
        public KhBacDaoTaoController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới bậc đào tạo khoa học
        /// </summary>
        /// <param name="model">Thông tin bậc đào tạo khoa học</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionLeaderEnum.KH_BAC_DAO_TAO_CREATE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateKhBacDaoTaoModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_CREATE);
                _contextAccessor.SystemLog.ActionName = LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_CREATE;

                return await _mediator.Send(new CreateKhBacDaoTaoCommand(model));
            });
        }

        /// <summary>
        /// Lấy bậc đào tạo khoa học theo id
        /// </summary>
        /// <param name="id">Id bậc đào tạo khoa học</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionLeaderEnum.KH_BAC_DAO_TAO_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<KhBacDaoTaoModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetKhBacDaoTaoByIdQuery(id));
            });
        }

        /// <summary>
        /// Cập nhật bậc đào tạo khoa học
        /// </summary>
        /// <param name="model">Thông tin bậc đào tạo khoa học cần cập nhật</param>
        /// <param name="id">Id bậc đào tạo khoa học</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionLeaderEnum.KH_BAC_DAO_TAO_UPDATE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateKhBacDaoTaoModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_UPDATE);
                _contextAccessor.SystemLog.ActionName = LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_UPDATE;

                model.Id = id;
                return await _mediator.Send(new UpdateKhBacDaoTaoCommand(model));
            });
        }

        /// <summary>
        /// Xóa bậc đào tạo khoa học
        /// </summary>
        /// <param name="id">id bậc đào tạo khoa học cần xóa</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionLeaderEnum.KH_BAC_DAO_TAO_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete([FromRoute] int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_DELETE);
                _contextAccessor.SystemLog.ActionName = LeaderLogConstants.ACTION_KH_BAC_DAO_TAO_DELETE;

                return await _mediator.Send(new DeleteKhBacDaoTaoCommand(id));
            });
        }

        /// <summary>
        /// Lấy danh sách bậc đào tạo khoa học theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionLeaderEnum.KH_BAC_DAO_TAO_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<KhBacDaoTaoBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] KhBacDaoTaoQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterKhBacDaoTaoQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách bậc đào tạo khoa học cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<KhBacDaoTaoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxKhBacDaoTaoQuery(count, ts));

                return result;
            });
        }
        #endregion
    }
}
