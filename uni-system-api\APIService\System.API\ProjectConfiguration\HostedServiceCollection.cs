﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.API.MessageQueue;
using System.API.Services;

namespace Core.API
{
    public static class HostedServiceCollection
    {
        /// <summary>
        /// RegisterHostedServiceComponents
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterHostedServiceComponents(this IServiceCollection services, IConfiguration configuration)
        {
            if (configuration["AppSettings:HostedService:NotificationConsumer:Enable"] == "true")
            {
                services.AddHostedService<NotificationConsumer>();
            }

            services.AddHostedService<StartupService>();

            return services;
        }
    }
}