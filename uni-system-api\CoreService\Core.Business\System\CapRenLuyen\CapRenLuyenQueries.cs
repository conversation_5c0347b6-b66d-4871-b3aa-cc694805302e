﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxCapRenLuyenQuery : IRequest<List<CapRenLuyenSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y cấp rèn luyện cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxCapRenLuyenQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxCapRenLuyenQuery, List<CapRenLuyenSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<CapRenLuyenSelectItemModel>> Handle(GetComboboxCapRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = CapRenLuyenConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvCapRenLuyens.OrderBy(x => x.TenCap)
                                select new CapRenLuyenSelectItemModel()
                                {
                                    IdCapRenLuyen = dt.IdCapRenLuyen,
                                    KyHieu = dt.KyHieu,
                                    TenCap = dt.TenCap,
                                    Diem = dt.Diem
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenCap.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterCapRenLuyenQuery : IRequest<PaginationList<CapRenLuyenBaseModel>>
    {
        public CapRenLuyenFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách cấp rèn luyện có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterCapRenLuyenQuery(CapRenLuyenFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterCapRenLuyenQuery, PaginationList<CapRenLuyenBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<CapRenLuyenBaseModel>> Handle(GetFilterCapRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvCapRenLuyens
                            select new CapRenLuyenBaseModel
                            {
                                IdCapRenLuyen = dt.IdCapRenLuyen,
                                KyHieu = dt.KyHieu,
                                TenCap = dt.TenCap,
                                Diem = dt.Diem

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenCap.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<CapRenLuyenBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetCapRenLuyenByIdQuery : IRequest<CapRenLuyenModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin cấp rèn luyện theo id
        /// </summary>
        /// <param name="id">Id cấp rèn luyện</param>
        public GetCapRenLuyenByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetCapRenLuyenByIdQuery, CapRenLuyenModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<CapRenLuyenModel> Handle(GetCapRenLuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = CapRenLuyenConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvCapRenLuyens.FirstOrDefaultAsync(x => x.IdCapRenLuyen == id);

                    return AutoMapperUtils.AutoMap<SvCapRenLuyen, CapRenLuyenModel>(entity);
                });
                return item;
            }
        }
    }
}
