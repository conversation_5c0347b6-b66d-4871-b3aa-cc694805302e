﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chi-tieu-tuyen-sinh")]
    [ApiExplorerSettings(GroupName = "120. Chỉ tiêu tuyển sinh")]
    [Authorize]
    public class ChiTieuTuyenSinhController : ApiControllerBase
    {
        public ChiTieuTuyenSinhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Lấy danh sách chỉ tiêu tuyển sinh có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChiTieuTuyenSinhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHI_TIEU_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ChiTieuTuyenSinhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterChiTieuTuyenSinhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết chỉ tiêu tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ChiTieuTuyenSinhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHI_TIEU_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChiTieuTuyenSinhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới chỉ tiêu tuyển sinh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHI_TIEU_TUYEN_SINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateChiTieuTuyenSinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_CREATE;


                return await _mediator.Send(new CreateChiTieuTuyenSinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa chỉ tiêu tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHI_TIEU_TUYEN_SINH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateChiTieuTuyenSinhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_UPDATE;
                return await _mediator.Send(new UpdateChiTieuTuyenSinhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chỉ tiêu tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHI_TIEU_TUYEN_SINH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHI_TIEU_TUYEN_SINH_DELETE;

                return await _mediator.Send(new DeleteChiTieuTuyenSinhCommand(id, u.SystemLog));
            });
        }

    }
}
