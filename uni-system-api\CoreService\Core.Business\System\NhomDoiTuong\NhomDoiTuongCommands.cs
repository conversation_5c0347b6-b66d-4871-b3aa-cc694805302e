﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateNhomDoiTuongCommand : IRequest<Unit>
    {
        public CreateNhomDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateNhomDoiTuongCommand(CreateNhomDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateNhomDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateNhomDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {NhomDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateNhomDoiTuongModel, SvNhomDoiTuong>(model);

                var checkCode = await _dataContext.SvNhomDoiTuongs.AnyAsync(x => x.IdNhomDoiTuong == entity.IdNhomDoiTuong || x.TenNhom == entity.TenNhom || x.MaNhom == entity.MaNhom);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NhomDoiTuong.Existed", entity.TenNhom.ToString()]}");
                }

                await _dataContext.SvNhomDoiTuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {NhomDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới nhóm đối tượng: {entity.TenNhom}",
                    ObjectCode = NhomDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdNhomDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyNhomDoiTuongCommand : IRequest<Unit>
    {
        public CreateManyNhomDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyNhomDoiTuongCommand(CreateManyNhomDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyNhomDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyNhomDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {NhomDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listNhomDoiTuongAdd = model.listNhomDoiTuongModels.Select(x => x.TenNhom).ToList();
                var listMaNhomAdd = model.listNhomDoiTuongModels.Select(x => x.MaNhom).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyNhomDoiTuongModel, SvNhomDoiTuong>(model);

                // Check data duplicate
                if (listNhomDoiTuongAdd.Count() != listNhomDoiTuongAdd.Distinct().Count() || listMaNhomAdd.Count() != listMaNhomAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvNhomDoiTuongs.AnyAsync(x => listNhomDoiTuongAdd.Contains(x.TenNhom)) || await _dataContext.SvNhomDoiTuongs.AnyAsync(x => listMaNhomAdd.Contains(x.MaNhom)))
                {
                    throw new ArgumentException($"{_localizer["NhomDoiTuong.Existed"]}");
                }

                var listEntity = model.listNhomDoiTuongModels.Select(x => new SvNhomDoiTuong()
                {
                    IdNhomDoiTuong = x.IdNhomDoiTuong,
                    MaNhom = x.MaNhom,
                    TenNhom = x.TenNhom,
                    TenNhomEn = x.TenNhomEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdNhomDoiTuong).ToList();

                Log.Information($"Create many {NhomDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import nhóm đối tượng từ file excel",
                    ObjectCode = NhomDoiTuongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateNhomDoiTuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateNhomDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateNhomDoiTuongCommand(int id, UpdateNhomDoiTuongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateNhomDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateNhomDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {NhomDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvNhomDoiTuongs.FirstOrDefaultAsync(dt => dt.IdNhomDoiTuong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvNhomDoiTuongs.AnyAsync(x => (x.TenNhom ==model.TenNhom || x.MaNhom ==model.MaNhom) && x.IdNhomDoiTuong !=model.IdNhomDoiTuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NhomDoiTuong.Existed",model.TenNhom.ToString()]}");
                }

                Log.Information($"Before Update {NhomDoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvNhomDoiTuongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {NhomDoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {NhomDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật nhóm đối tượng: {entity.TenNhom}",
                    ObjectCode = NhomDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdNhomDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey(entity.IdNhomDoiTuong.ToString()));
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteNhomDoiTuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteNhomDoiTuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteNhomDoiTuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteNhomDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {NhomDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvNhomDoiTuongs.FirstOrDefaultAsync(x => x.IdNhomDoiTuong == id);

                _dataContext.SvNhomDoiTuongs.Remove(entity);

                Log.Information($"Delete {NhomDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa nhóm đối tượng: {entity.TenNhom}",
                    ObjectCode = NhomDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdNhomDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey());
                _cacheService.Remove(NhomDoiTuongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
