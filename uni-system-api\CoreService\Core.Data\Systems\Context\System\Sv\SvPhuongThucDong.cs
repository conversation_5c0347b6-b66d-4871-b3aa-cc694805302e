﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("svPhuongThucDong")]
    public class SvPhuongThucDong
    {
        public SvPhuongThucDong()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phuong_thuc_dong")]
        public int IdPhuongThucDong { get; set; }

        [Column("Phuong_thuc_dong"), MaxLength(255)]
        public string PhuongThucDong { get; set; }

        [Column("Ghi_chu")]
        public string Ghi<PERSON>hu { get; set; }
    }
}
