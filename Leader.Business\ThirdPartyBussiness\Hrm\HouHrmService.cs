﻿using HouHRMClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Leader.Business.ThirdPartyBussiness.Hrm
{
    public class HouHrmService : IHrmService
    {
        private readonly HouHRMApiClient _client;

        public HouHrmService(HouHRMApiClient client)
        {
            _client = client;
        }

        public async Task<HrmEmployeeDetailModel> GetEmployeeByEmailAsync(string email)
        {
            var dt = await _client.GetUserByEmailAsync(email);

            if (dt == null)
            {
                throw new Exception("User data is null");
            }

            return new HrmEmployeeDetailModel
            {
                Email = dt.Email,
                PhoneNumber = dt.PhoneNumber,
                Address = dt.Address,
                ReferenceId = dt.Id.ToString(),
                UserName = dt.UserName,
            };
        }

        public async Task<List<HrmEmployeeModel>> GetAllEmployeesAsync()
        {
            var dt = await _client.GetAllUsersAsync();
            if (dt == null)
            {
                throw new Exception("Users data is null");
            }
            return dt.Select(x => new HrmEmployeeModel
            {
                Email = x.Email,
                PhoneNumber = x.PhoneNumber,
                ReferenceId = x.Id.ToString(),
                UserName = x.UserName,
            }).ToList();
        }

        public Task<List<HrmHoSoCanBoModel>> GetAllHoSoCanBoAsync(DateTime timestamp)
        {
            throw new NotImplementedException();
        }
    }
}
