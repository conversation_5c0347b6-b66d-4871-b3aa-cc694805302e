﻿
# Hướng Dẫn Viết Test Unit với Xunit

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu về Xunit
Xunit là một framework kiểm thử tự động trong .NET, giúp đảm bảo tính đúng đắn và ổn định của ứng dụng thông qua việc kiểm tra các thành phần.

---

## 2. <PERSON><PERSON><PERSON> đặt Xunit và Moq

### 2.1. Tạo Project Unit Test
- Mở Visual Studio.

- Chọn File > New > Project.

- Trong cửa sổ Create a new project:

- Tìm kiếm Unit Test Project trong hộp tìm kiếm (bạn c<PERSON> thể nhập "Unit Test").
- Chọn Unit Test Project (.NET) (nếu sử dụng .NET Framework thì chọn phiên bản tương ứng).
- Nhấn Next.
- Đặt Tên Project:

- Ví dụ: Core.Business.Tests.
- Chọn vị trí lưu project.
- Nhấn Create.
- Chọn Target Framework:

- <PERSON><PERSON>u bạn đang dùng .NET 6 trở lên, chọn .NET 6 (LTS) hoặc phiên bản phù hợp với project chính.
- Nhấn Create.
 
### 2.2. Thêm Xunit và các Package cần thiết
- Mở NuGet Package Manager:

- Nhấp chuột phải vào dự án vừa tạo trong Solution Explorer.
- Chọn Manage NuGet Packages.
- Tìm kiếm và cài đặt các package:

```
Xunit: Tìm xunit và cài đặt phiên bản mới nhất.
Xunit.runner.visualstudio: Cài đặt để tích hợp test vào Visual Studio.
Moq: Tìm Moq để hỗ trợ mock các đối tượng.
```

### 2.3. Thêm Project Chính vào Dự Án Unit Test

- Trong Solution Explorer:
- Nhấp chuột phải vào dự án Unit Test (Core.Business.Tests).
- Chọn Add > Project Reference.
- Chọn dự án chính (project bạn muốn kiểm tra) và nhấn OK.
---

## 3. Viết Hàm Test với Xunit và Moq

### Cấu trúc cơ bản
- **Arrange**: Chuẩn bị dữ liệu và giả lập các service.
- **Act**: Gọi phương thức cần kiểm tra.
- **Assert**: So sánh kết quả thực tế với kết quả mong đợi.

---

## 4. Mô tả chi tiết hàm `CreateSystemApplicationCommandTests -> Handle_ShouldThrowException_WhenCodeExists thao tác với DB core giả lập`

### **4.1. Mã nguồn**
```csharp
public class CreateSystemApplicationCommandTests
{
    private readonly SystemDataContext _systemDataContext;

    public CreateSystemApplicationCommandTests()
    {
        // Khởi tạo context giả lập
        var options = new DbContextOptionsBuilder<SystemDataContext>()
            .UseInMemoryDatabase("TestDb")
            .Options;
        _systemDataContext = new SystemDataContext(options);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenCodeExists()
    {
        // Arrange
        await _systemDataContext.SystemApplication.AddAsync(new Data.SystemApplication { Code = "EXISTING_CODE" });
        await _systemDataContext.SaveChangesAsync();

        var mockCacheService = new Mock<ICacheService>();
        var mockLocalizer = new Mock<IStringLocalizer<Resources>>();
        mockLocalizer.Setup(l => l["system-application.code.existed"])
            .Returns(new LocalizedString("system-application.code.existed", "Code already exists"));
        var mockConfig = new Mock<IConfiguration>();

        var handler = new CreateSystemApplicationCommand.Handler(
            _systemDataContext,
            mockCacheService.Object,
            mockLocalizer.Object,
            mockConfig.Object
        );

        var command = new CreateSystemApplicationCommand(
            new CreateSystemApplicationModel { Code = "EXISTING_CODE" },
            new SystemLogModel { TraceId = Guid.NewGuid().ToString() }
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() => handler.Handle(command, CancellationToken.None));
        Assert.Equal("Code already exists", exception.Message);
    }
}
```

### **4.2. Giải thích các bước**
####    **[Fact]:**
    - Trong xUnit, [Fact] là một attribute dùng để đánh dấu một phương thức là một unit test. Khi bạn sử dụng [Fact], xUnit sẽ hiểu rằng phương thức đó là một bài kiểm tra đơn vị và sẽ được thực thi trong quá trình chạy test.
#### **Step 1: Arrange (Chuẩn bị dữ liệu và các đối tượng cần thiết)**
1. **Giả lập dữ liệu trong cơ sở dữ liệu**:
    ```csharp
    await _systemDataContext.SystemApplication.AddAsync(new Data.SystemApplication { Code = "EXISTING_CODE" });
    await _systemDataContext.SaveChangesAsync();
    ```
    - Ý nghĩa: Tạo một bản ghi giả lập với Code = "EXISTING_CODE" trong cơ sở dữ liệu.
    - Mục đích: Đảm bảo khi chạy hàm kiểm tra, dữ liệu "EXISTING_CODE" đã tồn tại để xử lý trường hợp trùng mã (một lỗi cần bắt).

2. **Mock các service**:
    - **ICacheService**
        ```csharp
        var mockCacheService = new Mock<ICacheService>();
        ```
    - Ý nghĩa: Giả lập service ICacheService (không sử dụng phương thức cụ thể nào trong test này, nhưng vẫn cần để truyền vào handler).

    - **IConfiguration**
        ```csharp
        var mockConfig = new Mock<IConfiguration>();
        ```
    -  Giả lập IConfiguration, được truyền vào nhưng không sử dụng trong test này.

    - **Localizer**:
        ```csharp
        mockLocalizer.Setup(l => l["system-application.code.existed"])
            .Returns(new LocalizedString("system-application.code.existed", "Code already exists"));
        ```
        - Ý nghĩa:
            - Setup: Xác định cách phản hồi khi mockLocalizer được gọi với key "system-application.code.existed".
            - Returns: Trả về một đối tượng LocalizedString có key "system-application.code.existed" và giá trị là "Code already exists".
        - Mục đích: Kiểm tra việc localizer trả về thông báo chính xác khi mã bị trùng.

3. **Tạo handler và command**:
    ```csharp
    var handler = new CreateSystemApplicationCommand.Handler(
        _systemDataContext,
        mockCacheService.Object,
        mockLocalizer.Object,
        mockConfig.Object
    );
    var command = new CreateSystemApplicationCommand(new CreateSystemApplicationModel { Code = "EXISTING_CODE" },
        new SystemLogModel { TraceId = Guid.NewGuid().ToString() });
    ```
    - Ý nghĩa: 
        - Tạo một instance của handler chính (CreateSystemApplicationCommand.Handler), là lớp cần kiểm tra.
        - Tạo command chứa dữ liệu đầu vào (Code = "EXISTING_CODE") mà hàm Handle sẽ xử lý.
        - Gửi kèm một log model để theo dõi truy vết (TraceId).
#### **Step 2: Act & Assert (Thực thi và kiểm tra kết quả)**
- **1. Gọi hàm và bắt ngoại lệ**:
    ```csharp
    var exception = await Assert.ThrowsAsync<ArgumentException>(() => handler.Handle(command, CancellationToken.None));
    ```
    - Ý nghĩa:
        - Gọi hàm Handle của handler với dữ liệu đầu vào (command).
        - Kiểm tra xem hàm có ném ra ngoại lệ ArgumentException hay không.
        - Nếu không có ngoại lệ được ném ra hoặc ngoại lệ không đúng loại, test sẽ thất bại.

- **2. So sánh kết quả trả về**:
    ```csharp
    Assert.Equal("Code already exists", exception.Message);
    ```
    - Ý nghĩa:
        - So sánh thông báo lỗi trong ngoại lệ (exception.Message) với giá trị mong đợi "Code already exists".
    - Mục đích: Đảm bảo rằng logic trong Handle xử lý chính xác khi mã bị trùng, và thông báo lỗi phù hợp với nghiệp vụ.

---

## 4. Mô tả chi tiết hàm `SendMailLogCreateCommandTests -> Handle_ShouldInsertLogIntoMongoDB thao tác với DB MongoDB`

### **4.1. Mã nguồn**
```csharp
[Fact]
public async Task Handle_ShouldInsertLogIntoMongoDB()
{
    // Arrange
    var mockSettings = new Mock<IMongoDBDatabaseSettings>();

    var sendMailLog = new DataLog.SendMailLog
    {
        TraceId = Guid.NewGuid().ToString(),
        ToEmail = "<EMAIL>",
        EmailTemplateCode = "SENDMAIL_LOG_01",
        EmailTemplateName = "SENDMAIL_LOG_01",
        Subject = "SENDMAIL_LOG_01",
        SendMailStatus = 2,
        MessageId = "SENDMAIL_LOG_01",
        ErrorMesssage = null,
        CreatedDate = DateTime.Now
    };

    // Mock settings
    //Giả lập connectionString và DatabaseName cho mongoDB theo cấu hình.
    //Lưu ý là phải tạo 1 DB riêng biệt với DB hệ thống sử dụng
    mockSettings.Setup(s => s.ConnectionString).Returns(_mongoDbSettings.ConnectionString);
    mockSettings.Setup(s => s.DatabaseName).Returns(_mongoDbSettings.DatabaseName);

    var command = new SendMailLogCreateCommand(sendMailLog);
    var handler = new SendMailLogCreateCommand.Handler(mockSettings.Object);

    // Act
    await handler.Handle(command, CancellationToken.None);

    // Assert
    var filter = Builders<DataLog.SendMailLog>.Filter.Eq(doc => doc.TraceId, sendMailLog.TraceId);
    var sendMailLogCreated = await (await _sendMailLogCollection.FindAsync(filter)).FirstOrDefaultAsync();
    Assert.NotNull(sendMailLogCreated);
}
```

## Các Bước và Giải Thích

### 1. **Arrange (Sắp Xếp)**

#### a. **Giả Lập Cài Đặt Cơ Sở Dữ Liệu**
- Một đối tượng giả lập (`Mock`) của `IMongoDBDatabaseSettings` được tạo.
- Các thuộc tính được giả lập:
  - `ConnectionString`: Trả về chuỗi kết nối MongoDB từ `_mongoDbSettings.ConnectionString`.
  - `DatabaseName`: Trả về tên cơ sở dữ liệu từ `_mongoDbSettings.DatabaseName`.

#### b. **Tạo Dữ Liệu Kiểm Tra**
- Một đối tượng `SendMailLog` được tạo với các giá trị cụ thể:
  - `TraceId`: Mã định danh duy nhất (GUID).
  - `ToEmail`: Email người nhận dùng để kiểm tra.
  - `EmailTemplateCode`, `EmailTemplateName`, `Subject`: Các trường liên quan đến mẫu email.
  - `SendMailStatus`: Mã trạng thái (2 trong bài kiểm tra này).
  - `MessageId`: Mã định danh của tin nhắn.
  - `ErrorMessage`: Null trong bài kiểm tra này.
  - `CreatedDate`: Dấu thời gian hiện tại.

#### c. **Khởi Tạo Lệnh và Bộ Xử Lý**
- `SendMailLogCreateCommand` được khởi tạo với đối tượng `sendMailLog`.
- `Handler` được khởi tạo với đối tượng giả lập `IMongoDBDatabaseSettings`.

---

### 2. **Act (Thực Hiện)**
- Phương thức `Handle` được gọi với `command` và `CancellationToken.None`.

---

### 3. **Assert (Kiểm Tra)**
- Một bộ lọc được tạo để tìm tài liệu trong tập hợp MongoDB bằng `TraceId`.
- Kiểm tra truy xuất tài liệu từ `_sendMailLogCollection`.
- Đảm bảo rằng tài liệu được truy xuất không phải là `null`, xác nhận việc chèn thành công.

---

## Ghi Chú
- **Cách Ly Cơ Sở Dữ Liệu Kiểm Tra**: Kiểm tra sử dụng một cơ sở dữ liệu riêng biệt để đảm bảo không gây ảnh hưởng đến cơ sở dữ liệu hệ thống hoặc sản xuất.

---