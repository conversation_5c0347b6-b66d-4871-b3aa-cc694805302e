﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XaSelectItemModel
    {
        public string IdXa { get; set; }
        public string IdHuyen { get; set; }
        public string TenXa { get; set; }
    }

    public class XaBaseModel
    {

        public string IdXa { get; set; }
        public string IdHuyen { get; set; }
        public string TenHuyen { get; set; }
        public string TenXa { get; set; }
        public string TenXaEn { get; set; }
    }


    public class XaModel : XaBaseModel
    {

    }

    public class XaFilterModel : BaseQueryFilterModel
    {
        public XaFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXa";
        }
    }

    public class CreateXaModel
    {
        [MaxLength(6, ErrorMessage = "Xa.IdXa.MaxLength(6)")]
        [Required(ErrorMessage = "Xa.IdXa.NotRequire")]
        public string IdXa { get; set; }

        [MaxLength(4, ErrorMessage = "Xa.IdHuyen.MaxLength(4)")]
        [Required(ErrorMessage = "Xa.IdHuyen.NotRequire")]
        public string IdHuyen { get; set; }

        [MaxLength(30, ErrorMessage = "Xa.TenXa.MaxLength(30)")]
        [Required(ErrorMessage = "Xa.TenXa.NotRequire")]
        public string TenXa { get; set; }

        [MaxLength(50, ErrorMessage = "Xa.TenXaEn.MaxLength(50)")]
        public string TenXaEn { get; set; }

    }

    public class CreateManyXaModel
    {
        public List<CreateXaModel> listXaModels { get; set; }
    }

    public class UpdateXaModel : CreateXaModel
    {
        public void UpdateEntity(SvXa input)
        {
            input.IdXa = IdXa;
            input.IdHuyen = IdHuyen;
            input.TenXa = TenXa;
            input.TenXaEn = TenXaEn;

        }
    }
}
