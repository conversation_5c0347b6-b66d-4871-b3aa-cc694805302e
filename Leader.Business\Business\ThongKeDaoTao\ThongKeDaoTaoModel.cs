﻿using Core.Business;
using System;
using System.Collections.Generic;

namespace Leader.Business
{
    public class ThongKeSoLuongBaseModel
    {
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int? SoLuong { get; set; }
    }
    public class ThongKeSoLuongModel : ThongKeSoLuongBaseModel
    {

    }

    public class ThongKeDanhSachBaseModel
    {
        public string MaHocPhan { get; set; }
        public string TenHocPhan { get; set; }
        public float? SoTC { get; set; }
        public string TenLopTC { get; set; }
        public double? SoSVDangKy { get; set; }
        public string HoTenGV { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
        public string LichHoc { get; set; }
        public string PhongHoc { get; set; }
    }
    public class ThongKeDanhSachModel : ThongKeDanhSachBaseModel
    {

    }

    public class ThongKeToChucThiBaseModel
    {
        public string <PERSON>Hoc<PERSON>han { get; set; }
        public string TenHocPhan { get; set; }
        public int DotThi { get; set; }
        public int LanThi { get; set; }
        public string NgayThi { get; set; }
        public string CaThi { get; set; }
        public string GioThi { get; set; }
        public string PhongThi { get; set; }
        public int? SoSVPhong { get; set; }
        public int? SoSVThi { get; set; }
        public string GiamThi1 { get; set; }
        public string GiamThi2 { get; set; }
        public string GiamThi3 { get; set; }
        public string GiamThi4 { get; set; }
    }
    public class ThongKeToChucThiModel : ThongKeToChucThiBaseModel
    {

    }

    public class ThongKeDaoTaoQueryFilter : BaseQueryFilterModel
    {
        public int IDHe { get; set; }
        public int IDKhoa { get; set; }
        public int IDNganh { get; set; }
        public int HocKy { get; set; }
        public string NamHoc { get; set; }
        public int KhoaHoc { get; set; }
        public int Dot { get; set; }
    }

    public class TongHopLopTinChiBaseModel
    {
        public string TenHe { get; set; }
        public int? Count { get; set; }
    }

    public class TongHopLopTinChiModel
    {
        public string TenKhoa { get; set; }
        public List<TongHopLopTinChiBaseModel> ListTH { get; set; }
    }

}
