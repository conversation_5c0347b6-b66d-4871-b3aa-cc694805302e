﻿using System;

namespace Core.Business
{
    public class FileAttachmentModel
    {
        public Guid Id { get; set; }
        public string BucketName { get; set; }
        public string ObjectName { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long? Length { get; set; }
        public int? Source { get; set; }
        public string Type { get; set; }
        public string CreateUserName { get; set; }
    }

    public class Base64FileDataResponse
    {
        /// <summary>
        /// Tiêu đề
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// D<PERSON> liệu dạng base64
        /// </summary>
        public string FileData { get; set; }
    }
}
