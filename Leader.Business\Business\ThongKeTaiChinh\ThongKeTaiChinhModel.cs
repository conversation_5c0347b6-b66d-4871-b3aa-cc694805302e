﻿using Core.Business;
using System;
using System.Collections.Generic;

namespace Leader.Business
{
    public class ThongKeTaiChinhBaseModel
    {
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public string TenKhoanThu { get; set; }
        public double? SoTienPhaiThu { get; set; }
        public decimal? SoTienDaThu { get; set; }
        public int? SoTienDaChi { get; set; }
        public double? SoTienConPhaiThu { get; set; }
    }
    public class ThongKeTaiChinhModel : ThongKeTaiChinhBaseModel
    {
        
    }
    public class ThongKeTaiChinhQueryFilter : BaseQueryFilterModel
    {
        public int IDHe { get; set; }
        public int IDKhoa { get; set; }
        public int IDNganh { get; set; }
        public int HocKy { get; set; }
        public string NamHoc { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
    }
}
