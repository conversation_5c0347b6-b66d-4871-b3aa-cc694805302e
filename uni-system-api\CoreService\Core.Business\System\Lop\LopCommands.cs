﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{

    public class CreateLopCommand : IRequest<Unit>
    {
        public CreateLopModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLopCommand(CreateLopModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLopCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLopCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LopConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLopModel, SvLop>(model);

                var checkCode = await _dataContext.SvLops.AnyAsync(x => x.TenLop == entity.TenLop);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Lop.Existed", entity.TenLop.ToString()]}");
                }

                await _dataContext.SvLops.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LopConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới lớp: {entity.TenLop}",
                    ObjectCode = LopConstant.CachePrefix,
                    ObjectId = entity.IdLop.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LopConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLopCommand : IRequest<Unit>
    {
        public UpdateLopModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public int Id { get; set; }
        public UpdateLopCommand(int Id,UpdateLopModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLopCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLopCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Model.IdLop;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LopConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLops.FirstOrDefaultAsync(dt => dt.IdLop == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var m = AutoMapperUtils.AutoMap<UpdateLopModel, SvLop>(model);
                var checkCode = await _dataContext.SvLops.AnyAsync(x => x.TenLop == m.TenLop && x.IdLop != m.IdLop);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Lop.Existed", m.TenLop.ToString()]}");
                }

                Log.Information($"Before Update {LopConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLops.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LopConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LopConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật lớp: {entity.TenLop}",
                    ObjectCode = LopConstant.CachePrefix,
                    ObjectId = entity.IdLop.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LopConstant.BuildCacheKey(entity.IdLop.ToString()));
                _cacheService.Remove(LopConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLopCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLopCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLopCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLopCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LopConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLops.FirstOrDefaultAsync(x => x.IdLop == id);

                _dataContext.SvLops.Remove(entity);

                Log.Information($"Delete {LopConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa lớp: {entity.TenLop}",
                    ObjectCode = LopConstant.CachePrefix,
                    ObjectId = entity.IdLop.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LopConstant.BuildCacheKey());
                _cacheService.Remove(LopConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
