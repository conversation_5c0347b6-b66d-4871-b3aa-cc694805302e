﻿using Core.Business;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    /// <summary>
    /// L<PERSON>y danh sách thống kê tài chính theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh thống kê tài chính</returns>
    public class GetThongKeTaiChinhByFilterQuery : IRequest<PaginationList<ThongKeTaiChinhModel>>
    {
        public ThongKeTaiChinhQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách thống kê tài chính theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetThongKeTaiChinhByFilterQuery(ThongKeTaiChinhQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetThongKeTaiChinhByFilterQuery, PaginationList<ThongKeTaiChinhModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<ThongKeTaiChinhModel>> Handle(GetThongKeTaiChinhByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreKhoanThuByConditionsAsync(filter.IDHe, filter.IDKhoa >= 0 ? filter.IDKhoa : 0, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.TuNgay, filter.DenNgay);

                var data = new List<ThongKeTaiChinhModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new ThongKeTaiChinhModel()
                    {
                        TenHe = row.Field<string>("Ten_he"),
                        TenKhoa = row.Field<string>("Ten_khoa"),
                        TenKhoanThu = row.Field<string>("Ten_thu_chi"),
                        SoTienPhaiThu = row.Field<Double?>("So_tien_phai_nop"),
                        SoTienDaThu = row.Field<Decimal?>("So_tien_da_nop"),
                        SoTienDaChi = row.Field<int?>("So_tien_hoan_tra"),
                        SoTienConPhaiThu = row.Field<Double?>("So_tien_thieu_thua")
                    });
                }

                if(filter.IDKhoa == -1)
                {
                    data = data.GroupBy(x => new { x.TenHe, x.TenKhoanThu })
                            .Select(y => new ThongKeTaiChinhModel
                            {
                                TenHe = y.Select(z => z.TenHe).FirstOrDefault(),
                                TenKhoanThu = y.Select(z => z.TenKhoanThu).FirstOrDefault(),
                                SoTienPhaiThu = y.Sum(z => z.SoTienPhaiThu),
                                SoTienDaThu = y.Sum(z => z.SoTienDaThu),
                                SoTienDaChi = y.Sum(z => z.SoTienDaChi),
                                SoTienConPhaiThu = y.Sum(z => z.SoTienConPhaiThu)
                            }).ToList();
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<ThongKeTaiChinhModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }
}
