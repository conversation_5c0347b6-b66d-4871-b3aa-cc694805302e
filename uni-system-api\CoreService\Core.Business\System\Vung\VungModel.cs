﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateVungModel
    {
        public int IdVung { get; set; }
        public string <PERSON>y<PERSON><PERSON> { get; set; }
        public string TenVung { get; set; }
        public string <PERSON>hi<PERSON>hu { get; set; }
    }
    public class UpdateVungModel : CreateVungModel
    {
        public void UpdateEntity(SvVung input)
        {
            input.IdVung = IdVung;
            input.KyHieu = KyHieu;
            input.TenVung = TenVung;
            input.GhiChu = GhiChu;
        }
    }
    public class VungSelectItemModel
    {
        public int IdVung { get; set; }
        public string KyHieu { get; set; }
        public string TenVung { get; set; }
        public string GhiChu { get; set; }
    }

    public class VungBaseModel
    {
        public int IdVung { get; set; }
        public string <PERSON>y<PERSON>ieu { get; set; }
        public string TenVung { get; set; }
        public string GhiChu { get; set; }
    }


    public class VungModel : VungBaseModel
    {

    }

    public class VungFilterModel : BaseQueryFilterModel
    {
        public VungFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdVung";
        }
    }

}
