﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDoiTuongHocBongCommand : IRequest<Unit>
    {
        public CreateDoiTuongHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDoiTuongHocBongCommand(CreateDoiTuongHocBongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDoiTuongHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDoiTuongHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DoiTuongHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDoiTuongHocBongModel, SvDoiTuongHocBong>(model);

                var checkCode = await _dataContext.SvDoiTuongHocBongs.AnyAsync(x => x.IdDoiTuongHocBong == entity.IdDoiTuongHocBong || x.DoiTuongHocBong == entity.DoiTuongHocBong || x.MaDoiTuongHocBong == entity.MaDoiTuongHocBong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocBong.Existed", entity.DoiTuongHocBong.ToString()]}");
                }

                await _dataContext.SvDoiTuongHocBongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DoiTuongHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới đối tượng học bổng: {entity.DoiTuongHocBong}",
                    ObjectCode = DoiTuongHocBongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocBong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDoiTuongHocBongCommand : IRequest<Unit>
    {
        public CreateManyDoiTuongHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDoiTuongHocBongCommand(CreateManyDoiTuongHocBongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDoiTuongHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDoiTuongHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DoiTuongHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDoiTuongHocBongAdd = model.listDoiTuongHocBongModels.Select(x => x.DoiTuongHocBong).ToList();
                var listMaDoiTuongHocBongAdd = model.listDoiTuongHocBongModels.Select(x => x.MaDoiTuongHocBong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDoiTuongHocBongModel, SvDoiTuongHocBong>(model);

                // Check data duplicate
                if (listDoiTuongHocBongAdd.Count() != listDoiTuongHocBongAdd.Distinct().Count() || listMaDoiTuongHocBongAdd.Count() != listMaDoiTuongHocBongAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvDoiTuongHocBongs.AnyAsync(x => listDoiTuongHocBongAdd.Contains(x.DoiTuongHocBong)) || await _dataContext.SvDoiTuongHocBongs.AnyAsync(x => listMaDoiTuongHocBongAdd.Contains(x.MaDoiTuongHocBong)))
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocBong.Existed"]}");
                }

                var listEntity = model.listDoiTuongHocBongModels.Select(x => new SvDoiTuongHocBong()
                {
                    IdDoiTuongHocBong = x.IdDoiTuongHocBong,
                    MaDoiTuongHocBong = x.MaDoiTuongHocBong,
                    DoiTuongHocBong = x.DoiTuongHocBong,
                    SoTienTroCap = x.SoTienTroCap,
                    PhanTramTroCap = x.PhanTramTroCap

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdDoiTuongHocBong).ToList();

                Log.Information($"Create many {DoiTuongHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import đối tượng học bổng từ file excel",
                    ObjectCode = DoiTuongHocBongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDoiTuongHocBongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDoiTuongHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDoiTuongHocBongCommand(int id, UpdateDoiTuongHocBongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDoiTuongHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDoiTuongHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DoiTuongHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDoiTuongHocBongs.FirstOrDefaultAsync(dt => dt.IdDoiTuongHocBong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvDoiTuongHocBongs.AnyAsync(x => (x.DoiTuongHocBong == model.DoiTuongHocBong || x.MaDoiTuongHocBong == model.MaDoiTuongHocBong) && x.IdDoiTuongHocBong != model.IdDoiTuongHocBong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocBong.Existed", model.DoiTuongHocBong.ToString()]}");
                }

                Log.Information($"Before Update {DoiTuongHocBongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDoiTuongHocBongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DoiTuongHocBongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DoiTuongHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật đối tượng học bổng: {entity.DoiTuongHocBong}",
                    ObjectCode = DoiTuongHocBongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocBong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey(entity.IdDoiTuongHocBong.ToString()));
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDoiTuongHocBongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDoiTuongHocBongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDoiTuongHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDoiTuongHocBongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DoiTuongHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDoiTuongHocBongs.FirstOrDefaultAsync(x => x.IdDoiTuongHocBong == id);

                _dataContext.SvDoiTuongHocBongs.Remove(entity);

                Log.Information($"Delete {DoiTuongHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa đối tượng học bổng: {entity.DoiTuongHocBong}",
                    ObjectCode = DoiTuongHocBongConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocBong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey());
                _cacheService.Remove(DoiTuongHocBongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
