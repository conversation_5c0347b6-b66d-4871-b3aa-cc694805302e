﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business.System.MonHoc
{
    public class MonHocInsertCommand : IRequest<int>
    {
        public MonHocRequestModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Insert môn học
        /// </summary>
        /// <param name="model"></param>
        /// <param name="systemLog"></param>
        public MonHocInsertCommand(MonHocRequestModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<MonHocInsertCommand, int>
        {
            private readonly ICacheService _cacheService;
            private readonly ICallStoreHelper _callStoreHelper;
            private readonly SystemDataContext _dataContext;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(ICacheService cacheService, ICallStoreHelper callStoreHelper, SystemDataContext dataContext, IStringLocalizer<Resources> localizer)
            {
                _cacheService = cacheService;
                _callStoreHelper = callStoreHelper;
                _localizer = localizer;
                _dataContext = dataContext;
            }

            public async Task<int> Handle(MonHocInsertCommand request, CancellationToken cancellationToken)
            {
                var m = request.Model;
                var systemLog = request.SystemLog;
                string actionTitle = "Thêm môn học";
                Log.Information($"{actionTitle} {MonHocConstant.CachePrefix}: " + JsonSerializer.Serialize(m));

                var entity = AutoMapperUtils.AutoMap<MonHocRequestModel, SvMonHoc>(m);

                var checkCode = await _dataContext.SvMonHocs.AnyAsync(x => x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["MonHoc.Existed", entity.KyHieu.ToString()]}");
                }
                var data = new List<MonHocSelectItemModel>();
                var rs = _callStoreHelper.MonHocInsert(m.kyHieu, m.tenMon, m.tenTiengAnh, m.idBm, m.idHeDt, m.idNhomHp);
                actionTitle += rs > 0 ? "  thành công: " : "  thất bại: ";
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"{actionTitle}: {rs}",
                    ObjectCode = MonHocConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(rs)
                });

                //Xóa cache
                _cacheService.Remove(MonHocConstant.BuildCacheKey());
                return rs;
            }
        }
    }

    public class UpdateMonHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public MonHocItemModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateMonHocCommand(int id, MonHocItemModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateMonHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateMonHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {MonHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvMonHocs.FirstOrDefaultAsync(dt => dt.IdMonHoc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.SvMonHocs.AnyAsync(x => (x.KyHieu == model.KyHieu || x.TenMon == model.TenMon) && x.IdMonHoc != model.IdMon);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["MonHoc.Existed", model.IdMon.ToString()]}");
                }

                Log.Information($"Before Update {MonHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");
                model.UpdateEntity(entity);
                _dataContext.SvMonHocs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {MonHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {MonHocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật môn học: {entity.IdMonHoc}",
                    ObjectCode = MonHocConstant.CachePrefix,
                    ObjectId = entity.IdBoMon.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MonHocConstant.BuildCacheKey(entity.IdBoMon.ToString()));
                _cacheService.Remove(MonHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteMonHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteMonHocCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteMonHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteMonHocCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {MonHocConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvMonHocs.FirstOrDefaultAsync(x => x.IdMonHoc == id);

                _dataContext.SvMonHocs.Remove(entity);

                Log.Information($"Delete {MonHocConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa môn học: {entity.IdMonHoc}",
                    ObjectCode = MonHocConstant.CachePrefix,
                    ObjectId = entity.IdBoMon.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MonHocConstant.BuildCacheKey());
                _cacheService.Remove(MonHocConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
