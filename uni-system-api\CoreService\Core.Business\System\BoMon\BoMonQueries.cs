﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System.Data;


namespace Core.Business
{
    public class GetComboboxBoMonQuery : IRequest<List<BoMonSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tôn giáo cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxBoMonQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxBoMonQuery, List<BoMonSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<BoMonSelectItemModel>> Handle(GetComboboxBoMonQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = BoMonConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbBoMons.OrderBy(x => x.BoMon)
                                select new BoMonSelectItemModel()
                                {
                                    IdBoMon = dt.IdBoMon,
                                    MaBoMon = dt.MaBoMon,
                                    BoMon = dt.BoMon
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.BoMon.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterBoMonQuery : IRequest<PaginationList<BoMonBaseModel>>
    {
        public BoMonFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tôn giáo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterBoMonQuery(BoMonFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterBoMonQuery, PaginationList<BoMonBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICallStoreHelper _callStoreHelper;

            public Handler(SystemReadDataContext dataContext, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<BoMonBaseModel>> Handle(GetFilterBoMonQuery request, CancellationToken cancellationToken)
            {

                var filter = request.Filter;
                var dataTable = _callStoreHelper.CallStoreDanhSachBoMonAsync(filter.IdMon, filter.IdCb);
                //lib
                var data = new List<BoMonBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<BoMonBaseModel>());
                }
                if (filter.IdBoMon > 0)
                {
                    data = data.Where(x => x.IdBoMon == filter.IdBoMon).ToList();
                }

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count
                int totalCount = data.Count;

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination to List<T>
                var paginatedData = data.Skip(excludedRows).Take(filter.PageSize).ToList();

                return new PaginationList<BoMonBaseModel>()
                {
                    DataCount = paginatedData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = paginatedData
                };
            }
        }

    }

    public class GetBoMonByIdQuery : IRequest<BoMonModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Tôn giáo theo id
        /// </summary>
        /// <param name="id">Id tôn giáo</param>
        public GetBoMonByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetBoMonByIdQuery, BoMonModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<BoMonModel> Handle(GetBoMonByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = BoMonConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbBoMons.FirstOrDefaultAsync(x => x.IdBoMon == id);

                    return AutoMapperUtils.AutoMap<TkbBoMon, BoMonModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetFilterGiangVienTheoBoMonQuery : IRequest<PaginationList<GiangVienBaseModel>>
    {
        public GiangVienFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Bộ môn có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterGiangVienTheoBoMonQuery(GiangVienFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterGiangVienTheoBoMonQuery, PaginationList<GiangVienBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private ICallStoreHelper _callStoreHelper;
            public Handler(SystemReadDataContext dataContext, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<GiangVienBaseModel>> Handle(GetFilterGiangVienTheoBoMonQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                var dataTable = _callStoreHelper.CallStoreDanhSachGiangVienTheoBoMonAsync(filter.IdBm);
                //lib
                var data = new List<GiangVienBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<GiangVienBaseModel>());
                }



                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count
                int totalCount = data.Count;

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination to List<T>
                var paginatedData = data.Skip(excludedRows).Take(filter.PageSize).ToList();

                return new PaginationList<GiangVienBaseModel>()
                {
                    DataCount = paginatedData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = paginatedData
                };
            }
        }
    }

    public class GetFilterGiangVienChuaGanBoMonQuery : IRequest<PaginationList<GiangVienBaseModel>>
    {
        public GiangVienFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Bộ môn có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterGiangVienChuaGanBoMonQuery(GiangVienFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterGiangVienChuaGanBoMonQuery, PaginationList<GiangVienBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICallStoreHelper _callStoreHelper;
            public Handler(SystemReadDataContext dataContext, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<GiangVienBaseModel>> Handle(GetFilterGiangVienChuaGanBoMonQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                var dataTable = _callStoreHelper.CallStoreDanhSachGiangVienChuaGanBoMonAsync(filter.IdBm);
                //lib
                var data = new List<GiangVienBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<GiangVienBaseModel>());
                }



                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count
                int totalCount = data.Count;

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination to List<T>
                var paginatedData = data.Skip(excludedRows).Take(filter.PageSize).ToList();

                return new PaginationList<GiangVienBaseModel>()
                {
                    DataCount = paginatedData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = paginatedData
                };
            }
        }
    }

    public class GetFilterMonHocTheoBoMonQuery : IRequest<PaginationList<MonHocBaseModel>>
    {
        public MonHocModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Bộ môn có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterMonHocTheoBoMonQuery(MonHocModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterMonHocTheoBoMonQuery, PaginationList<MonHocBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private ICallStoreHelper _callStoreHelper;
            public Handler(SystemReadDataContext dataContext, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<MonHocBaseModel>> Handle(GetFilterMonHocTheoBoMonQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                var dataTable = _callStoreHelper.CallStoreDanhSachMonHocTheoBoMonAsync(filter.IdBm);
                //lib
                var data = new List<MonHocBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<MonHocBaseModel>());
                }



                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count
                int totalCount = data.Count;

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination to List<T>
                var paginatedData = data.Skip(excludedRows).Take(filter.PageSize).ToList();

                return new PaginationList<MonHocBaseModel>()
                {
                    DataCount = paginatedData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = paginatedData
                };
            }
        }
    }

    public class GetFilterMonHocChuaGanBoMonQuery : IRequest<PaginationList<MonHocBaseModel>>
    {
        public MonHocModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Bộ môn có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterMonHocChuaGanBoMonQuery(MonHocModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterMonHocChuaGanBoMonQuery, PaginationList<MonHocBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICallStoreHelper _callStoreHelper;
            public Handler(SystemReadDataContext dataContext, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<MonHocBaseModel>> Handle(GetFilterMonHocChuaGanBoMonQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                //  var data = new List<MonHocBaseModel>();
                var data = (from dt in _dataContext.SvMonHocs.OrderBy(x => x.TenMon)
                            where dt.IdBoMon != filter.IdBm
                            select new MonHocBaseModel()
                            {
                                IdMon = dt.IdMonHoc,
                                TenMon = dt.TenMon,
                                KyHieu = dt.KyHieu
                            });



                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<MonHocBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
