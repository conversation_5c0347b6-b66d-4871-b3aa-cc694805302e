﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Core.Business.Core;
using Core.API.Shared;
using Core.Business.System;
using System.Linq;

namespace Core.API
{
    /// <summary>
    /// JWT cho hệ thống
    /// </summary>
    [ApiController]
    [Route("auth/v1/authentication")]
    [ApiExplorerSettings(GroupName = "00. Authentication")]
    public class AuthenticationController : ApiControllerBase
    {
        private readonly ILdapManager _ldapManager;


        private static readonly HttpClient Client = new HttpClient();

        public AuthenticationController(IMediator mediator, ILdapManager ldapManager, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
            _ldapManager = ldapManager;
        }

        #region Đăng nhập/get token

        /// <summary>
        /// Đăng nhập trả về token JWT
        /// </summary>
        /// <param name="login">Model đăng nhập</param>
        /// <returns></returns>
        [Route("jwt/login")]
        [AllowAnonymous, HttpPost]
        public async Task<IActionResult> SignInJwt([FromBody] LoginModel login)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var res = new OAuthTokenModel();

                IActionResult response = Helper.TransformData(new ResponseObject<bool>(false));
                double timeToLive = Convert.ToDouble(_config["Authentication:Jwt:TimeToLive"]);
                //if (login.Username == _config["Authentication:AdminUser"] && login.Password == _config["Authentication:AdminPassWord"])
                //{
                //    u.SystemLog.ListAction.Add(new ActionDetail("Đăng nhập thành công bằng tài khoản Admin (fix)"));
                //    //Lấy về danh sách quyền của người dùng
                //    var tokenString = AuthenticationHelper.BuildToken(_config, new UserLoginModel()
                //    {
                //        UserName = _config["Authentication:AdminUser"],
                //        Email = "<EMAIL>",
                //        Id = UserConstants.AdministratorId,
                //        ListRole = new List<string>(),
                //        ListRight = new List<string>()
                //    }, login.RememberMe, timeToLive);
                //    response = Helper.TransformData(new ResponseObject<LoginResponse>(new LoginResponse()
                //    {
                //        TokenString = tokenString,
                //        UserId = UserConstants.AdministratorId,
                //        TimeExpride = DateTime.UtcNow.AddSeconds(timeToLive),
                //        ListRight = new List<string>(),
                //        ListRole = new List<string>(),
                //        UserModel = new BaseUserModel()
                //        {
                //            Id = UserConstants.AdministratorId,
                //            UserName = _config["Authentication:AdminUser"],
                //            Email = "<EMAIL>",
                //            Name = "Administrator",
                //            OrganizationId = null
                //        }
                //    }));
                //    return response;
                //}
                //if (login.Username == _config["Authentication:GuestUser"] && login.Password == _config["Authentication:GuestPassWord"])
                //{
                //    u.SystemLog.ListAction.Add(new ActionDetail("Đăng nhập thành công bằng tài khoản GuestUser (fix)"));
                //    var tokenString = AuthenticationHelper.BuildToken(_config, new UserLoginModel()
                //    {
                //        UserName = _config["Authentication:GuestUser"],
                //        Email = "<EMAIL>",
                //        Id = UserConstants.UserId,
                //    }, login.RememberMe, timeToLive);
                //    response = Helper.TransformData(new ResponseObject<LoginResponse>(new LoginResponse()
                //    {
                //        TokenString = tokenString,
                //        UserId = UserConstants.UserId,
                //        TimeExpride = DateTime.UtcNow.AddSeconds(timeToLive),
                //        UserModel = new BaseUserModel()
                //        {
                //            Id = UserConstants.UserId,
                //            UserName = _config["Authentication:GuestUser"],
                //            Email = "<EMAIL>"
                //        }
                //    }));
                //    return response;
                //}

                // Check user login from database

                var userModel = await _mediator.Send(new ValidateUserAuthQuery(login.Username, login.Password, u.SystemLog));
                if (userModel != null)
                {
                    string phanHe = Request.Headers["phanHe"].ToString();
                    var listPhanHe = await _mediator.Send(new GetComboboxPhanHeQuery());
                    var idPhanHe = listPhanHe.FirstOrDefault(x => x.PhanHe == phanHe)?.IdPh;
                    UserPermissionModel userPermission = new UserPermissionModel();
                    if (idPhanHe != null)
                    {
                        userPermission = await _mediator.Send(new GetUserPermissionQuery(userModel.UserId, idPhanHe.Value));
                    }
                    res.ExpiresIn = (int)timeToLive;
                    res.AccessToken = JwtService.GenerateToken(_config, new UserLoginModel()
                    {
                        UserName = userModel.UserName,
                        Id = userModel.UserId
                    }, login.RememberMe);
                    res.UserInfo = new UserAuthModel()
                    {
                        Id = userModel.UserId,
                        IsActive = userModel.Active == 1,
                        MaCanBo = userModel.MaCanBoUser,
                        FullName = userModel.FullName,
                        UserName = userModel.UserName,
                        Roles = idPhanHe != null ? userPermission.Roles : null,
                    };
                }

                return res;
            });
        }

        /// <summary>
        /// Đăng nhập trả về token JWT
        /// </summary>
        /// <param name="login">Model đăng nhập</param>
        /// <returns></returns>
        [Route("ldap/login")]
        [AllowAnonymous, HttpPost]
        public async Task<IActionResult> SignInLDAPJwt([FromBody] LoginModel login)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                bool isValied = new LdapManager(_config).ValidateAuthentication(login.Username, login.Password);

                return isValied;
            });
        }
        #endregion

        #region Cập nhật mật khẩu mới

        /// <summary>
        /// Cập nhật mật khẩu sử dụng phương thức quên mật khẩu được gửi qua email
        /// </summary>
        /// <param name="data">Model đăng nhập</param>
        /// <returns></returns>
        [Route("forgot/update-password")]
        [AllowAnonymous, HttpPost]
        public async Task<IActionResult> UpdatePasswordUserFromForgotPass([FromBody] UserUpdatePasswordFromForgotPassModel data)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_UPDATE_PASS_FORGOT);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_UPDATE_PASS_FORGOT;

                var res = await _mediator.Send(new UpdatePasswordUserFromForgotPassCommand(data, u.SystemLog));
                return res;
            });
        }

        /// <summary>
        /// Cập nhật mật khẩu sử dụng phương thức quên mật khẩu được gửi qua email
        /// </summary>
        /// <param name="data">Model đăng nhập</param>
        /// <returns></returns>
        [Route("change-password")]
        [Authorize, HttpPost]
        public async Task<IActionResult> ChangePasswordUser([FromBody] UserChangePasswordModel data)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_UPDATE_PASS_FORGOT);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_UPDATE_PASS_FORGOT;

                data.UserId = u.UserId;

                var res = await _mediator.Send(new ChangePasswordUserCommand(data, u.SystemLog));
                return res;
            });
        }

        #endregion
    }
}