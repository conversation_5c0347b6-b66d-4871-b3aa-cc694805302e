﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Xóa nhật ký quên mật khẩu
    /// </summary>
    /// <param name="Id">Id nhật ký quên mật khẩu</param>
    /// <returns>Danh sách kết quả xóa</returns>
    public class ForgotPasswordLogDeleteCommand : IRequest<Unit>
    {
        public string Id { get; set; }

        public ForgotPasswordLogDeleteCommand(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogDeleteCommand, Unit>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<Unit> Handle(ForgotPasswordLogDeleteCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<ForgotPasswordLog>.Filter.And(Builders<ForgotPasswordLog>.Filter.Where(p => request.Id == p.Id));

                    await _logs.DeleteManyAsync(builder).ConfigureAwait(false);
                }
                else
                {
                    int idLog = 0;
                    int.TryParse(request.Id, out idLog);
                    var dt = await _dataContext.ForgotPasswordLogs.FirstOrDefaultAsync(x => x.Id == idLog);
                    _dataContext.ForgotPasswordLogs.Remove(dt);
                    await _dataContext.SaveChangesAsync(cancellationToken);
                }
                return Unit.Value;
            }
        }
    }
}
