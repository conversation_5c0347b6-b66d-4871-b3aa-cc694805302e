﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateVungCommand : IRequest<Unit>
    {
        public CreateVungModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateVungCommand(CreateVungModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateVungCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateVungCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {VungConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateVungModel, SvVung>(model);

                var checkCode = await _dataContext.SvVungs.AnyAsync(x => x.IdVung == entity.IdVung || x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Vung.Existed", entity.KyHieu.ToString()]}");
                }

                await _dataContext.SvVungs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {VungConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới vùng : {entity.KyHieu}",
                    ObjectCode = VungConstant.CachePrefix,
                    ObjectId = entity.IdVung.ToString()
                });

                //Xóa cache
                _cacheService.Remove(VungConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
