﻿namespace Core.Shared.Model
{
    public class ExternalAuthenticatorConfig
    {
        public AuthenticatorConfig GoogleAuthenticator { get; set; }
        public AuthenticatorConfig MicrosoftAuthenticator { get; set; }
    }

    public class AuthenticatorConfig
    {
        public bool IsEnabled { get; set; }
        public string Authority { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string CallbackPath { get; set; }
        public string Scope { get; set; }
    }
}
