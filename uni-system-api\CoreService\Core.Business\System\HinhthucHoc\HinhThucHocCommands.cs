﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateHinhThucHocCommand : IRequest<Unit>
    {
        public CreateHinhThucHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHinhThucHocCommand(CreateHinhThucHocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHinhThucHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHinhThucHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HinhThucHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHinhThucHocModel, SvHinhThucHoc>(model);

                var checkCode = await _dataContext.SvHinhThucHocs.AnyAsync(x => x.IdHinhThucHoc == entity.IdHinhThucHoc || x.TenHinhThucHoc == entity.TenHinhThucHoc || x.MaHinhThucHoc == entity.MaHinhThucHoc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HinhThucHoc.Existed", entity.TenHinhThucHoc.ToString()]}");
                }

                await _dataContext.SvHinhThucHocs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HinhThucHocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới hình thức học: {entity.TenHinhThucHoc}",
                    ObjectCode = HinhThucHocConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucHoc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHinhThucHocCommand : IRequest<Unit>
    {
        public CreateManyHinhThucHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHinhThucHocCommand(CreateManyHinhThucHocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHinhThucHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHinhThucHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HinhThucHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHinhThucHocAdd = model.listHinhThucHocModels.Select(x => x.TenHinhThucHoc).ToList();
                var listMaHinhThucHocAdd = model.listHinhThucHocModels.Select(x => x.MaHinhThucHoc).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyHinhThucHocModel, SvHinhThucHoc>(model);

                // Check data duplicate
                if (listHinhThucHocAdd.Count() != listHinhThucHocAdd.Distinct().Count() || listMaHinhThucHocAdd.Count() != listMaHinhThucHocAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvHinhThucHocs.AnyAsync(x => listHinhThucHocAdd.Contains(x.TenHinhThucHoc)) || await _dataContext.SvHinhThucHocs.AnyAsync(x => listMaHinhThucHocAdd.Contains(x.MaHinhThucHoc)))
                {
                    throw new ArgumentException($"{_localizer["HinhThucHoc.Existed"]}");
                }

                var listEntity = model.listHinhThucHocModels.Select(x => new SvHinhThucHoc()
                {
                    IdHinhThucHoc = x.IdHinhThucHoc,
                    MaHinhThucHoc = x.MaHinhThucHoc,
                    TenHinhThucHoc = x.TenHinhThucHoc,
                    GhiChu = x.GhiChu

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHinhThucHoc).ToList();

                Log.Information($"Create many {HinhThucHocConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import hình thức học từ file excel",
                    ObjectCode = HinhThucHocConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHinhThucHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHinhThucHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHinhThucHocCommand(int id, UpdateHinhThucHocModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHinhThucHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHinhThucHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HinhThucHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvHinhThucHocs.FirstOrDefaultAsync(dt => dt.IdHinhThucHoc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvHinhThucHocs.AnyAsync(x => (x.TenHinhThucHoc == model.TenHinhThucHoc || x.MaHinhThucHoc == model.MaHinhThucHoc) && x.IdHinhThucHoc != model.IdHinhThucHoc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HinhThucHoc.Existed", model.TenHinhThucHoc.ToString()]}");
                }

                Log.Information($"Before Update {HinhThucHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvHinhThucHocs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HinhThucHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HinhThucHocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật hình thức học: {entity.TenHinhThucHoc}",
                    ObjectCode = HinhThucHocConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucHoc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey(entity.IdHinhThucHoc.ToString()));
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHinhThucHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHinhThucHocCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHinhThucHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHinhThucHocCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HinhThucHocConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvHinhThucHocs.FirstOrDefaultAsync(x => x.IdHinhThucHoc == id);

                _dataContext.SvHinhThucHocs.Remove(entity);

                Log.Information($"Delete {HinhThucHocConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa hình thức học: {entity.TenHinhThucHoc}",
                    ObjectCode = HinhThucHocConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucHoc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey());
                _cacheService.Remove(HinhThucHocConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
