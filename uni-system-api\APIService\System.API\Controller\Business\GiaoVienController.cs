﻿using Core.API.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Business;

namespace Core.API.Controllers
{
    [ApiController]
    [Route("system/v1/giao-vien")]
    [ApiExplorerSettings(GroupName = "84. Giáo viên")]
    [Authorize]
    public class GiaoVienController : ApiControllerBase
    {
        public GiaoVienController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// L<PERSON>y danh sách giáo viên cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TKBGiaoVienSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTKBGiaoVienQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách thông tin giáo viên
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("get-detail")]
        [ProducesResponseType(typeof(ResponseObject<List<GiaoVienModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListGiaoVien()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetListGiaoVienQuery());
            });
        }
    }
}
