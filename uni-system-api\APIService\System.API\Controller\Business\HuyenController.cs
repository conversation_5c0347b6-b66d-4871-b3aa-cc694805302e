﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/huyen")]
    [ApiExplorerSettings(GroupName = "22. Huyện")]
    [Authorize]
    public class HuyenController : ApiControllerBase
    {
        public HuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách huyện cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HuyenSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHuyenQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách huyện có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HuyenBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HuyenFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHuyenQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết huyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HuyenModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] string id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHuyenByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới huyện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HUYEN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HUYEN_CREATE;


                return await _mediator.Send(new CreateHuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel huyện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HUYEN_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HUYEN_CREATE_MANY;


                return await _mediator.Send(new CreateManyHuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa huyện
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_EDIT))]
        public async Task<IActionResult> Update(string id, [FromBody] UpdateHuyenModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HUYEN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HUYEN_UPDATE;
                return await _mediator.Send(new UpdateHuyenCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa huyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HUYEN_DELETE))]
        public async Task<IActionResult> Delete(string id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HUYEN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HUYEN_DELETE;

                return await _mediator.Send(new DeleteHuyenCommand(id, u.SystemLog));
            });
        }

    }
}

