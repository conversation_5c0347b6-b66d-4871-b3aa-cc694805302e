﻿using Core.API.Shared;
using Core.Business;
using Core.Business.Core;
using Serilog;
using System.Threading.Tasks;
using System.Threading;

namespace System.API.CronJob
{
    public class ClearCacheCronJobService : ICronJob
    {
        private readonly ICacheService _cacheService;
        
        public ClearCacheCronJobService(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }
        public Task Run(CancellationToken token = default)
        {
            Log.Information($"ClearCacheCronJobService at: " + DateTime.Now.ToString());

            _cacheService.RemoveAll();

            return Task.CompletedTask;
        }
    }
}
