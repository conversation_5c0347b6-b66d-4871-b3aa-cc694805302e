﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svHinhThucHoc")]
    public class SvHinhThucHoc
    {

        public SvHinhThucHoc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_hinh_thuc_hoc")]
        public int IdHinhThucHoc { get; set; }

        [Column("Ma_hinh_thuc_hoc"), MaxLength(50)]
        public string MaHinhThucHoc { get; set; }

        [Column("Ten_hinh_thuc_hoc"), MaxLength(50)]
        public string TenHinhThucHoc { get; set; }

        [Column("Ghi_chu"), MaxLength(200)]
        public string GhiChu { get; set; }


    }
}
