﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteMauChuKyCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Xóa mẫu chữ ký theo Id truyền vào
        /// </summary>
        /// <param name="id">Id mẫu chữ ký cần xóa</param>
        public DeleteMauChuKyCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteMauChuKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteMauChuKyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {MauChuKyConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SgMauChuKys.FirstOrDefaultAsync(x => x.Id == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SgMauChuKys.Remove(dt);

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa mẫu chữ ký mã: {dt.Code}",
                    ObjectCode = MauChuKyConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(MauChuKyConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(MauChuKyConstant.BuildCacheKey());

                Log.Information($"Delete {MauChuKyConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
