#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base

RUN  ["rm", "-rf", "/etc/localtime"]
RUN  ["ln", "-s", "/usr/share/zoneinfo/Asia/Ho_Chi_Minh", "/etc/localtime"]

USER app
WORKDIR /app

EXPOSE 80
ENV ASPNETCORE_URLS=http://+:80

COPY ./web-api-gw-publish /app

ENTRYPOINT ["dotnet", "WebAPIGateway.dll"]