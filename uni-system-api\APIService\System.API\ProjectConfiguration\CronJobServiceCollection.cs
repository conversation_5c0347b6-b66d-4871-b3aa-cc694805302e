using Microsoft.Extensions.DependencyInjection;
using Core.Business.Core;
using System.API.CronJob;
using Core.API.Shared;
using Core.Shared;
using Serilog;
using Microsoft.Extensions.Configuration;

namespace Core.API
{
    public static class CronJobServiceCollection
    {
        /// <summary>
        /// CronJobService
        /// https://crontab.guru/
        /// </summary>
        /// <param name="services"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCronJobServiceComponents(this IServiceCollection services, IConfiguration config)
        {
            if (config["AppSettings:ClearCacheCronJob:Enable"] == "true")
            {
                services.AddCronJob<ClearCacheCronJobService>(config["AppSettings:ClearCacheCronJob:CronExpression"]);
            }

            return services;
        }
    }
}