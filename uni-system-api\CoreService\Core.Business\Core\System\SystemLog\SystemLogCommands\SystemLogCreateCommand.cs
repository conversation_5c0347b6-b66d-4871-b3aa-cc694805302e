﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Thêm mới nhật ký hệ thống
    /// </summary>
    /// <param name="model">Model thêm mới nhật ký hệ thống</param>
    /// <returns>Id nhật ký hệ thống</returns>
    public class SystemLogCreateCommand : IRequest<Unit>
    {
        public SystemLog Model { get; set; }

        public SystemLogCreateCommand(SystemLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<SystemLogCreateCommand, Unit>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly IServiceScopeFactory _serviceScopeFactory;

            public Handler(IMongoDBDatabaseSettings settings, IServiceScopeFactory serviceScopeFactory)
            {
                _settings = settings;
                _serviceScopeFactory = serviceScopeFactory;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<Unit> Handle(SystemLogCreateCommand request, CancellationToken cancellationToken)
            {
                request.Model.Id = string.Empty;
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    await _logs.InsertOneAsync(request.Model);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<SystemLog, SystemLogEntity>(request.Model);
                    dt.Id = 0;
                    // Vì hàm này chạy sau khi hoàn thành request nên phải tạo scope mới nếu không sẽ bị lỗi DataContext is disposed
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var context = scope.ServiceProvider.GetRequiredService<SystemDataContext>();
                        context.SystemLogs.Add(dt);
                        await context.SaveChangesAsync();
                    }
                }

                return Unit.Value;
            }
        }
    }
}
