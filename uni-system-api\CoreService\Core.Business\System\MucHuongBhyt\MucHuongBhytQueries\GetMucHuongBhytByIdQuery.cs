﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetMucHuongBhytByIdQuery : IRequest<MucHuongBhytModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin mức hưởng bhyt theo id
        /// </summary>
        /// <param name="id">Id mức hưởng bhyt</param>
        public GetMucHuongBhytByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetMucHuongBhytByIdQuery, MucHuongBhytModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<MucHuongBhytModel> Handle(GetMucHuongBhytByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = MucHuongBhytConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvMucHuongBhyts.FirstOrDefaultAsync(x => x.IdMucHuongBhyt == id);

                    return AutoMapperUtils.AutoMap<SvMucHuongBhyt, MucHuongBhytModel>(entity);
                });
                return item;
            }
        }
    }
}
