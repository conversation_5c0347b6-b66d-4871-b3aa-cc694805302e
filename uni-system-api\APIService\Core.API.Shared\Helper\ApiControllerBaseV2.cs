﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Security.Authentication;
using Core.Shared;
using Serilog;
using Core.Business;
using MediatR;
using Microsoft.Extensions.Localization;
using Minio.Exceptions;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Serilog.Context;
using Core.Shared.ContextAccessor;

namespace Core.API.Shared
{
    public class ApiControllerBaseV2 : ControllerBase
    {
        protected readonly IContextAccessor _contextAccessor;
        protected readonly IMediator _mediator;
        protected readonly IStringLocalizer<Resources> _localizer;
        protected readonly IConfiguration _config;
        private readonly bool showRequestDuration = false;

        public ApiControllerBaseV2(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config
            )
        {
            _contextAccessor = contextAccessorFactory();
            _mediator = mediator;
            _localizer = localizer;
            _config = config;
            showRequestDuration = _config["AppSettings:ShowRequestDuration"] == "true";
        }

        protected async Task<IActionResult> ExecuteFunction<T>(Func<Task<T>> func)
        {
            var timer = new Stopwatch();

            if (showRequestDuration)
                timer.Start();

            LogContext.PushProperty("TraceIdentifier", _contextAccessor.TraceId);

            await GetCurrentUserInfo();

            try
            {
                var result = await func();

                if (showRequestDuration)
                    timer.Stop();

                if (_contextAccessor.SystemLog.ListAction.Any())
                {
                    _contextAccessor.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(_contextAccessor.SystemLog)).ConfigureAwait(false);
                }

                if (result == null)
                {
                    Log.Information($"Data return null");
                    return Helper.TransformData(new ResponseError(Code.NotFound, $"{_localizer["data.not-found"]}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }

                if (result is Response)
                {
                    var rs = result as Response;
                    rs.TraceId = _contextAccessor.TraceId;
                    rs.RequestDuration = timer.ElapsedMilliseconds;
                    return Helper.TransformData(rs);
                }
                if (result is IActionResult)
                {
                    return (IActionResult)result;
                }
                return Helper.TransformData(new ResponseObject<T>(result) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ArgumentException agrEx)
            {
                if (showRequestDuration)
                    timer.Stop();
                Log.Information("{Error}", agrEx.ToString());

                return Helper.TransformData(new ResponseError(Code.BadRequest, agrEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (NullReferenceException nullEx)
            {
                Log.Debug("{Error}", nullEx.ToString());

                timer.Stop();
                return Helper.TransformData(new ResponseError(Code.NotFound, nullEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ForbiddenException forbiddenEx)
            {
                Log.Debug("{Error}", forbiddenEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Forbidden, forbiddenEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (AuthenticationException authEx)
            {
                Log.Debug("{Error}", authEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Unauthorized, authEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());

                if (showRequestDuration)
                    timer.Stop();
                if (_config["AppSettings:ReturnDetailError500Message"] == "true")
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500"]} {ex.Message}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
                else
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500-minimum"]}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
            }
        }

        protected async Task<IActionResult> ExecuteFunction<T>(Func<T> func)
        {
            var timer = new Stopwatch();
            if (showRequestDuration)
                timer.Start();

            LogContext.PushProperty("TraceIdentifier", _contextAccessor.TraceId.ToString());

            await GetCurrentUserInfo();

            try
            {
                var result = func();

                if (showRequestDuration)
                    timer.Stop();

                if (_contextAccessor.SystemLog.ListAction.Any())
                {
                    _contextAccessor.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(_contextAccessor.SystemLog)).ConfigureAwait(false);
                }

                if (result == null)
                {
                    Log.Information($"Data return null");
                    return Helper.TransformData(new ResponseError(Code.NotFound, $"{_localizer["data.not-found"]}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }

                if (result is Response)
                {
                    var rs = result as Response;
                    rs.TraceId = _contextAccessor.TraceId;
                    return Helper.TransformData(rs);
                }
                if (result is IActionResult)
                {
                    return (IActionResult)result;
                }
                return Helper.TransformData(new ResponseObject<T>(result) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ArgumentException agrEx)
            {
                if (showRequestDuration)
                    timer.Stop();
                Log.Information("{Error}", agrEx.ToString());

                return Helper.TransformData(new ResponseError(Code.BadRequest, agrEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (NullReferenceException nullEx)
            {
                Log.Debug("{Error}", nullEx.ToString());

                timer.Stop();
                return Helper.TransformData(new ResponseError(Code.NotFound, nullEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ForbiddenException forbiddenEx)
            {
                Log.Debug("{Error}", forbiddenEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Forbidden, forbiddenEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (AuthenticationException authEx)
            {
                Log.Debug("{Error}", authEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Unauthorized, authEx.Message) { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());

                if (showRequestDuration)
                    timer.Stop();
                if (_config["AppSettings:ReturnDetailError500Message"] == "true")
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500"]} {ex.Message}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
                else
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500-minimum"]}") { TraceId = _contextAccessor.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
            }
        }

        private async Task GetCurrentUserInfo()
        {
            var currentUser = await Helper.GetRequestInfo(HttpContext.Request);

            _contextAccessor.SystemLog = currentUser.SystemLog;
            _contextAccessor.SystemLog.TraceId = _contextAccessor.TraceId;

            // Lấy thông tin người dùng đang truy cập để gán vào log
            var listUser = await _mediator.Send(new GetComboboxUserQuery());
            var user = listUser.FirstOrDefault(x => x.UserId == currentUser.UserId);
            _contextAccessor.UserName = user?.UserName;
            _contextAccessor.SystemLog.UserName = user?.UserName;
            _contextAccessor.MaCB = user?.MaCB;

            // Kiểm tra xem điều kiện có lấy thông tin giáo viên trong middleware không
            if (_config["AppSettings:EnableLoadTeacherInfoInMiddleware"] == "true")
            {
                var listTeacher = await _mediator.Send(new GetComboboxTKBGiaoVienQuery());
                var teacher = listTeacher.FirstOrDefault(x => !string.IsNullOrEmpty(x.MaCB) && x.MaCB == user?.MaCB);
                _contextAccessor.GiaoVienId = teacher?.Id;
            }
        }
    }
}
