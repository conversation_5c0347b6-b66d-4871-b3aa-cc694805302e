﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDanToc")]
    public class SvDanToc
    {
        public SvDanToc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_dan_toc")]
        public int IdDanToc { get; set; }

        [Column("Ma_dan_toc"), MaxLength(5)]
        public string MaDanToc { get; set; }

        [Column("Dan_toc"), MaxLength(50)]
        public string DanToc { get; set; }

        [Column("Dan_toc_en"), MaxLength(50)]
        public string DanTocEn { get; set; }
    }
}
