﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Leader.Data
{
    public partial class LeaderReadDataContext : DbContext
    {
        public LeaderReadDataContext()
        {
        }

        public LeaderReadDataContext(DbContextOptions<LeaderReadDataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<TuyenSinhPhuongThucXetTuyen> TuyenSinhPhuongThucXetTuyens { get; set; }
        public virtual DbSet<SvHe> SvHes { get; set; }
        public virtual DbSet<SvKhoa> SvKhoas { get; set; }
        public virtual DbSet<SvNganh> SvNganhs { get; set; }
        public virtual DbSet<SvChuyenNganh> SvChuyenNganhs { get; set; }
        public virtual DbSet<SvLop> SvLops { get; set; }
        public virtual DbSet<TkbHocKyDangKy> TkbHocKyDangKys { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
