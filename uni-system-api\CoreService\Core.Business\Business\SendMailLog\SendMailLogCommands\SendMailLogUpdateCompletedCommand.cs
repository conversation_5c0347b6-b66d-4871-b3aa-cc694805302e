﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Cập nhật trạng thái nhật ký gửi mail
    /// </summary>
    /// <param name="model">Model cập nhật nhật ký gửi mail</param>
    /// <returns>Id nhật ký gửi mail</returns>
    public class SendMailLogUpdateCompletedCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public SendMailStatus SendMailStatus { get; set; }

        public SendMailLogUpdateCompletedCommand(string id, SendMailStatus sendMailStatus)
        {
            Id = id;
            SendMailStatus = sendMailStatus;
        }

        public class Handler : IRequestHandler<SendMailLogUpdateCompletedCommand, Unit>
        {
            private readonly IMongoCollection<SendMailLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SendMailLog>(MongoCollections.SendMailLog);
                }
            }

            public async Task<Unit> Handle(SendMailLogUpdateCompletedCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var filter = Builders<SendMailLog>.Filter.Eq(doc => doc.Id, request.Id);
                    var update = Builders<SendMailLog>.Update.Set(doc => doc.SendMailStatus, request.SendMailStatus.GetHashCode());
                    var result = await _logs.UpdateOneAsync(filter, update);
                }
                else
                {
                    // Không sử dụng MongoDB
                    int idLog = 0;
                    int.TryParse(request.Id, out idLog);
                    var dt = await _dataContext.SendMailLogs.FirstOrDefaultAsync(x => x.Id == idLog);
                    if (dt != null)
                    {
                        dt.SendMailStatus = request.SendMailStatus.GetHashCode();
                        _dataContext.SendMailLogs.Update(dt);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }
                return Unit.Value;
            }
        }
    }
}
