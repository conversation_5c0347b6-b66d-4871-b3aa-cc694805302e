{
  "AppSettings": {
    "Tittle": "Uni Core WebAPI Gateway",
    "Description": "Net Core Framework",
    "TermsOfService": "",
    "Contact": {
      "Name": "HaiND",
      "Email": "<EMAIL>",
      "Url": "https://github.com/duchaindh94"
    },
    "CorsOrigins": "*,http://localhost:8100,http://localhost:8000",
    "EnableSwagger": "false",
    "UseSerilogRequestLogging": "true",
    "EnableRequestResponseLoggingMiddleware": "false"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Core.API.Shared.CustomAuthHandler": "Error",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error",
        "Ocelot.RateLimiting.Middleware.RateLimitingMiddleware": "Warning",
        "Ocelot.Authentication.Middleware.AuthenticationMiddleware": "Warning",
        "Ocelot.Authorization.Middleware.AuthorizationMiddleware": "Warning",
        "Ocelot.Requester.Middleware.HttpRequesterMiddleware": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "File",
              "Args": {
                "path": "/opt/logs_folder/api-gw/log-.txt",
                "rollingInterval": "Day",
                "fileSizeLimitBytes": 10485760,
                "retainedFileCountLimit": 100,
                "rollOnFileSizeLimit": true,
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}] {MachineName} ({ThreadId}) <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "Console",
              "Args": {
                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}] {MachineName} ({ThreadId}) <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "GrafanaLoki",
              "Args": {
                "uri": "http://localhost:3100",
                "credentials": {
                  "login": "grafanalokiuser",
                  "password": "grafanalokipass"
                },
                "labels": [
                  {
                    "key": "app",
                    "value": "web-api-gateway-test"
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  },
  "AllowedHosts": "*",
  "Routes": [
    {
      "DownstreamPathTemplate": "/v1/secrets/data/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "vault.unisoft.edu.vn",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/vault/v1/{everything}",
      "UpstreamHttpMethod": [ "GET" ]
    },
    {
      "DownstreamPathTemplate": "/hrm/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8001
        }
      ],
      "UpstreamPathTemplate": "/hrm/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/admission/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8004
        }
      ],
      "UpstreamPathTemplate": "/admission/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/auth/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8002
        }
      ],
      "UpstreamPathTemplate": "/auth/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/system/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8002
        }
        //{
        //  "Host": "localhost",
        //  "Port": 8072
        //}
      ],
      "UpstreamPathTemplate": "/system/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
      //"LoadBalancerOptions": {
      //  "Type": "DCDRLoadBalancer"
      //}
    },
    {
      "DownstreamPathTemplate": "/signalr_hub/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8002
        }
      ],
      "UpstreamPathTemplate": "/signalr_hub/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "UpstreamPathTemplate": "/",
      "DownstreamPathTemplate": "/signalr_hub",
      "DownstreamScheme": "ws",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8002
        }
      ]
    },
    {
      "DownstreamPathTemplate": "/teacher/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8007
        }
      ],
      "UpstreamPathTemplate": "/teacher/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/eledger/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8011
        }
      ],
      "UpstreamPathTemplate": "/eledger/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/science/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8014
        }
      ],
      "UpstreamPathTemplate": "/science/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/student/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8008
        }
      ],
      "UpstreamPathTemplate": "/student/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/leader/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8006
        }
      ],
      "UpstreamPathTemplate": "/leader/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/admission-portal/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8005
        }
      ],
      "UpstreamPathTemplate": "/admission-portal/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/survey/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 8013
        }
      ],
      "UpstreamPathTemplate": "/survey/v1/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    }
  ],
  "SwaggerEndPoints": [
    {
      "Key": "system",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "System API",
          "Version": "v1",
          "Url": "http://localhost:8002/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "hrm",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "HRM API",
          "Version": "v1",
          "Url": "http://localhost:8001/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "admissions",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "Admissions API",
          "Version": "v1",
          "Url": "http://localhost:8004/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "admissions-portal",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "Admissions Portal API",
          "Version": "v1",
          "Url": "http://localhost:8005/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "leader",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "Leader API",
          "Version": "v1",
          "Url": "http://localhost:8006/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "teacher",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "Teacher API",
          "Version": "v1",
          "Url": "http://localhost:8007/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "eledger",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "e-Ledger API",
          "Version": "v1",
          "Url": "http://localhost:8011/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "survey",
      "TransformByOcelotConfig": false,
      "Config": [
        {
          "Name": "Survey API",
          "Version": "v1",
          "Url": "http://localhost:8013/swagger/v1/swagger.json"
        }
      ]
    }
  ],
  "GlobalConfiguration": {
    "BaseUrl": "https://api.mybusiness.com"
  }
}
