﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteEmailTemplateCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Xóa mẫu email theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Id mẫu email cần xóa</param>
        public DeleteEmailTemplateCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteEmailTemplateCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteEmailTemplateCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {EmailTemplateConstant.CachePrefix}: {id}");

                var dt = await _dataContext.EmailTemplates.FirstOrDefaultAsync(x => x.Id == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.EmailTemplates.Remove(dt);

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa mẫu email mã: {dt.Code}",
                    ObjectCode = EmailTemplateConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(EmailTemplateConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(EmailTemplateConstant.BuildCacheKey());

                Log.Information($"Delete {EmailTemplateConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
