﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svQuocTich")]
    public class SvQuocTich
    {
        public SvQuocTich()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_quoc_tich")]
        public int IdQuocTich { get; set; }

        [Column("Ma_quoc_tich"), MaxLength(10)]
        public string MaQuocTich{ get; set; }

        [Column("Quoc_tich"), MaxLength(50)]
        public string QuocTich { get; set; }

    }
}

