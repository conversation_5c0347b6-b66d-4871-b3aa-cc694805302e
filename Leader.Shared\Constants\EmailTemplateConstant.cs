﻿using Core.Shared.EmailTemplate;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Leader.Shared
{
    public enum LeaderEmailTemplateEnum
    {
        
    }

    public class LeaderEmailTemplateSeedConstant
    {
        public static List<EmailTemplateSeedModel> ListSeedEmails = new List<EmailTemplateSeedModel>
        {
            //new EmailTemplateSeedModel
            //{
            //    Code = LeaderEmailTemplateEnum.DHM_NHS.ToString(),
            //    Name = "Mẫu mail sau ...",
            //    Subject = "Thông báo đăng ký xét tuyển của trường ...",
            //    Template = "<p>Xin chào bạn..."
            //}
        };
    }
}
