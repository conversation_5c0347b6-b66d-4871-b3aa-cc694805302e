﻿using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetChungThuSoByIdQuery : IRequest<ChungThuSoModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// L<PERSON>y thông tin chứng thư số theo Id
        /// </summary>
        /// <param name="id">Id chứng thư số</param>
        public GetChungThuSoByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChungThuSoByIdQuery, ChungThuSoModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChungThuSoModel> Handle(GetChungThuSoByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = ChungThuSoConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SgChungThuSos.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<SgChungThuSo, ChungThuSoModel>(entity);
                });
                return item;
            }
        }
    }
}
