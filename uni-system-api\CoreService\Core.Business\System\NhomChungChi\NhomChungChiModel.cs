﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class NhomChungChiSelectItemModel
    {
        public int IdNhomChungChi { get; set; }
        public string KyHieuNhom { get; set; }
        public string NhomChungChi { get; set; }
    }

    public class NhomChungChiBaseModel
    {
        public int IdNhomChungChi { get; set; }
        public string KyHieuNhom { get; set; }
        public string NhomChungChi { get; set; }
    }


    public class NhomChungChiModel : NhomChungChiBaseModel
    {
      
    }

    public class NhomChungChiFilterModel : BaseQueryFilterModel
    {
        public NhomChungChiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNhomChungChi";
        }
    }

    public class CreateNhomChungChiModel
    {
        [MaxLength(20, ErrorMessage = "NhomChungChi.KyHieuNhom.MaxLength(20)")]
        [Required(ErrorMessage = "NhomChungChi.KyHieuNhom.NotRequire")]
        public string KyHieuNhom { get; set; }

        [MaxLength(200, ErrorMessage = "NhomChungChi.NhomChungChi.MaxLength(200)")]
        [Required(ErrorMessage = "NhomChungChi.NhomChungChi.NotRequire")]
        public string NhomChungChi { get; set; }

    }

    public class CreateManyNhomChungChiModel
    {
        public List<CreateNhomChungChiModel> listNhomChungChiModels { get; set; }
    }

    public class UpdateNhomChungChiModel : CreateNhomChungChiModel
    {
        public void UpdateEntity(SvNhomChungChi input)
        {
            input.KyHieuNhom = KyHieuNhom;
            input.NhomChungChi = NhomChungChi;
        }
    }
}
