﻿using Core.Data;
using Core.Shared;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetHocKyHienTaiQuery : IRequest<int>
    {
        /// <summary>
        /// L<PERSON>y học kỳ hiện tại
        /// </summary>
        public GetHocKyHienTaiQuery()
        {
        }

        public class Handler : IRequestHandler<GetHocKyHienTaiQuery, int>
        {
            public Handler()
            {
            }

            public async Task<int> Handle(GetHocKyHienTaiQuery request, CancellationToken cancellationToken)
            {
                int Hoc_ky;
                try
                {
                    var inputDate = DateTime.Now;
                    if (inputDate.Month >= 8 || inputDate.Month == 1)
                    {
                        if (inputDate.Month == 1 && inputDate.Day > 15)
                        {
                            Hoc_ky = 2;
                        }
                        else
                        {
                            Hoc_ky = 1;
                        }
                    }
                    else
                    {
                        Hoc_ky = 2;
                    };
                }
                catch (Exception)
                {
                    Hoc_ky = -1;
                }
                return Hoc_ky;
            }
        }
    }

    public class GetNamHocHienTaiQuery : IRequest<string>
    {
        /// <summary>
        /// Lấy năm học hiện tại
        /// </summary>
        public GetNamHocHienTaiQuery() 
        { 
        }

        public class Handler : IRequestHandler<GetNamHocHienTaiQuery, string>
        {
            public Handler() 
            { 
            }

            public async Task<string> Handle(GetNamHocHienTaiQuery request, CancellationToken cancellationToken)
            {
                string Nam_hoc;
                try
                {
                    var inputDate = DateTime.Now;
                    int CurYear = DateTime.Today.Year;
                    if (inputDate.Month >= 8)
                    {
                        Nam_hoc = CurYear + "-" + (CurYear + 1).ToString();
                    }
                    else
                    {
                        Nam_hoc = (CurYear - 1).ToString() + "-" + CurYear;
                    }
                }
                catch (Exception)
                {
                    Nam_hoc = "";
                }
                return Nam_hoc;
            }
        }
    }

    public class GetComboboxNamHocQuery : IRequest<List<NamHocSelectItemModel>>
    {
        /// <summary>
        /// Lấy khóa học cho combobox
        /// </summary>
        public GetComboboxNamHocQuery()
        {
        }

        public class Handler : IRequestHandler<GetComboboxNamHocQuery, List<NamHocSelectItemModel>>
        {

            public Handler()
            {
            }

            public async Task<List<NamHocSelectItemModel>> Handle(GetComboboxNamHocQuery request, CancellationToken cancellationToken)
            {
                int yearNow = DateTime.Now.Year;
                List<NamHocSelectItemModel> namHocArr = new List<NamHocSelectItemModel>();

                for (int i = 0; i < 10; i++)
                {
                    string namHocString = $"{yearNow}-{yearNow + 1}";
                    NamHocSelectItemModel obj = new NamHocSelectItemModel
                    {
                        Value = namHocString,
                        Name = namHocString
                    };
                    namHocArr.Add(obj);
                    yearNow--;
                }

                return namHocArr;
            }
        }
    }
}
