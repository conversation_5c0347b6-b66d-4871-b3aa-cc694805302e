﻿using Core.Business;
using System;
using System.Collections.Generic;

namespace Leader.Business
{
    public class ThongKeNhapHocBaseModel
    {
        public int IdNganh { get; set; }
        public string TenNganh { get; set; }
        public int? ChiTieuTuyenSinh { get; set; }
        public int? TrungTuyen { get; set; }
        public int? XacNhanNhapHoc { get; set; }
        public int? DangKy { get; set; }
        public int? DaThuPhieuDiem { get; set; }
        public int? DaTraGiayBao { get; set; }
        public int? DaThuHoSo { get; set; }
        public int? DaThuHocPhi { get; set; }
        public int? DaHoanThanh { get; set; }
        public int? DaRutHoSo { get; set; }
        public long? TongTien { get; set; }

    }
    public class ThongKeNhapHocModel : ThongKeNhapHocBaseModel
    {

    }
    public class ThongKeNhapHocQueryFilter : BaseQueryFilterModel
    {
        public int IDHe { get; set; }
        public int NamHoc { get; set; }
        public int IDNganh { get; set; }
    }
}
