﻿using Core.Business;
using Leader.Data;
using System;
using System.Collections.Generic;

namespace Leader.Business
{
    public class TuyenSinhPhuongThucXetTuyenBaseModel
    {
        public int IdPhuongThucXetTuyen { get; set; }
        public string MaPhuongThucXetTuyen { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
        public string GhiChu { get; set; }
    }

    public class TuyenSinhPhuongThucXetTuyenModel : TuyenSinhPhuongThucXetTuyenBaseModel
    {
    }

    public class TuyenSinhPhuongThucXetTuyenDetailModel : TuyenSinhPhuongThucXetTuyenModel
    {
    }

    public class CreateTuyenSinhPhuongThucXetTuyenModel : TuyenSinhPhuongThucXetTuyenDetailModel
    {
        public string CreateUserName { get; set; }
    }

    public class UpdateTuyenSinhPhuongThucXetTuyenModel : TuyenSinhPhuongThucXetTuyenDetailModel
    {
        public void UpdateEntity(TuyenSinhPhuongThucXetTuyen entity)
        {
            //entity.MaPhuongThucXetTuyen = this.MaPhuongThucXetTuyen;
            entity.TenPhuongThucXetTuyen = this.TenPhuongThucXetTuyen;
            entity.GhiChu = this.GhiChu;
        }
    }

    public class TuyenSinhPhuongThucXetTuyenQueryFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhPhuongThucXetTuyenQueryFilterModel()
        {
            PropertyName = "MaPhuongThucXetTuyen";
            Ascending = "asc";
        }
    }

    public class TuyenSinhPhuongThucXetTuyenSelectItemModel
    {
        public int IdPhuongThucXetTuyen { get; set; }
        public string MaPhuongThucXetTuyen { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
    }
}
