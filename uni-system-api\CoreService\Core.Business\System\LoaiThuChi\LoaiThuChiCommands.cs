﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiThuChiCommand : IRequest<Unit>
    {
        public CreateLoaiThuChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiThuChiCommand(CreateLoaiThuChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiThuChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiThuChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiThuChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiThuChiModel, SvLoaiThuChi>(model);

                var checkCode = await _dataContext.SvLoaiThuChis.AnyAsync(x =>  x.TenThuChi == entity.TenThuChi || x.MaThuChi == entity.MaThuChi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiThuChi.Existed", entity.TenThuChi.ToString()]}");
                }

                await _dataContext.SvLoaiThuChis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiThuChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại thu chi: {entity.TenThuChi}",
                    ObjectCode = LoaiThuChiConstant.CachePrefix,
                    ObjectId = entity.TenThuChi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiThuChiCommand : IRequest<Unit>
    {
        public CreateManyLoaiThuChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiThuChiCommand(CreateManyLoaiThuChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiThuChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiThuChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiThuChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listLoaiThuChiAdd = model.listLoaiThuChiModels.Select(x => x.TenThuChi).ToList();
                var listMaLoaiThuChiAdd = model.listLoaiThuChiModels.Select(x => x.TenThuChi).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiThuChiModel, SvLoaiThuChi>(model);

                // Check data duplicate
                if (listLoaiThuChiAdd.Count() != listLoaiThuChiAdd.Distinct().Count() || listMaLoaiThuChiAdd.Count() != listMaLoaiThuChiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvLoaiThuChis.AnyAsync(x => listLoaiThuChiAdd.Contains(x.TenThuChi)) || await _dataContext.SvLoaiThuChis.AnyAsync(x => listMaLoaiThuChiAdd.Contains(x.MaThuChi)))
                {
                    throw new ArgumentException($"{_localizer["LoaiThuChi.Existed"]}");
                }

                var listEntity = model.listLoaiThuChiModels.Select(x => new SvLoaiThuChi()
                {
                    IdThuChi = x.IdThuChi,
                    TenThuChi = x.TenThuChi,
                    ThuChi = x.ThuChi,
                    SoTien = x.SoTien,
                    HocLai = x.HocLai,
                    ThiLai = x.ThiLai,
                    KinhPhiDt = x.KinhPhiDt,
                    KhoanThuKtx = x.KhoanThuKtx,
                    KhoanThuTienPhong = x.KhoanThuTienPhong,
                    KhoanTienCuoc = x.KhoanTienCuoc,
                    MaThuChi = x.MaThuChi,
                    BaoHiem = x.BaoHiem,
                    HocPhi = x.HocPhi

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdThuChi).ToList();

                Log.Information($"Create many {LoaiThuChiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import loại thu chi từ file excel",
                    ObjectCode = LoaiThuChiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiThuChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiThuChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiThuChiCommand(int id, UpdateLoaiThuChiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiThuChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiThuChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiThuChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiThuChis.FirstOrDefaultAsync(dt => dt.IdThuChi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.SvLoaiThuChis.AnyAsync(x =>  x.MaThuChi == model.MaThuChi && x.IdThuChi != model.IdThuChi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiThuChi.Existed", model.TenThuChi.ToString()]}");
                }

                Log.Information($"Before Update {LoaiThuChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiThuChis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiThuChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiThuChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại thu chi: {entity.TenThuChi}",
                    ObjectCode = LoaiThuChiConstant.CachePrefix,
                    ObjectId = entity.IdThuChi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey(entity.IdThuChi.ToString()));
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiThuChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiThuChiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiThuChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiThuChiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiThuChiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiThuChis.FirstOrDefaultAsync(x => x.IdThuChi == id);

                _dataContext.SvLoaiThuChis.Remove(entity);

                Log.Information($"Delete {LoaiThuChiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại thu chi: {entity.TenThuChi}",
                    ObjectCode = LoaiThuChiConstant.CachePrefix,
                    ObjectId = entity.IdThuChi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey());
                _cacheService.Remove(LoaiThuChiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
