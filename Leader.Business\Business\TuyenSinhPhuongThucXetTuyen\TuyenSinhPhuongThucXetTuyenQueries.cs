﻿using Core.Business;
using Core.Data;
using Core.Shared;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class GetTuyenSinhPhuongThucXetTuyenByIdQuery : IRequest<TuyenSinhPhuongThucXetTuyenModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin phương thức xét tuyển theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetTuyenSinhPhuongThucXetTuyenByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhPhuongThucXetTuyenByIdQuery, TuyenSinhPhuongThucXetTuyenModel>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhPhuongThucXetTuyenModel> Handle(GetTuyenSinhPhuongThucXetTuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TuyenSinhPhuongThucXetTuyens.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == id);

                    return AutoMapperUtils.AutoMap<TuyenSinhPhuongThucXetTuyen, TuyenSinhPhuongThucXetTuyenModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetFilterTuyenSinhPhuongThucXetTuyenQuery : IRequest<PaginationList<TuyenSinhPhuongThucXetTuyenBaseModel>>
    {
        public TuyenSinhPhuongThucXetTuyenQueryFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách phương thức xét tuyển theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhPhuongThucXetTuyenQuery(TuyenSinhPhuongThucXetTuyenQueryFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhPhuongThucXetTuyenQuery, PaginationList<TuyenSinhPhuongThucXetTuyenBaseModel>>
        {
            private readonly LeaderReadDataContext _dataContext;

            public Handler(LeaderReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhPhuongThucXetTuyenBaseModel>> Handle(GetFilterTuyenSinhPhuongThucXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TuyenSinhPhuongThucXetTuyens
                            select new TuyenSinhPhuongThucXetTuyenBaseModel()
                            {
                                IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                MaPhuongThucXetTuyen = dt.MaPhuongThucXetTuyen,
                                TenPhuongThucXetTuyen = dt.TenPhuongThucXetTuyen,
                                GhiChu = dt.GhiChu
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.MaPhuongThucXetTuyen.ToLower().Contains(ts) || x.TenPhuongThucXetTuyen.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhPhuongThucXetTuyenBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetComboboxTuyenSinhPhuongThucXetTuyenQuery : IRequest<List<TuyenSinhPhuongThucXetTuyenSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách phương thức xét tuyển cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTuyenSinhPhuongThucXetTuyenQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTuyenSinhPhuongThucXetTuyenQuery, List<TuyenSinhPhuongThucXetTuyenSelectItemModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhPhuongThucXetTuyenSelectItemModel>> Handle(GetComboboxTuyenSinhPhuongThucXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TuyenSinhPhuongThucXetTuyens.OrderBy(x => x.MaPhuongThucXetTuyen)
                                select new TuyenSinhPhuongThucXetTuyenSelectItemModel()
                                {
                                    IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                    MaPhuongThucXetTuyen = dt.MaPhuongThucXetTuyen,
                                    TenPhuongThucXetTuyen = dt.TenPhuongThucXetTuyen
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.MaPhuongThucXetTuyen.ToLower().Contains(textSearch) || x.TenPhuongThucXetTuyen.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
