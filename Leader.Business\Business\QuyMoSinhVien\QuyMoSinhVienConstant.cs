﻿using Core.Shared;
using <PERSON>.Shared;

namespace Leader.Business
{
    public class QuyMoSinhVienConstant
    {
        public const string CachePrefix = LeaderCacheConstants.QUY_MO_SINH_VIEN;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
