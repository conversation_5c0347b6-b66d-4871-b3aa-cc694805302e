﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateGioiTinhCommand : IRequest<Unit>
    {
        public CreateGioiTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateGioiTinhCommand(CreateGioiTinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateGioiTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateGioiTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {GioiTinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateGioiTinhModel, SvGioiTinh>(model);

                var checkCode = await _dataContext.SvGioiTinhs.AnyAsync(x => x.IdGioiTinh == entity.IdGioiTinh || x.GioiTinh == entity.GioiTinh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["GioiTinh.Existed", entity.GioiTinh.ToString()]}");
                }

                await _dataContext.SvGioiTinhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {GioiTinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới giới tính: {entity.GioiTinh}",
                    ObjectCode = GioiTinhConstant.CachePrefix,
                    ObjectId = entity.IdGioiTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyGioiTinhCommand : IRequest<Unit>
    {
        public CreateManyGioiTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyGioiTinhCommand(CreateManyGioiTinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyGioiTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyGioiTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {GioiTinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listGioiTinhAdd = model.listGioiTinhModels.Select(x => x.GioiTinh).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyGioiTinhModel, SvGioiTinh>(model);

                // Check data duplicate
                if (listGioiTinhAdd.Count() != listGioiTinhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvGioiTinhs.AnyAsync(x => listGioiTinhAdd.Contains(x.GioiTinh)) )
                {
                    throw new ArgumentException($"{_localizer["GioiTinh.Existed"]}");
                }

                var listEntity = model.listGioiTinhModels.Select(x => new SvGioiTinh()
                {
                    IdGioiTinh = x.IdGioiTinh,
                    GioiTinh = x.GioiTinh,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdGioiTinh).ToList();

                Log.Information($"Create many {GioiTinhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import giới tính từ file excel",
                    ObjectCode = GioiTinhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateGioiTinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateGioiTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateGioiTinhCommand(int id, UpdateGioiTinhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateGioiTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateGioiTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {GioiTinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvGioiTinhs.FirstOrDefaultAsync(dt => dt.IdGioiTinh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvGioiTinhs.AnyAsync(x => x.GioiTinh == model.GioiTinh && x.IdGioiTinh != model.IdGioiTinh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["GioiTinh.Existed", model.GioiTinh.ToString()]}");
                }

                Log.Information($"Before Update {GioiTinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvGioiTinhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {GioiTinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {GioiTinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật giới tính: {entity.GioiTinh}",
                    ObjectCode = GioiTinhConstant.CachePrefix,
                    ObjectId = entity.IdGioiTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey(entity.IdGioiTinh.ToString()));
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteGioiTinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteGioiTinhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteGioiTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteGioiTinhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {GioiTinhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvGioiTinhs.FirstOrDefaultAsync(x => x.IdGioiTinh == id);

                _dataContext.SvGioiTinhs.Remove(entity);

                Log.Information($"Delete {GioiTinhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa giới tính: {entity.GioiTinh}",
                    ObjectCode = GioiTinhConstant.CachePrefix,
                    ObjectId = entity.IdGioiTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey());
                _cacheService.Remove(GioiTinhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
