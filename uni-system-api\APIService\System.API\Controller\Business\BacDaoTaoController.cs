﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/bac-dao-tao")]
    [ApiExplorerSettings(GroupName = "86. Bậc đào tạo")]
    [Authorize]
    public class BacDaoTaoController : ApiControllerBase
    {
        public BacDaoTaoController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách bậc đào tạo cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<BacDaoTaoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxBacDaoTaoQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách bậc đào tạo có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<BacDaoTaoBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_VIEW))]
        public async Task<IActionResult> Filter([FromBody] BacDaoTaoFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterBacDaoTaoQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết bậc đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<BacDaoTaoModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetBacDaoTaoByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới bậc đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateBacDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BAC_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BAC_DAO_TAO_CREATE;


                return await _mediator.Send(new CreateBacDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel bậc đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyBacDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BAC_DAO_TAO_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BAC_DAO_TAO_CREATE_MANY;


                return await _mediator.Send(new CreateManyBacDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa bậc đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateBacDaoTaoModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BAC_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BAC_DAO_TAO_UPDATE;
                return await _mediator.Send(new UpdateBacDaoTaoCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa bậc đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BAC_DAO_TAO_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BAC_DAO_TAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BAC_DAO_TAO_DELETE;

                return await _mediator.Send(new DeleteBacDaoTaoCommand(id, u.SystemLog));
            });
        }

    }
}
