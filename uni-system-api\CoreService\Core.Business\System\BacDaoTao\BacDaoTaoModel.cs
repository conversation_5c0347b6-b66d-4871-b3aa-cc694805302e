﻿using Core.Data;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class BacDaoTaoSelectItemModel
    {
        public int IdBacDaoTao { get; set; }
        public string MaBacDaoTao { get; set; }
        public string BacDaoTao { get; set; }

    }

    public class BacDaoTaoBaseModel : BacDaoTaoSelectItemModel
    {

    }


    public class BacDaoTaoModel : BacDaoTaoSelectItemModel
    {

    }

    public class BacDaoTaoFilterModel : BaseQueryFilterModel
    {
        public BacDaoTaoFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdBacDaoTao";
        }
    }

    public class CreateBacDaoTaoModel
    {
        [Required(ErrorMessage = "BacDaoTao.IdBacDaoTao.NotRequire")]
        public int IdBacDaoTao { get; set; }

        [MaxLength(10, ErrorMessage = "BacDaoTao.MaBacDaoTao.MaxLength(10)")]
        [Required(ErrorMessage = "BacDaoTao.MaBacDaoTao.NotRequire")]
        public string MaBacDaoTao { get; set; }

        [MaxLength(100, ErrorMessage = "BacDaoTao.BacDaoTao.MaxLength(100)")]
        [Required(ErrorMessage = "BacDaoTao.BacDaoTao.NotRequire")]
        public string BacDaoTao { get; set; }


    }

    public class CreateManyBacDaoTaoModel
    {
        public List<CreateBacDaoTaoModel> listBacDaoTaoModels { get; set; }
    }

    public class UpdateBacDaoTaoModel : CreateBacDaoTaoModel
    {
        public void UpdateEntity(TkbBacDaoTao input)
        {
            input.IdBacDaoTao = IdBacDaoTao;
            input.MaBacDaoTao = MaBacDaoTao;
            input.BacDaoTao = BacDaoTao;
        }
    }
}
