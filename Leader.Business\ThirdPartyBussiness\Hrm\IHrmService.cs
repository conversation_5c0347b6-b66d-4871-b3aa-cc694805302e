﻿using HouHRMClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Leader.Business.ThirdPartyBussiness.Hrm
{
    public interface IHrmService
    {
        /// <summary>
        /// L<PERSON>y danh sách nhân viên
        /// </summary>
        /// <returns></returns>
        Task<List<HrmEmployeeModel>> GetAllEmployeesAsync();

        /// <summary>
        /// L<PERSON>y thông tin nhân viên theo email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        Task<HrmEmployeeDetailModel> GetEmployeeByEmailAsync(string email);

        /// <summary>
        /// L<PERSON><PERSON> tất cả hồ sơ cán bộ
        /// </summary>
        /// <param name="houRequest"></param>
        /// <returns></returns>
        Task<List<HrmHoSoCanBoModel>> GetAllHoSoCanBoAsync(DateTime timestamp);
    }
}
