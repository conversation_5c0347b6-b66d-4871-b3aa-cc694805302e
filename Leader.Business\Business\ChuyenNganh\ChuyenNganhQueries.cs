﻿using Core.Business;
using Core.Data;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class GetComboboxChuyenNganhQuery : IRequest<List<ChuyenNganhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy chuyên ngành cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxChuyenNganhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxChuyenNganhQuery, List<ChuyenNganhSelectItemModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChuyenNganhSelectItemModel>> Handle(GetComboboxChuyenNganhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChuyenNganhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvChuyenNganhs.OrderBy(x => x.ChuyenNganh)
                                select new ChuyenNganhSelectItemModel()
                                {
                                    IdChuyenNganh = dt.IdChuyenNganh,
                                    MaChuyenNganh = dt.MaChuyenNganh,
                                    ChuyenNganh = dt.ChuyenNganh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.ChuyenNganh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
