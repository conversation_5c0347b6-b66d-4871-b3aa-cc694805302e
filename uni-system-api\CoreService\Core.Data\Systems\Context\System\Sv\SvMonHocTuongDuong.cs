﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svMonHocTuongDuong")]
    public class SvMonHocTuongDuong
    {

        public SvMonHocTuongDuong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_mon_tuong_duong")]
        public int IdMonTuongDuong { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("So_tin_chi")]
        public float? SoTinChi { get; set; }

        [Column("ID_mon1")]
        public int IdMon1 { get; set; }

        [Column("So_tin_chi1")]
        public float? SoTinChi1 { get; set; }

    }
}
