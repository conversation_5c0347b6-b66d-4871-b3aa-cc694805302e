﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svMucHuongBhyt")]
    public class SvMucHuongBhyt
    {

        public SvMucHuongBhyt()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_muc_huong_bhyt")]
        public int IdMucHuongBhyt { get; set; }

        [Column("Ky_hieu"), MaxLength(10)]
        public string <PERSON>y<PERSON><PERSON> { get; set; }

        [Column("Doi_tuong_muc_huong")]
        public string DoiTuongMucHuong { get; set; }

        [Column("Muc_huong")]
        public int MucHuong { get; set; }
    }
}
