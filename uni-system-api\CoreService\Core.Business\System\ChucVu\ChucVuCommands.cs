﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateChucVuCommand : IRequest<Unit>
    {
        public CreateChucVuModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateChucVuCommand(CreateChucVuModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateChucVuCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateChucVuCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ChucVuConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChucVuModel, TkbChucVu>(model);

                var checkCode = await _dataContext.TkbChucVus.AnyAsync(x => x.IdChucVu == entity.IdChucVu || x.ChucVu == entity.ChucVu || x.MaChucVu == entity.MaChucVu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChucVu.Existed", entity.ChucVu.ToString()]}");
                }

                await _dataContext.TkbChucVus.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChucVuConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới chức vụ: {entity.ChucVu}",
                    ObjectCode = ChucVuConstant.CachePrefix,
                    ObjectId = entity.IdChucVu.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucVuConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyChucVuCommand : IRequest<Unit>
    {
        public CreateManyChucVuModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyChucVuCommand(CreateManyChucVuModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyChucVuCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyChucVuCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {ChucVuConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listChucVuAdd = model.listChucVuModels.Select(x => x.ChucVu).ToList();
                var listMaChucVuAdd = model.listChucVuModels.Select(x => x.MaChucVu).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyChucVuModel, TkbChucVu>(model);

                // Check data duplicate
                if (listChucVuAdd.Count() != listChucVuAdd.Distinct().Count() || listMaChucVuAdd.Count() != listMaChucVuAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbChucVus.AnyAsync(x => listChucVuAdd.Contains(x.ChucVu)) || await _dataContext.TkbChucVus.AnyAsync(x => listMaChucVuAdd.Contains(x.MaChucVu)))
                {
                    throw new ArgumentException($"{_localizer["ChucVu.Existed"]}");
                }

                var listEntity = model.listChucVuModels.Select(x => new TkbChucVu()
                {
                    IdChucVu = x.IdChucVu,
                    MaChucVu = x.MaChucVu,
                    ChucVu = x.ChucVu

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdChucVu).ToList();

                Log.Information($"Create many {ChucVuConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import chức vụ từ file excel",
                    ObjectCode = ChucVuConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(ChucVuConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateChucVuCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateChucVuModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateChucVuCommand(int id, UpdateChucVuModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateChucVuCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateChucVuCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ChucVuConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbChucVus.FirstOrDefaultAsync(dt => dt.IdChucVu == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.TkbChucVus.AnyAsync(x => (x.ChucVu == model.ChucVu || x.MaChucVu == model.MaChucVu) && x.IdChucVu != model.IdChucVu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChucVu.Existed", model.ChucVu.ToString()]}");
                }

                Log.Information($"Before Update {ChucVuConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbChucVus.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChucVuConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ChucVuConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chức vụ: {entity.ChucVu}",
                    ObjectCode = ChucVuConstant.CachePrefix,
                    ObjectId = entity.IdChucVu.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucVuConstant.BuildCacheKey(entity.IdChucVu.ToString()));
                _cacheService.Remove(ChucVuConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChucVuCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteChucVuCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteChucVuCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteChucVuCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ChucVuConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbChucVus.FirstOrDefaultAsync(x => x.IdChucVu == id);

                _dataContext.TkbChucVus.Remove(entity);

                Log.Information($"Delete {ChucVuConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa chức vụ: {entity.ChucVu}",
                    ObjectCode = ChucVuConstant.CachePrefix,
                    ObjectId = entity.IdChucVu.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucVuConstant.BuildCacheKey());
                _cacheService.Remove(ChucVuConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
