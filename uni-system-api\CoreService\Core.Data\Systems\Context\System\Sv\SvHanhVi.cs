﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svHanhVi")]
    public class SvHanhVi
    {

        public SvHanhVi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_hanh_vi")]
        public int IdHanhVi { get; set; }

        [Column("Ma_hanh_vi"), MaxLength(5)]
        public string MaHanhVi { get; set; }

        [Column("Hanh_vi"), MaxLength(100)]
        public string HanhVi { get; set; }


    }
}
