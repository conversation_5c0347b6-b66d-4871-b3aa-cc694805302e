﻿using Core.Shared;
using System.Text.Json.Serialization;

namespace Core.Business.Core
{
    #region System
    [EventName("notification-message")]
    public class NotificationModel
    {
        public int UserId { get; set; }
        public object Data { get; set; }
    }

    [EventName("notification-broadcast-message")]
    public class NotificationBroadcastModel
    {
        public object Data { get; set; }
    }
    #endregion

    #region Teacher
    [EventName("teacher-notification-message")]
    public class TeacherNotificationModel
    {
        public int UserId { get; set; }
        public object Data { get; set; }
    }
    #endregion
}
