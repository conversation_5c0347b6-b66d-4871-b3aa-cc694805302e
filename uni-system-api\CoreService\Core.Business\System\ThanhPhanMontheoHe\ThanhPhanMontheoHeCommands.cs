﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateThanhPhanMonTheoHeCommand : IRequest<Unit>
    {
        public CreateThanhPhanMonTheoHeModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateThanhPhanMonTheoHeCommand(CreateThanhPhanMonTheoHeModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateThanhPhanMonTheoHeCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateThanhPhanMonTheoHeCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ThanhPhanMonTheoHeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateThanhPhanMonTheoHeModel, SvThanhPhanMonTheoHe>(model);

                var checkCode = await _dataContext.SvThanhPhanMonTheoHes.AnyAsync(x => x.IdThanhPhan == entity.IdThanhPhan && x.IdHe == entity.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ThanhPhanMonTheoHe.Existed", entity.IdThanhPhan.ToString(), entity.IdHe.ToString()]}");
                }

                await _dataContext.SvThanhPhanMonTheoHes.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ThanhPhanMonTheoHeConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới thành phần môn theo hệ: {entity.IdThanhPhan} và {entity.IdHe}",
                    ObjectCode = ThanhPhanMonTheoHeConstant.CachePrefix,
                    ObjectId = entity.IdThanhPhan.ToString() + "_" + entity.IdHe.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ThanhPhanMonTheoHeConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateThanhPhanMonTheoHeCommand : IRequest<Unit>
    {
        public int IdThanhPhan { get; set; }
        public int IdHe { get; set; }
        public UpdateThanhPhanMonTheoHeModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateThanhPhanMonTheoHeCommand(int idThanhPhan, int idHe, UpdateThanhPhanMonTheoHeModel model, SystemLogModel systemLog)
        {
            IdThanhPhan = idThanhPhan;
            IdHe = idHe;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateThanhPhanMonTheoHeCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateThanhPhanMonTheoHeCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int idThanhPhan = request.IdThanhPhan;
                int idHe = request.IdHe;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ThanhPhanMonTheoHeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvThanhPhanMonTheoHes.FirstOrDefaultAsync(dt => dt.IdThanhPhan == idThanhPhan && dt.IdHe == idHe);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvThanhPhanMonTheoHes.AnyAsync(x => x.IdThanhPhan != model.IdThanhPhan && x.IdHe == model.IdHe && x.Stt == model.Stt  && x.TyLe == model.TyLe && x.NhomThanhPhan == model.NhomThanhPhan );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ThanhPhanMonTheoHe.Existed", model.IdThanhPhan.ToString() + "và" + model.IdHe.ToString()]}");
                }

                Log.Information($"Before Update {ThanhPhanMonTheoHeConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvThanhPhanMonTheoHes.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ThanhPhanMonTheoHeConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ThanhPhanMonTheoHeConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật thành phần môn theo hệ: {entity.IdThanhPhan} và {entity.IdHe}",
                    ObjectCode = ThanhPhanMonTheoHeConstant.CachePrefix,
                    ObjectId = entity.IdThanhPhan.ToString()+ "_" + entity.IdHe.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ThanhPhanMonTheoHeConstant.BuildCacheKey(idThanhPhan.ToString() + "_" + idHe.ToString()));
                _cacheService.Remove(ThanhPhanMonTheoHeConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteThanhPhanMonTheoHeCommand : IRequest<Unit>
    {
        public int IdThanhPhan { get; set; }
        public int IdHe { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteThanhPhanMonTheoHeCommand(int idThanhPhan, int idHe, SystemLogModel systemLog)
        {
            IdThanhPhan = idThanhPhan;
            IdHe = idHe;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteThanhPhanMonTheoHeCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteThanhPhanMonTheoHeCommand request, CancellationToken cancellationToken)
            {
                var idThanhPhan = request.IdThanhPhan;
                var idHe = request.IdHe;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ThanhPhanMonTheoHeConstant.CachePrefix}: " + JsonSerializer.Serialize(idThanhPhan)+ JsonSerializer.Serialize(idHe));

                var entity = await _dataContext.SvThanhPhanMonTheoHes.FirstOrDefaultAsync(x => x.IdThanhPhan == idThanhPhan && x.IdHe == idHe);

                _dataContext.SvThanhPhanMonTheoHes.Remove(entity);

                Log.Information($"Delete {ThanhPhanMonTheoHeConstant.CachePrefix} success: {JsonSerializer.Serialize(idThanhPhan) + JsonSerializer.Serialize(idHe)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa thành phần môn theo hệ: {entity.IdThanhPhan} và {entity.IdThanhPhan}",
                    ObjectCode = ThanhPhanMonTheoHeConstant.CachePrefix,
                    ObjectId = entity.IdThanhPhan.ToString() + "_" + entity.IdHe.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ThanhPhanMonTheoHeConstant.BuildCacheKey());
                _cacheService.Remove(ThanhPhanMonTheoHeConstant.BuildCacheKey(idThanhPhan.ToString() + "_" + idHe.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
