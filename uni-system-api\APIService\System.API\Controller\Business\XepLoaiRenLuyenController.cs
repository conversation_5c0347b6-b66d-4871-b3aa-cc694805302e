﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-loai-ren-luyen")]
    [ApiExplorerSettings(GroupName = "38. Xếp loại rèn luyện")]
    [Authorize]
    public class XepLoaiRenLuyenController : ApiControllerBase
    {
        public XepLoaiRenLuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách xếp loại rèn luyện cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiRenLuyenSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiRenLuyenQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách xếp loại rèn luyện có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepLoaiRenLuyenBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepLoaiRenLuyenFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepLoaiRenLuyenQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết xếp loại rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepLoaiRenLuyenModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepLoaiRenLuyenByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới xếp loại rèn luyện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepLoaiRenLuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_CREATE;


                return await _mediator.Send(new CreateXepLoaiRenLuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel xếp loại rèn luyện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepLoaiRenLuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepLoaiRenLuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa xếp loại rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepLoaiRenLuyenModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_UPDATE;
                return await _mediator.Send(new UpdateXepLoaiRenLuyenCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa xếp loại rèn luyện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_REN_LUYEN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_REN_LUYEN_DELETE;

                return await _mediator.Send(new DeleteXepLoaiRenLuyenCommand(id, u.SystemLog));
            });
        }

    }
}
