﻿using Core.Shared;
using <PERSON>.Shared;

namespace Leader.Business
{
    public class KhBacDaoTaoConstant
    {
        public const string CachePrefix = LeaderCacheConstants.KH_BAC_DAO_TAO;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
