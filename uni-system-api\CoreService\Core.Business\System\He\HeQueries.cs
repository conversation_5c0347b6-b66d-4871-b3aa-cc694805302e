﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using Core.Business;

namespace Core.Business
{
    public class GetComboboxHeQuery : IRequest<List<HeSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y hệ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHeQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHeQuery, List<HeSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HeSelectItemModel>> Handle(GetComboboxHeQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HeConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvHes.OrderBy(x => x.TenHe)
                                select new HeSelectItemModel()
                                {
                                    IdHe = dt.IdHe, 
                                    MaHe = dt.MaHe, 
                                    TenHe = dt.TenHe,
                                    QuyChe = dt.QuyChe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenHe.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHeQuery : IRequest<PaginationList<HeBaseModel>>
    {
        public HeFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách hệ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHeQuery(HeFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHeQuery, PaginationList<HeBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HeBaseModel>> Handle(GetFilterHeQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvHes
                            select new HeBaseModel
                            {
                                IdHe = dt.IdHe,
                                MaHe = dt.MaHe,
                                TenHe = dt.TenHe,
                                TenHeEn = dt.TenHeEn,
                                QuyChe = dt.QuyChe,
                                TenBacDaoTao = dt.TenBacDaoTao,
                                TenBacDaoTaoEn = dt.TenBacDaoTaoEn,
                                HinhThucDaoTao = dt.HinhThucDaoTao,
                                HinhThucDaoTaoEn = dt.HinhThucDaoTaoEn

                            });
                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenHe.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HeBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHeByIdQuery : IRequest<HeModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Hệ theo id
        /// </summary>
        /// <param name="id">Id hệ</param>
        public GetHeByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHeByIdQuery, HeModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HeModel> Handle(GetHeByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HeConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvHes.FirstOrDefaultAsync(x => x.IdHe == id);

                    return AutoMapperUtils.AutoMap<SvHe, HeModel>(entity);
                });
                return item;
            }
        }
    }  
}
