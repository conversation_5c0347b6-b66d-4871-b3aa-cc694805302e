﻿using Core.API.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Leader.Business;

namespace Leader.API.Controller
{
    [ApiController]
    [Route("leader/v1/he")]
    [ApiExplorerSettings(GroupName = "03. Hệ")]
    [Authorize]
    public class HeController : ApiControllerBase
    {
        public HeController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {

        }

        /// <summary>
        /// L<PERSON>y danh sách hệ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HeSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHeQuery(count, ts));
            });
        }
    }
}
