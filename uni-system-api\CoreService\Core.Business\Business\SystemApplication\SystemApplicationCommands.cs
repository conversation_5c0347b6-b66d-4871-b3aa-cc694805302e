﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Core.Data;
using Core.Shared;
using Serilog;
using SharpCompress.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Resources;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Minio.DataModel;
using Core.Business.Core;
using Microsoft.Extensions.Configuration;

namespace Core.Business
{
    public class CreateSystemApplicationCommand : IRequest<Unit>
    {
        public CreateSystemApplicationModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới ứng dụng
        /// </summary>
        /// <param name="model">Thông tin ứng dụng cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateSystemApplicationCommand(CreateSystemApplicationModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateSystemApplicationCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IConfiguration config)
            {
                _config = config;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateSystemApplicationCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {SystemApplicationConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateSystemApplicationModel, SystemApplication>(model);

                var checkCode = await _dataContext.SystemApplication.AnyAsync(x => x.Code == entity.Code);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["system-application.code.existed"]}");
                }

                entity.CreatedDate = DateTime.Now;
                //entity.Id = int.NewGuid();

                await _dataContext.SystemApplication.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {SystemApplicationConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới ứng dụng mã: {entity.Code}",
                    ObjectCode = SystemApplicationConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(SystemApplicationConstant.BuildCacheKey());

                #region Insert data to elastic
                //var elasticItem = AutoMapperUtils.AutoMap<SystemApplication, SystemApplicationElasticModel>(entity);
                //var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                //await ElasticClientUtils.GetElasticClient(elasticConfig).CreateAsync(elasticItem, c => c
                //    .Index(SystemApplicationConstant.CachePrefix) // index
                //    .Id(elasticItem.Id) // document id
                //);
                #endregion

                return Unit.Value;
            }
        }
    }

    public class CreateManySystemApplicationCommand : IRequest<Unit>
    {
        public List<CreateSystemApplicationModel> List { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới nhiều ứng dụng
        /// </summary>
        /// <param name="list">Danh sách ứng dụng</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateManySystemApplicationCommand(List<CreateSystemApplicationModel> list, SystemLogModel systemLog, IConfiguration config)
        {
            List = list;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateManySystemApplicationCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IConfiguration config)
            {
                _config = config;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateManySystemApplicationCommand request, CancellationToken cancellationToken)
            {
                var list = request.List;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {SystemApplicationConstant.CachePrefix}: " + JsonSerializer.Serialize(list));

                //Check Code exist
                var listExistCode = list.Select(x => x.Code).ToList();

                var checkCode = await _dataContext.SystemApplication.AnyAsync(x => listExistCode.Contains(x.Code));
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["system-application.code.existed"]}");
                }

                var listResult = new List<SystemApplicationElasticModel>();

                foreach (var item in list)
                {
                    var entity = AutoMapperUtils.AutoMap<CreateSystemApplicationModel, SystemApplication>(item);

                    entity.CreatedDate = DateTime.Now;

                    await _dataContext.SystemApplication.AddAsync(entity);

                    var elasticItem = AutoMapperUtils.AutoMap<SystemApplication, SystemApplicationElasticModel>(entity);
                    listResult.Add(elasticItem);

                    Log.Information($"Create {SystemApplicationConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới ứng dụng mã: {entity.Code}",
                        ObjectCode = SystemApplicationConstant.CachePrefix,
                        ObjectId = entity.Id.ToString()
                    });
                }

                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(SystemApplicationConstant.BuildCacheKey());

                #region Insert data to elastic
                //var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                //await ElasticClientUtils.GetElasticClient(elasticConfig).BulkAsync(b => b
                //    .Index(SystemApplicationConstant.CachePrefix)
                //    .IndexMany(listResult, (d, doc) => d.Document(doc).Id(doc.Id))
                //);
                #endregion

                return Unit.Value;
            }
        }
    }

    public class UpdateSystemApplicationCommand : IRequest<Unit>
    {
        public UpdateSystemApplicationModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật ứng dụng
        /// </summary>
        /// <param name="model">Thông tin ứng dụng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateSystemApplicationCommand(UpdateSystemApplicationModel model, SystemLogModel systemLog, IConfiguration config)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateSystemApplicationCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IConfiguration config)
            {
                _config = config;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateSystemApplicationCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Update {SystemApplicationConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SystemApplication.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {SystemApplicationConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SystemApplication.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {SystemApplicationConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(SystemApplicationConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(SystemApplicationConstant.BuildCacheKey());

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập ứng dụng mã: {entity.Code}",
                    ObjectCode = SystemApplicationConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                #region Update data to elastic
                //var elasticItem = AutoMapperUtils.AutoMap<SystemApplication, SystemApplicationElasticModel>(entity);
                //var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                //await ElasticClientUtils.GetElasticClient(elasticConfig).UpdateAsync<SystemApplicationElasticModel>(elasticItem.Id, u => u
                //      .Index(SystemApplicationConstant.CachePrefix)
                //      .Doc(elasticItem));
                #endregion

                return Unit.Value;
            }
        }
    }

    public class DeleteSystemApplicationCommand : IRequest<Unit>
    {
        public List<int> ListId { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa ứng dụng theo danh sách truyền vào
        /// </summary>
        /// <param name="listId">Danh sách id cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteSystemApplicationCommand(List<int> listId, SystemLogModel systemLog)
        {
            ListId = listId;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteSystemApplicationCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IConfiguration config)
            {
                _config = config;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteSystemApplicationCommand request, CancellationToken cancellationToken)
            {
                var listIds = request.ListId;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {SystemApplicationConstant.CachePrefix}: {JsonSerializer.Serialize(listIds)}");


                var listDt = await _dataContext.SystemApplication.Where(x => listIds.Contains(x.Id)).ToListAsync();

                if (listDt.Count != listIds.Count)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.SystemApplication.RemoveRange(listDt);

                var codes = string.Join(",", listDt.Select(x => x.Code));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa ứng dụng mã: {codes}",
                    ObjectCode = SystemApplicationConstant.CachePrefix
                });

                foreach (var item in listDt)
                {
                    //Xóa cache
                    _cacheService.Remove(SystemApplicationConstant.BuildCacheKey(item.Id.ToString()));
                }
                await _dataContext.SaveChangesAsync();

                _cacheService.Remove(SystemApplicationConstant.BuildCacheKey());

                #region Delete data from elastic
                //var elasticConfig = _config.GetSection("ElasticConfiguration").Get<ElasticConfig>();
                //foreach (var item in listIds)
                //{
                //    await ElasticClientUtils.GetElasticClient(elasticConfig).DeleteAsync<SystemApplicationElasticModel>(item, u => u
                //     .Index(SystemApplicationConstant.CachePrefix));
                //}
                #endregion

                return Unit.Value;
            }
        }
    }
}