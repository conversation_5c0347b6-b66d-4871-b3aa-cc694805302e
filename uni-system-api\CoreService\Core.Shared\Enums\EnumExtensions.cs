﻿using System;
using System.ComponentModel;
using System.Reflection;

namespace Core.Shared.Enums
{
    public static class EnumExtensions
    {
        public static string GetDisplayName(this Enum enumValue)
        {
            return enumValue.GetType()
                .GetField(enumValue.ToString())?
                .GetCustomAttribute<DescriptionAttribute>()?
                .Description ?? enumValue.ToString();
        }
    }
}
