﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svMonHoc")]
    public class SvMonHoc
    {

        public SvMonHoc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_mon")]
        public int IdMonHoc { get; set; }

        [Column("Ky_hieu"), MaxLength(255)]
        public string KyHieu { get; set; }

        [Column("Ten_mon"), MaxLength(500)]
        public string TenMon { get; set; }

        [Column("Ten_tieng_anh"), MaxLength(200)]
        public string TenTiengAnh { get; set; }

        [Column("Ten_viet_tat"), MaxLength(100)]
        public string TenVietTat { get; set; }

        [Column("ID_bm")]
        public int IdBoMon { get; set; }

        [Column("ID_he_dt")]
        public int IdHeDt { get; set; }

        [Column("ID_nhom_hp")]
        public int IdNhomHp { get; set; }

        [Column("HP_thuc_hanh")]
        public bool HocPhanTH { get; set; }

        [Column("Chat_luong_cao")]
        public bool ChatLuongCao { get; set; }

        [Column("Mon_chung_chi")]
        public bool MonChungChi { get; set; }

        [Column("Mon_NN")]
        public bool MonNn { get; set; }

        [Column("Ky_hieu_cu"), MaxLength(50)]
        public string KyHieuCu { get; set; }

        [Column("Ten_tieng_phap"), MaxLength(500)]
        public string TenTiengPhap { get; set; }


    }
}
