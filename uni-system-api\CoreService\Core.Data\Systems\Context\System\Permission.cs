﻿using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("sys_permission")]
    public class Permission : BaseTableDefault
    {
        public Permission()
        {
        }

        [Column("group_name")]
        public string GroupName { get; set; }

        [Column("code")]
        public string Code { get; set; }

        [Column("name")]
        public string Name { get; set; }

        [Column("id_phan_he")]
        public int IdPhanHe { get; set; }
    }
}
