﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/visnam-tai-khoan-ket-noi")]
    [ApiExplorerSettings(GroupName = "11. VisnamTaiKhoanKetNoi (Tài khoản kết nối Visnam)")]
    [Authorize]
    public class VisnamTaiKhoanKetNoiController : ApiControllerBaseV2
    {
        public VisnamTaiKhoanKetNoiController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới tài khoản kết nối Visnam
        /// </summary>
        /// <param name="model">Thông tin tài khoản kết nối Visnam</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VISNAM_TAI_KHOAN_KET_NOI_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateVisnamTaiKhoanKetNoiModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_CREATE;

                return await _mediator.Send(new CreateVisnamTaiKhoanKetNoiCommand(model));
            });
        }

        /// <summary>
        /// Cập nhật tài khoản kết nối Visnam
        /// </summary>
        /// <param name="model">Thông tin tài khoản kết nối Visnam</param>
        /// <returns></returns>
        [HttpPut, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VISNAM_TAI_KHOAN_KET_NOI_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromBody] UpdateVisnamTaiKhoanKetNoiModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_UPDATE;

                return await _mediator.Send(new UpdateVisnamTaiKhoanKetNoiCommand(model));
            });
        }

        /// <summary>
        /// Xóa tài khoản kết nối Visnam
        /// </summary>
        /// <param name="id">Id tài khoản kết nối Visnam</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VISNAM_TAI_KHOAN_KET_NOI_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_DELETE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_VISNAM_TAI_KHOAN_KET_NOI_DELETE;

                return await _mediator.Send(new DeleteVisnamTaiKhoanKetNoiCommand(id));
            });
        }

        /// <summary>
        /// Lấy thông tin tài khoản kết nối Visnam theo Id
        /// </summary>
        /// <param name="id">Id tài khoản kết nối Visnam</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VISNAM_TAI_KHOAN_KET_NOI_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<VisnamTaiKhoanKetNoiModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetVisnamTaiKhoanKetNoiByIdQuery(id));
            });
        }
        #endregion

        #region List
        /// <summary>
        /// Lấy danh sách tài khoản kết nối Visnam theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VISNAM_TAI_KHOAN_KET_NOI_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<VisnamTaiKhoanKetNoiBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] VisnamTaiKhoanKetNoiQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterVisnamTaiKhoanKetNoiQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách tài khoản kết nối Visnam cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<VisnamTaiKhoanKetNoiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxVisnamTaiKhoanKetNoiQuery(count, ts));

                return result;
            });
        }
        #endregion
    }
}
