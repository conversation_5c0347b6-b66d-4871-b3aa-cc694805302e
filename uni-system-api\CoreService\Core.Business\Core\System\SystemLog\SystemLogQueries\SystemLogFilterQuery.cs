﻿using MediatR;
using MongoDB.Driver;
using Core.DataLog;
using Core.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using Microsoft.EntityFrameworkCore;

namespace Core.Business
{
    /// <summary>
    /// Lấy danh sách nhật ký hệ thống theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách nhật ký hệ thống</returns>
    public class SystemLogFilterQuery : IRequest<PaginationList<SystemLog>>
    {
        public SystemLogQueryFilter Filter { get; set; }

        public SystemLogFilterQuery(SystemLogQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<SystemLogFilterQuery, PaginationList<SystemLog>>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMediator _mediator;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, IMediator mediator, SystemReadDataContext dataContext)
            {
                _mediator = mediator;
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<PaginationList<SystemLog>> Handle(SystemLogFilterQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                // set time StartDate = 0h and time EndDate = 24h
                if (filter.StartDate.HasValue && filter.EndDate.HasValue)
                {
                    filter.StartDate = filter.StartDate.Value.Date;
                    filter.EndDate = filter.EndDate.Value.AddDays(1).Date;
                }

                if (string.IsNullOrEmpty(filter.TextSearch))
                {
                    filter.TextSearch = "";
                }

                List<SystemLog> listResult = new List<SystemLog>();
                long dataCount = 0;
                long totalCount = 0;

                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<SystemLog>.Filter.And(
                        Builders<SystemLog>.Filter.Where(p => p.ActionCode.ToLower().Contains(filter.TextSearch.ToLower())
                            || p.ActionName.ToLower().Contains(filter.TextSearch.ToLower())
                            || string.IsNullOrEmpty(filter.TextSearch)
                        ),
                        Builders<SystemLog>.Filter.Where(p => p.TraceId.Equals(filter.TradeId) || string.IsNullOrEmpty(filter.TradeId)),
                        Builders<SystemLog>.Filter.Where(p => p.ActionCode.Equals(filter.ActionCode) || string.IsNullOrEmpty(filter.ActionCode)),
                        Builders<SystemLog>.Filter.Where(p => (filter.StartDate.HasValue && filter.EndDate.HasValue && p.CreatedDate >= filter.StartDate && p.CreatedDate < filter.EndDate)
                            || (!filter.StartDate.HasValue && !filter.EndDate.HasValue))
                    );

                    IFindFluent<SystemLog, SystemLog> data = _logs.Find(builder).Sort(Builders<SystemLog>.Sort.Descending(x => x.CreatedDate));

                    totalCount = await data.CountDocumentsAsync();

                    // Pagination
                    if (filter.PageSize.HasValue && filter.PageNumber.HasValue)
                    {
                        if (filter.PageSize <= 0)
                        {
                            filter.PageSize = QueryFilter.DefaultPageSize;
                        }

                        //Calculate nunber of rows to skip on pagesize
                        int excludedRows = (filter.PageNumber.Value - 1) * (filter.PageSize.Value);
                        if (excludedRows <= 0)
                        {
                            excludedRows = 0;
                        }

                        // Query
                        data = data.Skip(excludedRows).Limit(filter.PageSize.Value);
                    }
                    dataCount = await data.CountDocumentsAsync();

                    listResult = await data.ToListAsync();
                }

                // Nếu không có cấu hình MongoDB thì lấy từ SQL
                else
                {
                    var data = (from dt in _dataContext.SystemLogs.AsNoTracking()
                                select dt);

                    if (!string.IsNullOrEmpty(filter.TextSearch))
                    {
                        string ts = filter.TextSearch.Trim().ToLower();
                        data = data.Where(x => x.ActionCode.ToLower().Contains(ts) || x.ActionName.ToLower().Contains(ts));
                    }

                    data = data.OrderByDescending(x => x.Id);

                    // Apply pagination with improved performance
                    if (filter.PageSize <= 0)
                    {
                        filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    // Get total count asynchronously
                    totalCount = await data.CountAsync();

                    //Calculate nunber of rows to skip on pagesize
                    int excludedRows = (filter.PageNumber.Value - 1) * (filter.PageSize.Value);
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Query
                    data = data.Skip(excludedRows).Take(filter.PageSize.Value);
                    dataCount = data.Count();

                    var listData = await data.ToListAsync();

                    listResult = AutoMapperUtils.AutoMap<SystemLogEntity, SystemLog>(listData);
                }

                var listUser = await _mediator.Send(new GetComboboxUserQuery());
                foreach (var item in listResult)
                {
                    if (string.IsNullOrEmpty(item.UserName))
                    {
                        item.UserName = listUser.FirstOrDefault(x => x.UserId.ToString() == item.UserId)?.UserName;
                    }
                }

                return new PaginationList<SystemLog>()
                {
                    DataCount = (int)dataCount,
                    TotalCount = (int)totalCount,
                    PageNumber = filter.PageNumber ?? 0,
                    PageSize = filter.PageSize ?? 0,
                    Data = listResult
                };

            }
        }
    }

}
