﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hanh-vi-ky-luat")]
    [ApiExplorerSettings(GroupName = "31. Hành vi kỷ luật")]
    [Authorize]
    public class HanhViKyLuatController : ApiControllerBase
    {
        public HanhViKyLuatController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách hành vi kỷ luật cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HanhViSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHanhViQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách hành vi kỷ luật có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HanhViBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HanhViFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHanhViQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết hành vi kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HanhViModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHanhViByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới hành vi kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHanhViModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HANH_VI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HANH_VI_CREATE;


                return await _mediator.Send(new CreateHanhViCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel hành vi kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHanhViModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HANH_VI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HANH_VI_CREATE_MANY;


                return await _mediator.Send(new CreateManyHanhViCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa hành vi kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHanhViModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HANH_VI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HANH_VI_UPDATE;
                return await _mediator.Send(new UpdateHanhViCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa hành vi kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HANH_VI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HANH_VI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HANH_VI_DELETE;

                return await _mediator.Send(new DeleteHanhViCommand(id, u.SystemLog));
            });
        }

    }
}
