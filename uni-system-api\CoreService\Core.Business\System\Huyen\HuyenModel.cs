﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HuyenSelectItemModel
    {
        public string IdHuyen { get; set; }
        public string IdTinh { get; set; }
        public string TenHuyen { get; set; }


    }

    public class HuyenBaseModel 
    {
        public string IdHuyen { get; set; }
        public string IdTinh { get; set; }
        public string TenTinh { get; set; }
        public string TenHuyen { get; set; }
        public string TenHuyenEn { get; set; }
        public string IdHuyenCu { get; set; }
        public string IdHuyenCu1 { get; set; }
        public string TenHuyenCu { get; set; }

    }


    public class HuyenModel : HuyenBaseModel
    {

    }

    public class HuyenFilterModel : BaseQueryFilterModel
    {
        public HuyenFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHuyen";
        }
    }

    public class CreateHuyenModel
    {
        [Required(ErrorMessage = "Huyen.IdHuyen.NotRequire")]
        [MaxLength(5, ErrorMessage = "Huyen.IdHuyen.MaxLength(5)")]
        public string IdHuyen { get; set; }

        [MaxLength(5, ErrorMessage = "Huyen.IdTinh.MaxLength(5)")]
        [Required(ErrorMessage = "Huyen.IdTinh.NotRequire")]
        public string IdTinh { get; set; }

        [MaxLength(50, ErrorMessage = "Huyen.TenHuyen.MaxLength(50)")]
        [Required(ErrorMessage = "Huyen.TenHuyen.NotRequire")]
        public string TenHuyen { get; set; }

        [MaxLength(50, ErrorMessage = "Huyen.HuyenEn.MaxLength(50)")]
        public string TenHuyenEn { get; set; }


        public string IdHuyenCu { get; set; }


        public string IdHuyenCu1 { get; set; }



        public string TenHuyenCu { get; set; }

    }

    public class CreateManyHuyenModel
    {
        public List<CreateHuyenModel> listHuyenModels { get; set; }
    }

    public class UpdateHuyenModel : CreateHuyenModel
    {
        public void UpdateEntity(SvHuyen input)
        {
            input.IdHuyen = IdHuyen;
            input.IdTinh = IdTinh;
            input.TenHuyen = TenHuyen;
            input.TenHuyenEn = TenHuyenEn;
            input.IdHuyenCu = IdHuyenCu;
            input.IdHuyenCu1 = IdHuyenCu1;
            input.TenHuyenCu = TenHuyenCu;

        }
    }
}
