﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteMucHuongBhytCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteMucHuongBhytCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteMucHuongBhytCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteMucHuongBhytCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {MucHuongBhytConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvMucHuongBhyts.FirstOrDefaultAsync(x => x.IdMucHuongBhyt == id);

                _dataContext.SvMucHuongBhyts.Remove(entity);

                Log.Information($"Delete {MucHuongBhytConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa mức hưởng bhyt: {entity.KyHieu}",
                    ObjectCode = MucHuongBhytConstant.CachePrefix,
                    ObjectId = entity.IdMucHuongBhyt.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MucHuongBhytConstant.BuildCacheKey());
                _cacheService.Remove(MucHuongBhytConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
