﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxNganhQuery : IRequest<List<NganhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy ngành cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxNganhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxNganhQuery, List<NganhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NganhSelectItemModel>> Handle(GetComboboxNganhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = NganhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvNganhs
                               
                                select new NganhSelectItemModel()
                                {
                                    IdNganh = dt.IdNganh,
                                    MaNganh = dt.MaNganh,
                                    TenNganh = dt.TenNganh,
                                }).Distinct()
                                .OrderBy(x => x.TenNganh);

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenNganh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterNganhQuery : IRequest<PaginationList<NganhBaseModel>>
    {
        public NganhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách ngành có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterNganhQuery(NganhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterNganhQuery, PaginationList<NganhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<NganhBaseModel>> Handle(GetFilterNganhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvNganhs
                            select new NganhBaseModel
                            {
                                IdNganh = dt.IdNganh,
                                MaNganh = dt.MaNganh,
                                TenNganh = dt.TenNganh,
                                TenNganhEn = dt.TenNganhEn,
                                SuPham = dt.SuPham

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenNganh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<NganhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetNganhByIdQuery : IRequest<NganhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin ngành theo id
        /// </summary>
        /// <param name="id">Id ngành</param>
        public GetNganhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetNganhByIdQuery, NganhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<NganhModel> Handle(GetNganhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = NganhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvNganhs.FirstOrDefaultAsync(x => x.IdNganh == id);

                    return AutoMapperUtils.AutoMap<SvNganh, NganhModel>(entity);
                });
                return item;
            }
        }
    }
}
