﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.Core.LockDynamic
{
    public interface ILockDynamicHandler
    {
        Task<T> LimitNumberOfTimesAsync<T>(Func<Task<T>> func, LockDynamicModel lockDynamic);
        Task<T> LimitNumberOfFailuresAsync<T>(Func<Task<T>> func, LockDynamicModel lockDynamic);
    }
}
