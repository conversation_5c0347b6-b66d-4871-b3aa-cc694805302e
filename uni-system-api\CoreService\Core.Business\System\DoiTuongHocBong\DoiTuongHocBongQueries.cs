﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxDoiTuongHocBongQuery : IRequest<List<DoiTuongHocBongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy đối tượng học bổng cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDoiTuongHocBongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDoiTuongHocBongQuery, List<DoiTuongHocBongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DoiTuongHocBongSelectItemModel>> Handle(GetComboboxDoiTuongHocBongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DoiTuongHocBongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDoiTuongHocBongs.OrderBy(x => x.DoiTuongHocBong)
                                select new DoiTuongHocBongSelectItemModel()
                                {
                                    IdDoiTuongHocBong = dt.IdDoiTuongHocBong,
                                    MaDoiTuongHocBong = dt.MaDoiTuongHocBong,
                                    DoiTuongHocBong = dt.DoiTuongHocBong,
                                    SoTienTroCap = dt.SoTienTroCap
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.DoiTuongHocBong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDoiTuongHocBongQuery : IRequest<PaginationList<DoiTuongHocBongBaseModel>>
    {
        public DoiTuongHocBongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách đối tượng học bổng có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDoiTuongHocBongQuery(DoiTuongHocBongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDoiTuongHocBongQuery, PaginationList<DoiTuongHocBongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DoiTuongHocBongBaseModel>> Handle(GetFilterDoiTuongHocBongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDoiTuongHocBongs
                            select new DoiTuongHocBongBaseModel
                            {
                                IdDoiTuongHocBong = dt.IdDoiTuongHocBong,
                                MaDoiTuongHocBong = dt.MaDoiTuongHocBong,
                                DoiTuongHocBong = dt.DoiTuongHocBong,
                                SoTienTroCap = dt.SoTienTroCap,
                                PhanTramTroCap= dt.PhanTramTroCap

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.DoiTuongHocBong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<DoiTuongHocBongBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDoiTuongHocBongByIdQuery : IRequest<DoiTuongHocBongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin đối tượng học bổng theo id
        /// </summary>
        /// <param name="id">Id đối tượng học bổng</param>
        public GetDoiTuongHocBongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDoiTuongHocBongByIdQuery, DoiTuongHocBongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DoiTuongHocBongModel> Handle(GetDoiTuongHocBongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DoiTuongHocBongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDoiTuongHocBongs.FirstOrDefaultAsync(x => x.IdDoiTuongHocBong == id);

                    return AutoMapperUtils.AutoMap<SvDoiTuongHocBong, DoiTuongHocBongModel>(entity);
                });
                return item;
            }
        }
    }
}
