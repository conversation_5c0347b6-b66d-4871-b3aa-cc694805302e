﻿using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UserBaseForAuthModel
    {
        public int UserId { get; set; }
        public int Active { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public string MaCanBoUser { get; set; }
    }

    public class UserBaseModel
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string MaCanBoUser { get; set; }
        public string Password { get; set; }
        public string DienThoai { get; set; }
        public bool IsActive { get; set; }
        public int Active { get; set; }
        public int IdKhoa { get; set; }
    }

    public class GiaoVienBaseModel
    {
        public string MaCB { get; set; }
        public string HoTen { get; set; }
        public string TenDangNhap { get; set; }
        public string MatKhau { get; set; }
        public string Email { get; set; }
        public string Ten { get; set; }
    }

    public class UserModel : UserBaseModel
    {
    }

    public class CreateUserModel : UserModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateUserModel : UserModel
    {
        public void UpdateEntity(HtUser entity)
        {
            entity.FullName = FullName;
            entity.UserName = UserName;
            entity.Email = Email;
            entity.Active = IsActive ? 1 : 0;
            entity.MaCanBoUser = MaCanBoUser;
            // Nếu có nhập mật khẩu thì cập nhật mật khẩu
            if (!string.IsNullOrEmpty(this.Password))
            {
                entity.PassWord = XCrypto.MD5(Password);
            }
        }
    }

    public class UserCurrentUpdateModel
    {
        public int UserId { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string DienThoai { get; set; }
        public void UpdateUserInfo(HtUser entity)
        {
            entity.FullName = FullName;
            entity.Email = Email;
            entity.DienThoai = DienThoai;
        }
    }

    public class UserSelectItemModel
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public string MaCB { get; set; }
        public bool IsActive { get; set; }
    }

    public class UserQueryFilter
    {
        public string TextSearch { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public bool? IsActive { get; set; }
        public string PropertyName { get; set; } = "UserName";
        //asc - desc
        public string Ascending { get; set; } = "asc";
        public UserQueryFilter()
        {
            PageNumber = QueryFilter.DefaultPageNumber;
            PageSize = QueryFilter.DefaultPageSize;
        }
    }

    public class UserPermissionModel
    {
        public List<string> Roles { get; set; }
        public List<string> Permissions { get; set; }
        public bool IsActive { get; set; }
    }

    public class RoleOfUserCreateModel
    {
        public int UserId { get; set; }
        public List<int> RoleIds { get; set; }
    }

    public class UserChangePasswordModel
    {
        public int UserId { get; set; }
        public string OldPassword { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmNewPassword { get; set; }
    }

    public class SendEmailForgotPasswordModel
    {
        public string UserName { get; set; }
        [JsonIgnore]
        public bool IsIgnoreCheckCache { get; set; }
        [JsonIgnore]
        public string ReturnUrl { get; set; }
    }

    public class UserUpdatePasswordFromForgotPassModel
    {
        public string Token { get; set; }
        public string SecretKey { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmNewPassword { get; set; }
    }

    public class UserChangePasswordLockModel
    {
        /// <summary>
        /// Id người dùng
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// Số lần nhập sai mật khẩu (5 lần sẽ tạm khóa 15p)
        /// </summary>
        public int Times { get; set; }
        /// <summary>
        /// Thời gian sau cũng nhập sai mật khẩu
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }

    public class UserForgotPasswordLockModel
    {
        /// <summary>
        /// UserName
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// Số lần nhập sai mật khẩu (5 lần sẽ tạm khóa 15p)
        /// </summary>
        public int Times { get; set; }
        /// <summary>
        /// Thời gian sau cũng nhập sai mật khẩu
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
