﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phuong-an")]
    [ApiExplorerSettings(GroupName = "112. Phương án")]
    [Authorize]
    public class PhuongAnController : ApiControllerBase
    {
        public PhuongAnController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách phương án cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ kh<PERSON>a tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhuongAnSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhuongAnQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phương án có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<PhuongAnBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_AN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] PhuongAnQueryFilter filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterPhuongAnQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PhuongAnModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_AN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetPhuongAnByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới bênh viện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_AN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreatePhuongAnModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_AN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_AN_CREATE;


                return await _mediator.Send(new CreatePhuongAnCommand(model, u));
            });
        }


        /// <summary>
        /// Sửa bênh viện
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{request.id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_AN_EDIT))]
        public async Task<IActionResult> Update([FromBody] UpdatePhuongAnModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_AN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_AN_UPDATE;
                return await _mediator.Send(new UpdatePhuongAnCommand(request, u));
            });
        }

        /// <summary>
        /// Xóa bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_AN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_AN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_AN_DELETE;

                return await _mediator.Send(new DeletePhuongAnCommand(id, u));
            });
        }

    }
}
