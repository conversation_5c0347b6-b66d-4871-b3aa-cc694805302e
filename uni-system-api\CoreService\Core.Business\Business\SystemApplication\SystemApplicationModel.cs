﻿using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class SystemApplicationBaseModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "system-application.code.required")]
        public string Code { get; set; }

        [Required(ErrorMessage = "system-application.name.required")]
        [MaxLength(128, ErrorMessage = "system-application.name.max-length")]
        public string Name { get; set; }

        public string Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    public class SystemApplicationModel : SystemApplicationBaseModel
    {

    }

    public class CreateSystemApplicationModel : SystemApplicationModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateSystemApplicationModel : SystemApplicationModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(SystemApplication entity)
        {
            entity.Name = this.Name;
            entity.IsActive = this.IsActive;
            entity.Description = this.Description;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class SystemApplicationSelectItemModel : SelectItemModel
    {
    }

    public class SystemApplicationQueryFilter : BaseQueryFilterModel
    {
    }

    #region Elastic
    public class SystemApplicationElasticModel
    {
        public int Id { get; set; }

        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    #endregion
}
