{"AppSettings": {"EnableSwagger": "true", "Tittle": "Leader UniCore Service", "Description": "Net Core Framework", "TermsOfService": "", "Contact": {"Name": "HaiND", "Email": "<EMAIL>", "Url": "https://github.com/duchaindh94"}, "License": {"Name": "The MIT License (MIT).", "Url": "https://github.com/dotnet/core"}, "ReturnDetailError500Message": "true", "CorsOrigins": "http://localhost:5001,http://localhost:8100,http://localhost:8000,http://localhost:8101,http://localhost:8102", "EnableCache": "true", "ShowRequestDuration": "true"}, "Database": {"Leader": {"ConnectionString": {"MSSQLDatabase": "server=clouddatata10.unisoft.edu.vn;database=UNISOFT_FULL6_CDMNBG_TEST;User ID=laptrinh;password=*************;TrustServerCertificate=True;", "MSSQLDatabaseRead": "server=clouddatata10.unisoft.edu.vn;database=UNISOFT_FULL6_CDMNBG_TEST;User ID=laptrinh;password=*************;TrustServerCertificate=True;"}}, "System": {"ConnectionString": {"MSSQLDatabase": "server=clouddatata10.unisoft.edu.vn;database=UNISOFT_FULL6_CDMNBG_TEST;User ID=laptrinh;password=*************;TrustServerCertificate=True;", "MSSQLDatabaseRead": "server=clouddatata10.unisoft.edu.vn;database=UNISOFT_FULL6_CDMNBG_TEST;User ID=laptrinh;password=*************;TrustServerCertificate=True;"}}}, "MongoDBDatabaseSettings": {"ConnectionString": "mongodb+srv://haind:<EMAIL>/?retryWrites=true&w=majority", "DatabaseName": "UniCore_System_Log"}, "redis": {"enabled": "true", "configuration": "************:6379,password=thienan123,defaultDatabase=0", "instanceName": "UniSoft:", "timeLive": "30000"}, "RabbitMq": {"url": "amqp://localhost:5672", "username": "guest", "password": "guest", "ssl": "false", "vhost": "/netcore-service", "sendMail": {"exchange": "sendMail", "queue": "sendMail", "routingKey": "sendMail", "type": "direct"}}, "email": {"from": "<EMAIL>", "smtp": "smtp.gmail.com", "port": 587, "user": "Savis eContract", "sendtype": "sync", "password": "galmzdnbnhuithew", "ssl": "1"}, "Authentication": {"HOU": {"CasServerUrl": "https://cas.hou.edu.vn"}, "Jwt": {"Enable": "false", "Key": "NETCORE_SECRET_KEY", "Issuer": "NETCORE-ORION-CORP", "TimeToLive": "3600"}, "IdentityServer": {"Enable": "true", "Uri": "https://sso2.unisoft.edu.vn", "ClientId": "uni-hrm-portal-client", "Secret": "pt0bM7sY!9*cpT7s$MjGB4s", "Redirecturi": "http://localhost:8100/verification"}, "AdminUserName": "admin"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Core.API.Shared.CustomAuthHandler": "Error"}}, "AllowedHosts": "*", "PhanHe": {"Leader": "UniLeader"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Error", "System": "Error", "Core.API.Shared.CustomAuthHandler": "Error", "Microsoft.EntityFrameworkCore.Database.Command": "Error"}}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "File", "Args": {"path": "/opt/logs_folder/log-netcore/log-.txt", "rollingInterval": "Day", "fileSizeLimitBytes": 10485760, "retainedFileCountLimit": null, "rollOnFileSizeLimit": true}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] {MachineName} ({ThreadId}) <{SourceContext}> {Message}{NewLine}{Exception}"}}]}}]}}