﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System.Data;
using System;


namespace Core.Business
{
    public class GetFilterUsersAccessHeQuery : IRequest<PaginationList<UsersAccessHeBaseModel>>
    {
        public UsersAccessHeFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách phân quyền lớp người dùng
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterUsersAccessHeQuery(UsersAccessHeFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterUsersAccessHeQuery, PaginationList<UsersAccessHeBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<UsersAccessHeBaseModel>> Handle(GetFilterUsersAccessHeQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.HtUsersAccessHes
                            select new UsersAccessHeBaseModel
                            {
                                Id = dt.Id,
                                IdChuyenNganh = dt.IdChuyenNganh,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                                IdLop = dt.IdLop,
                                IdNganh = dt.IdNganh,
                                KhoaHoc = dt.KhoaHoc,
                                UserId = dt.UserId
                            });

                //if (!string.IsNullOrEmpty(filter.TextSearch))
                //{
                //    string ts = filter.TextSearch.Trim().ToLower();
                //    data = data.Where(x => x.TenUsersAccessHe.ToLower().Contains(ts));
                //}

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<UsersAccessHeBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetAccessHeByUsersIdQuery : IRequest<UsersAccessHeModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin User Access Hệ theo id
        /// </summary>
        /// <param name="id">Id Khu vực</param>
        public GetAccessHeByUsersIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetAccessHeByUsersIdQuery, UsersAccessHeModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<UsersAccessHeModel> Handle(GetAccessHeByUsersIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                //string cacheKey = UsersAccessHeConstant.BuildCacheKey(id.ToString());
                //var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                //{
                var entity = await _dataContext.HtUsersAccessHes.FirstOrDefaultAsync(x => x.Id == id);

                return AutoMapperUtils.AutoMap<HtUsersAccessHe, UsersAccessHeModel>(entity);
                //});
                //return item;
            }
        }
    }

    public class GetAccessHeLopByUserQuery : IRequest<List<UsersAccessHeByUserModel>>
    {
        public int UserId { get; set; }

        /// <summary>
        /// Lấy danh sách lớp được truy cập theo id người dùng
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetAccessHeLopByUserQuery(int userId)
        {
            UserId = userId;
        }

        public class Handler : IRequestHandler<GetAccessHeLopByUserQuery, List<UsersAccessHeByUserModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<UsersAccessHeByUserModel>> Handle(GetAccessHeLopByUserQuery request, CancellationToken cancellationToken)
            {
                var userId = request.UserId;

                // Danh sách phân hệ truy cập theo người dùng
                var lstAccessHe = await _dataContext.HtUsersAccessHes.Where(x => x.UserId == userId).ToListAsync();

                if (lstAccessHe.Count() == 0)
                {
                    return new List<UsersAccessHeByUserModel>();
                }

                var rs = new List<UsersAccessHeByUserModel>();

                // Danh sách hệ
                var lstHe = await _mediator.Send(new GetComboboxHeQuery());

                // TODO: Cần chuyển về 
                // Danh sách khoa
                //var lstKhoa = await _mediator.Send(new GetComboboxKhoaQuery());
                List<KhoaSelectItemModel> lstKhoa = await _dataContext.SvKhoas.Select(x => new KhoaSelectItemModel() { IdKhoa = x.IdKhoa, TenKhoa = x.TenKhoa }).ToListAsync();

                // Danh sách chuyên ngành
                //var lstChuyenNganh = await _mediator.Send(new GetComboboxChuyenNganhQuery());
                List<ChuyenNganhSelectItemModel> lstChuyenNganh = await _dataContext.SvChuyenNganhs.Select(x => new ChuyenNganhSelectItemModel() { IdChuyenNganh = x.IdChuyenNganh, ChuyenNganh = x.ChuyenNganh, IdNganh = x.IdNganh, MaChuyenNganh = x.MaChuyenNganh }).ToListAsync();

                // Danh sách ngành
                var lstNganh = await _mediator.Send(new GetComboboxNganhQuery());

                // Danh sách lớp
                var lstLop = await _mediator.Send(new GetComboboxLopQuery());

                List<int> lstDt = new List<int>();

                foreach (var item in lstAccessHe)
                {
                    var dt = lstLop.Where(x =>
                        (x.IdHe == item.IdHe || item.IdHe == 0)
                        && (x.IdKhoa == item.IdKhoa || item.IdKhoa == 0)
                        && (x.KhoaHoc == item.KhoaHoc || item.KhoaHoc == 0)
                        && (x.IdChuyenNganh == item.IdChuyenNganh || item.IdChuyenNganh == 0)
                        ).Select(x => x.IdLop);
                    lstDt.AddRange(dt);
                }

                // Loại bỏ Id lớp bị trùng
                lstDt = new HashSet<int>(lstDt).ToList();

                rs = (from dt in lstDt
                      join lop in lstLop on dt equals lop.IdLop
                      join he in lstHe on lop.IdHe equals he.IdHe
                      join khoa in lstKhoa on lop.IdKhoa equals khoa.IdKhoa
                      join chuyenNganh in lstChuyenNganh on lop.IdChuyenNganh equals chuyenNganh.IdChuyenNganh
                      join nganh in lstNganh on chuyenNganh.IdNganh equals nganh.IdNganh
                      select new UsersAccessHeByUserModel
                      {
                          IdLop = lop.IdLop,
                          IdHe = lop.IdHe,
                          MaHe = he.MaHe,
                          He = he.TenHe,
                          IdKhoa = lop.IdKhoa,
                          Khoa = khoa.TenKhoa,
                          IdChuyenNganh = lop.IdChuyenNganh,
                          MaChuyenNganh = chuyenNganh.MaChuyenNganh,
                          ChuyenNganh = chuyenNganh.ChuyenNganh,
                          IdNganh = chuyenNganh.IdNganh,
                          Nganh = nganh.TenNganh,
                          NienKhoa = lop.NienKhoa,
                          IdDt = lop.IdDt,
                          IdPhong = lop.IdPhong,
                          CaHoc = lop.CaHoc,
                          KhoaHoc = lop.KhoaHoc,
                          RaTruong = lop.RaTruong,
                          SoSv = lop.SoSv,
                          TenLop = lop.TenLop
                      }).ToList();

                rs = rs.OrderBy(x => x.He).ThenBy(x => x.Khoa).ThenBy(x => x.KhoaHoc).ThenBy(x => x.ChuyenNganh).ThenBy(x => x.TenLop).ToList();

                return rs;
            }
        }
    }

    public class GetAccessChuongTrinhDaoTaoByUserQuery : IRequest<List<int>>
    {
        public string UserName { get; set; }

        /// <summary>
        /// Lấy danh sách idDaoTao được truy cập theo id người dùng
        /// </summary>
        /// <param name="userName">Id người dùng</param>
        public GetAccessChuongTrinhDaoTaoByUserQuery(string userName)
        {
            UserName = userName;
        }

        public class Handler : IRequestHandler<GetAccessChuongTrinhDaoTaoByUserQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;
            private readonly ICallStoreHelper _callStoreHelper;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<List<int>> Handle(GetAccessChuongTrinhDaoTaoByUserQuery request, CancellationToken cancellationToken)
            {
                List<int> rs = new List<int>();
                var userName = request.UserName;
                var tableData = _callStoreHelper.CallStoresvLopGetByUserName(userName);
                var result = new List<LopHocUserModel>();
                foreach (DataRow row in tableData.Rows)
                {
                    result.Add(row.ToObjectWithColumnName<LopHocUserModel>());
                };
                return result.Select(x => x.IdDt).Distinct().ToList();
            }
        }
    }

    public class GetAccessHeByIdUsersQuery : IRequest<List<AccessHeByUserBaseModel>>
    {
        public int UserId { get; set; }

        /// <summary>
        /// Lấy thông tin User Access Hệ theo id
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetAccessHeByIdUsersQuery(int userId)
        {
            UserId = userId;
        }

        public class Handler : IRequestHandler<GetAccessHeByIdUsersQuery, List<AccessHeByUserBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<AccessHeByUserBaseModel>> Handle(GetAccessHeByIdUsersQuery request, CancellationToken cancellationToken)
            {
                var userId = request.UserId;
                var data = await (from dt in _dataContext.HtUsersAccessHes
                                  join he in _dataContext.SvHes on dt.IdHe equals he.IdHe into heJoin
                                  from he in heJoin.DefaultIfEmpty()
                                  join khoa in _dataContext.SvKhoas on dt.IdKhoa equals khoa.IdKhoa into khoaJoin
                                  from khoa in khoaJoin.DefaultIfEmpty()
                                  join chuyenNganh in _dataContext.SvChuyenNganhs on dt.IdChuyenNganh equals chuyenNganh.IdChuyenNganh into chuyenNganhJoin
                                  from chuyenNganh in chuyenNganhJoin.DefaultIfEmpty()
                                  join nganh in _dataContext.SvNganhs on dt.IdNganh equals nganh.IdNganh into nganhJoin
                                  from nganh in nganhJoin.DefaultIfEmpty()
                                  where dt.UserId == userId
                                  select new AccessHeByUserBaseModel
                                  {
                                      Id = dt.Id,
                                      IdChuyenNganh = dt.IdChuyenNganh,
                                      IdHe = dt.IdHe,
                                      IdKhoa = dt.IdKhoa,
                                      IdLop = dt.IdLop,
                                      IdNganh = dt.IdNganh,
                                      KhoaHoc = dt.KhoaHoc,
                                      TenHe = he.TenHe ?? "0",
                                      TenKhoa = khoa.TenKhoa ?? "0",
                                      TenNganh = nganh.TenNganh ?? "0",
                                      TenChuyenNganh = chuyenNganh.ChuyenNganh ?? "0",
                                      UserId = dt.UserId
                                  }).ToListAsync();

                foreach (var item in data)
                {
                    if (!string.IsNullOrWhiteSpace(item.IdLop))
                    {
                        var lopIds = item.IdLop.Split(',')
                                               .Where(id => !string.IsNullOrWhiteSpace(id))
                                               .Select(id => Convert.ToInt32(id))
                                               .ToList();

                        item.TenLop = string.Join(", ", _dataContext.SvLops
                                                        .Where(lop => lopIds.Contains(lop.IdLop))
                                                        .Select(lop => lop.TenLop)
                                                        .ToList()) ?? "0";
                    }
                }

                return data;
            }

        }
    }

}
