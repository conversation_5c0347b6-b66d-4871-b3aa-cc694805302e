﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreatePhuongThucDongCommand : IRequest<Unit>
    {
        public CreatePhuongThucDongModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Thêm mới phương thức đóng
        /// </summary>
        /// <param name="model">Thông tin phương thức đóng cần thêm mới</param>
        /// <param name="requestUser">Thông tin người request</param>
        public CreatePhuongThucDongCommand(CreatePhuongThucDongModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<CreatePhuongThucDongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreatePhuongThucDongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Create {PhuongThucDongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreatePhuongThucDongModel, SvPhuongThucDong>(model);

                var checkMaPhuongThucDong = await _dataContext.SvPhuongThucDongs.AnyAsync(x => x.PhuongThucDong == entity.PhuongThucDong);
                if (checkMaPhuongThucDong)
                {
                    throw new ArgumentException($"{_localizer["system-application.PhuongThucDong.existed"]}");
                }

                await _dataContext.SvPhuongThucDongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {PhuongThucDongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phương thức đóng : {entity.PhuongThucDong}",
                    ObjectCode = PhuongThucDongConstant.CachePrefix,
                    ObjectId = entity.IdPhuongThucDong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhuongThucDongConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
