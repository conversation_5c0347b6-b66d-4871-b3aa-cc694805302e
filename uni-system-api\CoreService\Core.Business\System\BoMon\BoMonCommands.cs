﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Minio.DataModel;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateBoMonCommand : IRequest<Unit>
    {
        public CreateBoMonModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateBoMonCommand(CreateBoMonModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateBoMonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateBoMonModel, TkbBoMon>(model);

                var checkCode = await _dataContext.TkbBoMons.AnyAsync(x => x.IdBoMon == entity.IdBoMon || x.BoMon == entity.BoMon || x.MaBoMon == entity.MaBoMon);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["BoMon.Existed", entity.BoMon.ToString()]}");
                }

                await _dataContext.TkbBoMons.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tôn giáo: {entity.BoMon}",
                    ObjectCode = BoMonConstant.CachePrefix,
                    ObjectId = entity.IdBoMon.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyBoMonCommand : IRequest<Unit>
    {
        public CreateManyBoMonModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyBoMonCommand(CreateManyBoMonModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyBoMonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listBoMonAdd = model.listBoMonModels.Select(x => x.BoMon).ToList();
                var listMaBoMonAdd = model.listBoMonModels.Select(x => x.MaBoMon).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyBoMonModel, TkbBoMon>(model);

                // Check data duplicate
                if (listBoMonAdd.Count() != listBoMonAdd.Distinct().Count() || listMaBoMonAdd.Count() != listMaBoMonAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbBoMons.AnyAsync(x => listBoMonAdd.Contains(x.BoMon)) || await _dataContext.TkbBoMons.AnyAsync(x => listMaBoMonAdd.Contains(x.MaBoMon)))
                {
                    throw new ArgumentException($"{_localizer["BoMon.Existed"]}");
                }

                var listEntity = model.listBoMonModels.Select(x => new TkbBoMon()
                {
                    IdBoMon = x.IdBoMon,
                    MaBoMon = x.MaBoMon,
                    BoMon = x.BoMon,
                    SoNhom = x.SoNhom,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdBoMon).ToList();

                Log.Information($"Create many {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Tôn Giáo từ file excel",
                    ObjectCode = BoMonConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateBoMonCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateBoMonModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateBoMonCommand(int id, UpdateBoMonModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateBoMonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbBoMons.FirstOrDefaultAsync(dt => dt.IdBoMon == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.TkbBoMons.AnyAsync(x => (x.BoMon == model.BoMon || x.MaBoMon == model.MaBoMon) && x.IdBoMon != model.IdBoMon);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["BoMon.Existed", model.BoMon.ToString()]}");
                }

                Log.Information($"Before Update {BoMonConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbBoMons.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {BoMonConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tôn giáo: {entity.BoMon}",
                    ObjectCode = BoMonConstant.CachePrefix,
                    ObjectId = entity.IdBoMon.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey(entity.IdBoMon.ToString()));
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class DeleteBoMonCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        public DeleteBoMonCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICallStoreHelper _callStoreHelper;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(
                SystemDataContext dataContext,
                ICallStoreHelper callStoreHelper,
                ICacheService cacheService,
                IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteBoMonCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;

                Log.Information($"Delete {BoMonConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                // Lấy dữ liệu bộ môn từ stored procedure
                var dataTable = _callStoreHelper.CallStoreDanhSachBoMonAsync(0, 0);
                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    throw new InvalidOperationException(_localizer["BoMonDataNotFound"]);
                }

                var data = dataTable.Rows.Cast<DataRow>()
                    .Select(row => row.ToObjectWithColumnName<BoMonBaseModel>())
                    .ToList();

                // Kiểm tra ràng buộc
                var boMon = data.FirstOrDefault(item => item.IdBoMon == id);
                if (boMon != null && (boMon.SoMon > 0 || boMon.SoGiangVien > 0))
                {
                    throw new ArgumentException(_localizer["BoMonDaGan"]);
                }

                // Tìm bản ghi cần xóa
                var entity = await _dataContext.TkbBoMons.FirstOrDefaultAsync(x => x.IdBoMon == id, cancellationToken);
                if (entity == null)
                {
                    throw new KeyNotFoundException(_localizer["BoMonNotFound"]);
                }

                // Xóa bản ghi
                _dataContext.TkbBoMons.Remove(entity);

                Log.Information($"Delete {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");

                // Thêm thông tin vào log hành động
                systemLog.ListAction.Add(new ActionDetail
                {
                    Description = $"Xóa Bộ môn: {entity.BoMon}",
                    ObjectCode = BoMonConstant.CachePrefix,
                    ObjectId = entity.IdBoMon.ToString()
                });

                // Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                _cacheService.Remove(BoMonConstant.BuildCacheKey(id.ToString()));

                // Lưu thay đổi vào cơ sở dữ liệu
                await _dataContext.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }


    public class DeleteManyBoMonCommand : IRequest<Unit>
    {
        public List<int> ListId { get; set; }
        public SystemLogModel SystemLog { get; set; }

        public DeleteManyBoMonCommand(List<int> listId, SystemLogModel systemLog)
        {
            ListId = listId;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteManyBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICallStoreHelper _callStoreHelper;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(
                SystemDataContext dataContext,
                ICallStoreHelper callStoreHelper,
                ICacheService cacheService,
                IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteManyBoMonCommand request, CancellationToken cancellationToken)
            {
                var listId = request.ListId;
                var systemLog = request.SystemLog;

                var dataTable = _callStoreHelper.CallStoreDanhSachBoMonAsync(0, 0);
                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    throw new InvalidOperationException(_localizer["BoMonDataNotFound"]);
                }

                var data = dataTable.Rows.Cast<DataRow>()
                    .Select(row => row.ToObjectWithColumnName<BoMonBaseModel>())
                    .ToList();

                foreach (var id in listId)
                {

                    var boMon = data.FirstOrDefault(item => item.IdBoMon == id);
                    if (boMon != null && (boMon.SoMon > 0 || boMon.SoGiangVien > 0))
                    {
                        throw new ArgumentException(_localizer["BoMonDaGan"]);
                    }

                    // Tìm bản ghi cần xóa
                    var entity = await _dataContext.TkbBoMons.FirstOrDefaultAsync(x => x.IdBoMon == id, cancellationToken);
                    if (entity == null)
                    {
                        throw new KeyNotFoundException(_localizer["BoMonNotFound"]);
                    }

                    // Xóa bản ghi
                    _dataContext.TkbBoMons.Remove(entity);

                    Log.Information($"Delete {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");

                    // Thêm thông tin vào log hành động
                    systemLog.ListAction.Add(new ActionDetail
                    {
                        Description = $"Xóa Bộ môn: {entity.BoMon}",
                        ObjectCode = BoMonConstant.CachePrefix,
                        ObjectId = entity.IdBoMon.ToString()
                    });

                    // Xóa cache
                    _cacheService.Remove(BoMonConstant.BuildCacheKey());
                    _cacheService.Remove(BoMonConstant.BuildCacheKey(id.ToString()));

                }


                // Lưu thay đổi vào cơ sở dữ liệu
                await _dataContext.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }



    public class UpdateMonHocBoMonCommand : IRequest<Unit>
    {
        public UpdateMonHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateMonHocBoMonCommand(UpdateMonHocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateMonHocBoMonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateMonHocBoMonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Update {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));
                var listEntities = await _dataContext.SvMonHocs.Where(dt => model.ListIdMonHoc.Contains(dt.IdMonHoc)).ToListAsync();

                var notFoundIds = model.ListIdMonHoc.Except(listEntities.Select(e => e.IdMonHoc)).ToList();
                if (notFoundIds.Any())
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}: {string.Join(", ", notFoundIds)}");
                }

                foreach (var entity in listEntities)
                {
                    Log.Information($"Before Update {BoMonConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);

                    Log.Information($"After Update {BoMonConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    Log.Information($"Update {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa môn học khỏi bộ môn",
                        ObjectCode = BoMonConstant.CachePrefix,
                        ObjectId = entity.IdMonHoc.ToString()
                    });

                    // Xóa cache
                    _cacheService.Remove(BoMonConstant.BuildCacheKey(entity.IdMonHoc.ToString()));
                }

                _dataContext.SvMonHocs.UpdateRange(listEntities);
                await _dataContext.SaveChangesAsync();
                _cacheService.Remove(BoMonConstant.BuildCacheKey());




                return Unit.Value;
            }
        }
    }


    public class CreateBoMonGiangVienCommand : IRequest<Unit>
    {
        public CreateBoMonGiangVienModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateBoMonGiangVienCommand(CreateBoMonGiangVienModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateBoMonGiangVienCommand, Unit>
        {
            private ICallStoreHelper _callStoreHelper;
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, ICallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateBoMonGiangVienCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var dataTable = _callStoreHelper.CallStoreDanhSachGiangVienTheoBoMonAsync(model.IdBoMon);
                var data = new List<GiangVienBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<GiangVienBaseModel>());
                }

                foreach (var item in model.ListIdCb)
                {
                    if (!(data.Any(gv => gv.IdCb == item)))
                    {
                        _callStoreHelper.CallStoreCreateGiangVienTheoBoMonAsync(model.IdBoMon, item);
                    }


                    Log.Information($"Create {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới bộ môn giảng viên: ",
                        ObjectCode = BoMonConstant.CachePrefix,
                        ObjectId = model.IdBoMon.ToString()
                    });
                }


                //Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteBoMonGiangVienCommand : IRequest<Unit>
    {
        public DeleteBoMonGiangVienModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteBoMonGiangVienCommand(DeleteBoMonGiangVienModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteBoMonGiangVienCommand, Unit>
        {
            private ICallStoreHelper _callStoreHelper;
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, ICallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteBoMonGiangVienCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {BoMonConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var dataTable = _callStoreHelper.CallStoreDanhSachGiangVienTheoBoMonAsync(model.IdBm);
                var data = new List<GiangVienBaseModel>();
                foreach (DataRow row in dataTable.Rows)
                {

                    data.Add(row.ToObjectWithColumnName<GiangVienBaseModel>());
                }
                foreach (var item in model.ListIdCb)
                {
                    if (data.Any(gv => gv.IdCb == item))
                    {
                        _callStoreHelper.CallStoreDeleteGiangVienTheoBoMonAsync(model.IdBm, item);

                        Log.Information($"Delete {BoMonConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");
                        systemLog.ListAction.Add(new ActionDetail()
                        {
                            Description = $"Thêm mới bộ môn giảng viên:{item} ",
                            ObjectCode = BoMonConstant.CachePrefix,
                            ObjectId = item.ToString()
                        });
                    }

                }


                //Xóa cache
                _cacheService.Remove(BoMonConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
