﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetFilterChiTieuTuyenSinhQuery : IRequest<PaginationList<ChiTieuTuyenSinhBaseModel>>
    {
        public ChiTieuTuyenSinhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách chỉ tiêu tuyển sinh có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChiTieuTuyenSinhQuery(ChiTieuTuyenSinhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChiTieuTuyenSinhQuery, PaginationList<ChiTieuTuyenSinhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChiTieuTuyenSinhBaseModel>> Handle(GetFilterChiTieuTuyenSinhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvChiTieuTuyenSinhs
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join nganh in _dataContext.SvNganhs on dt.IdNganh equals nganh.IdNganh
                            select new ChiTieuTuyenSinhBaseModel
                            {
                                IdChiTieuTuyenSinh = dt.IdChiTieuTuyenSinh,
                                NamTuyenSinh = dt.NamTuyenSinh,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe,
                                IdNganh = dt.IdNganh,
                                TenNganh = nganh.TenNganh,
                                ChiTieuTuyenSinh = dt.ChiTieuTuyenSinh,
                                DiemSanXetTuyen = dt.DiemSanXetTuyen,
                                ChiTieuTuyenSinh1 = dt.ChiTieuTuyenSinh1

                            });
                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.NamTuyenSinh.ToString().ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ChiTieuTuyenSinhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetChiTieuTuyenSinhByIdQuery : IRequest<ChiTieuTuyenSinhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin chỉ tiêu tuyển sinh theo id
        /// </summary>
        /// <param name="id">Id chỉ tiêu tuyển sinh</param>
        public GetChiTieuTuyenSinhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChiTieuTuyenSinhByIdQuery, ChiTieuTuyenSinhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChiTieuTuyenSinhModel> Handle(GetChiTieuTuyenSinhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ChiTieuTuyenSinhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvChiTieuTuyenSinhs.FirstOrDefaultAsync(x => x.IdChiTieuTuyenSinh == id);

                    return AutoMapperUtils.AutoMap<SvChiTieuTuyenSinh, ChiTieuTuyenSinhModel>(entity);
                });
                return item;
            }
        }
    }
}
