﻿using Core.Shared;

namespace Core.Business
{
    public class ThamSoHeThongConstant
    {
        public const string CachePrefix = CacheConstants.THAM_SO_HE_THONG;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
