﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("sys_file_attachment")]
    public class FileAttachment
    {
        [Key]
        public Guid Id { get; set; }
        public string BucketName { get; set; }
        public string ObjectName { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long? Length { get; set; }
        public int? Source { get; set; }
        public string CreateUserName { get; set; }
        public string ModifyUserName { get; set; }
        public DateTime? CreateDate { get; set; }
        public DateTime? ModifyDate { get; set; }
    }
}