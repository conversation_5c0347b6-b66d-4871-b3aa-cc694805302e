﻿using Microsoft.AspNetCore.Http;
using Serilog.Context;
using System;
using System.Threading.Tasks;

namespace Core.Shared.Middlewares
{
    public class HttpRequestMiddleware
    {
        private readonly RequestDelegate _next;

        public HttpRequestMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            if (!context.Request.Headers.TryGetValue("X-Correlation-Id", out var correlationId))
            {
                correlationId = Guid.NewGuid().ToString();
                // set header X-CorrelationId if not exist
                context.Request.Headers["X-Correlation-Id"] = correlationId;
            }

            LogContext.PushProperty("CorrelationId", correlationId.ToString());

            context.Response.Headers["X-Correlation-Id"] = correlationId.ToString();

            await _next(context);
        }
    }
}
