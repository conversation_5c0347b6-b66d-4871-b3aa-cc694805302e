﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ToaNhaSelectItemModel
    {
        public int IdNha { get; set; }
        public string MaNha { get; set; }
        public string TenNha { get; set; }
    }

    public class ToaNhaBaseModel
    {
        public int IdNha { get; set; }
        public string MaNha { get; set; }
        public string TenNha { get; set; }
        public int IdCoSo { get; set; }
        public string TenCoSo { get; set; }

    }


    public class ToaNhaModel : ToaNhaBaseModel
    {

    }

    public class ToaNhaFilterModel : BaseQueryFilterModel
    {
        public ToaNhaFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNha";
        }
    }

    public class CreateToaNhaModel
    {
        [Required(ErrorMessage = "ToaNha.IdNha.NotRequire")]
        public int IdNha { get; set; }

        [MaxLength(10, ErrorMessage = "ToaNha.MaNha.MaxLength(10)")]
        [Required(ErrorMessage = "ToaNha.MaNha.NotRequire")]
        public string MaNha { get; set; }

        [MaxLength(50, ErrorMessage = "ToaNha.TenNha.MaxLength(50)")]
        [Required(ErrorMessage = "ToaNha.TenNha.NotRequire")]
        public string TenNha { get; set; }

        [Required(ErrorMessage = "ToaNha.IDCoSo.NotRequire")]
        public int IDCoSo { get; set; }

    }

    public class CreateManyToaNhaModel
    {
        public List<CreateToaNhaModel> listToaNhaModels { get; set; }
    }

    public class UpdateToaNhaModel : CreateToaNhaModel
    {
        public void UpdateEntity(TkbToaNha input)
        {
            input.IdNha = IdNha;
            input.MaNha = MaNha;
            input.TenNha = TenNha;
            input.IdCoSo = IDCoSo;

        }
    }
}
