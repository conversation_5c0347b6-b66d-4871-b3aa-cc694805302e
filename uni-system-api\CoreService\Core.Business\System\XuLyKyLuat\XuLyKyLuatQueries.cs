﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxXuLyQuery : IRequest<List<XuLySelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy xử lý kỷ luật cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXuLyQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXuLyQuery, List<XuLySelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XuLySelectItemModel>> Handle(GetComboboxXuLyQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XuLyKyLuatConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXuLys.OrderBy(x => x.XuLy)
                                select new XuLySelectItemModel()
                                {
                                    IdXuLy = dt.IdXuLy,
                                    IdCap = dt.IdCap,
                                    XuLy = dt.XuLy
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XuLy.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXuLyQuery : IRequest<PaginationList<XuLyBaseModel>>
    {
        public XuLyFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách xử lý kỷ luật có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXuLyQuery(XuLyFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXuLyQuery, PaginationList<XuLyBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XuLyBaseModel>> Handle(GetFilterXuLyQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXuLys
                            join cap in _dataContext.SvCapKhenThuongKyLuats on dt.IdCap equals cap.IdCap
                            select new XuLyBaseModel
                            {
                                IdXuLy = dt.IdXuLy,
                                IdCap = dt.IdCap,
                                TenCap = cap.TenCap,
                                XuLy = dt.XuLy,
                                DiemPhat = dt.DiemPhat,
                                SoThang = dt.SoThang,
                                MucXuLy = dt.MucXuLy,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XuLy.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XuLyBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXuLyByIdQuery : IRequest<XuLyModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin xử lý kỷ luật theo id
        /// </summary>
        /// <param name="id">Id xử lý kỷ luật</param>
        public GetXuLyByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXuLyByIdQuery, XuLyModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XuLyModel> Handle(GetXuLyByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XuLyKyLuatConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXuLys.FirstOrDefaultAsync(x => x.IdXuLy == id);

                    return AutoMapperUtils.AutoMap<SvXuLy, XuLyModel>(entity);
                });
                return item;
            }
        }
    }
}
