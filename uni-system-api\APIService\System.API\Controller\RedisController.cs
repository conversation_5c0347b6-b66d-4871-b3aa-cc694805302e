﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Serilog;
using System;
using System.Text;
using Core.Business.Core;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("system/v1/redis")]
    [ApiExplorerSettings(GroupName = "97. Redis-test", IgnoreApi = false)]
    [AllowAnonymous]
    public class RedisController : ApiControllerBase
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IRedisHandler _redisHandler;
        public RedisController(IDistributedCache distributedCache, IRedisHandler redisHandler, IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
            _distributedCache = distributedCache;
            _redisHandler = redisHandler;
        }

        /// <summary>
        /// Test redis connect
        /// </summary>
        /// <returns></returns>
        /// <response code="201">Returns the newly created item</response>
        /// <response code="400">If the item is null</response>
        [HttpGet, Route("cache")]
        //[Authorize]
        //[ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.any,read,NHAT_KY_HE_THONG_VIEW")] // Người dùng cần có 1 trong 2 quyền any hoặc read
        public string Get()
        {
            string cacheKey = "TheTime";
            string currentTime = DateTime.Now.ToString();
            string cachedTime = _distributedCache.GetString(cacheKey);
            if (string.IsNullOrEmpty(cachedTime))
            {
                // cachedTime = "Expired";
                // Cache expire trong 300s
                DistributedCacheEntryOptions options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(300));
                // Nạp lại giá trị mới cho cache
                _distributedCache.SetString(cacheKey, currentTime, options);
                cachedTime = _distributedCache.GetString(cacheKey);
            }

            var netCoreVer = System.Environment.Version;
            var runtimeVer = System.Runtime.InteropServices.RuntimeInformation.FrameworkDescription;

            Log.Information($"NetCore version: {netCoreVer.ToString()}");
            Log.Information($"Runtime version: {runtimeVer.ToString()}");

            Log.Information($"{_localizer["redis.test.label"]}");

            string result = $"{_localizer["redis.test.label"]}" +
                $"\n{_localizer["redis.test.current-time"]} : {currentTime} " +
                $"\n{_localizer["redis.test.cache-time"]} : {cachedTime}";
            return result;
        }



        /// <summary>
        /// DeleteAsync
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost, Route("delete-async/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(string key)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.DeleteAsync(key);
            });
        }

        /// <summary>
        /// DeleteAsync
        /// </summary>
        /// <param name="key"></param>
        /// <param name="hashKey"></param>
        /// <returns></returns>
        [HttpPost, Route("delete-async/{key}/{hashKey}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(string key, string hashKey)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.DeleteHashAsync(key, hashKey);
            });
        }

        #region Increase - Decrease
        /// <summary>
        /// SetLongAsync
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("set-long-async")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetAsync([FromBody] RedisInCreaseModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.SetLongValueAsync(model.Key, model.Value);
            });
        }

        [HttpGet, Route("increment-async/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> IncrementAsync([FromRoute] string key)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.StringIncrementAsync(key);
            });
        }

        [HttpGet, Route("decrement-async/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> StringDecrementAsync([FromRoute] string key)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.StringDecrementAsync(key);
            });
        }

        #endregion

        /// <summary>
        /// SetAsync
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("set-async")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetAsync([FromBody] RedisModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.SetAsync(model.Key, model.Value);
            });
        }

        /// <summary>
        /// SetAsyncWithTime
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("set-async-with-time")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetAsyncWithTime([FromBody] RedisModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.SetAsync(model.Key, model.Value, new TimeSpan(0, 0, model.Second));
            });
        }

        /// <summary>
        /// SetAsync
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpGet, Route("get-async/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetAsync(string key)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _redisHandler.GetAsync(key);
            });
        }
    }
}
