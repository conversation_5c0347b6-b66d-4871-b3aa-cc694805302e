﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace Core.Business
{
    public class GetComboboxNhomChungChiQuery : IRequest<List<NhomChungChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 4 cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxNhomChungChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxNhomChungChiQuery, List<NhomChungChiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NhomChungChiSelectItemModel>> Handle(GetComboboxNhomChungChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = NhomChungChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvNhomChungChis.OrderBy(x => x.NhomChungChi)
                                select new NhomChungChiSelectItemModel()
                                {
                                    IdNhomChungChi = dt.IdNhomChungChi,
                                    KyHieuNhom = dt.KyHieuNhom,
                                    NhomChungChi = dt.NhomChungChi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.NhomChungChi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterNhomChungChiQuery : IRequest<PaginationList<NhomChungChiBaseModel>>
    {
        public NhomChungChiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 4 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterNhomChungChiQuery(NhomChungChiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterNhomChungChiQuery, PaginationList<NhomChungChiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<NhomChungChiBaseModel>> Handle(GetFilterNhomChungChiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvNhomChungChis
                            select new NhomChungChiBaseModel
                            {
                                IdNhomChungChi = dt.IdNhomChungChi,
                                KyHieuNhom = dt.KyHieuNhom,
                                NhomChungChi = dt.NhomChungChi
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.NhomChungChi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<NhomChungChiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetNhomChungChiByIdQuery : IRequest<NhomChungChiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 4 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 4</param>
        public GetNhomChungChiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetNhomChungChiByIdQuery, NhomChungChiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<NhomChungChiModel> Handle(GetNhomChungChiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = NhomChungChiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvNhomChungChis.FirstOrDefaultAsync(x => x.IdNhomChungChi == id);

                    return AutoMapperUtils.AutoMap<SvNhomChungChi, NhomChungChiModel>(entity);
                });
                return item;
            }
        }
    }
}
