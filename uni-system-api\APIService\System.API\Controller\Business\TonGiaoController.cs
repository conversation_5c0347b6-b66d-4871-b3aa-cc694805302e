﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/ton-giao")]
    [ApiExplorerSettings(GroupName = "12. Tôn giáo")]
    [Authorize]
    public class TonGiaoController : ApiControllerBase
    {
        public TonGiaoController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Tôn giáo cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TonGiaoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTonGiaoQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Tôn giáo có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TonGiaoBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TonGiaoFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTonGiaoQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết tôn giáo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TonGiaoModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTonGiaoByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới tôn giáo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTonGiaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TON_GIAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TON_GIAO_CREATE;


                return await _mediator.Send(new CreateTonGiaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel tôn giáo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyTonGiaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TON_GIAO_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TON_GIAO_CREATE_MANY;


                return await _mediator.Send(new CreateManyTonGiaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa tôn giáo
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateTonGiaoModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TON_GIAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TON_GIAO_UPDATE;
                return await _mediator.Send(new UpdateTonGiaoCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa tôn giáo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TON_GIAO_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TON_GIAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TON_GIAO_DELETE;

                return await _mediator.Send(new DeleteTonGiaoCommand(id, u.SystemLog));
            });
        }

    }
}
