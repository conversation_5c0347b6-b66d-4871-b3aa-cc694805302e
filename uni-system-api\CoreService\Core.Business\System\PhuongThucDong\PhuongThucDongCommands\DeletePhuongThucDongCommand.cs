﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeletePhuongThucDongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Xóa phương thức đóng theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Id phương thức đóng cần xóa</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public DeletePhuongThucDongCommand(int id, RequestUser requestUser)
        {
            Id = id;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<DeletePhuongThucDongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeletePhuongThucDongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var requestUser = request.RequestUser;
                Log.Information($"Delete {PhuongThucDongConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SvPhuongThucDongs.FirstOrDefaultAsync(x => x.IdPhuongThucDong == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SvPhuongThucDongs.Remove(dt);

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phương thức đóng: {dt.PhuongThucDong}",
                    ObjectCode = PhuongThucDongConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(PhuongThucDongConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(PhuongThucDongConstant.BuildCacheKey());

                Log.Information($"Delete {PhuongThucDongConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
