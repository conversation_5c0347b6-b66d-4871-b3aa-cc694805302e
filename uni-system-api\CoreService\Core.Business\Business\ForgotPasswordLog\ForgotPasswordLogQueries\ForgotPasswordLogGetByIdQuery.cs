﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Lấy nhật ký quên mật khẩu theo Id
    /// </summary>
    /// <param name="id">Id nhật ký quên mật khẩu</param>
    /// <returns>Thông tin nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogGetByIdQuery : IRequest<ForgotPasswordLog>
    {
        public string Id { get; set; }

        public ForgotPasswordLogGetByIdQuery(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogGetByIdQuery, ForgotPasswordLog>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemReadDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<ForgotPasswordLog> Handle(ForgotPasswordLogGetByIdQuery request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    return await _logs.Find(log => log.Id == request.Id).FirstOrDefaultAsync();
                }
                else
                {
                    int idLog = 0;
                    int.TryParse(request.Id, out idLog);
                    var dt = await _dataContext.ForgotPasswordLogs.AsNoTracking().FirstOrDefaultAsync(x => x.Id == idLog);
                    return AutoMapperUtils.AutoMap<ForgotPasswordLogEntity, ForgotPasswordLog>(dt);
                }
            }
        }
    }
}
