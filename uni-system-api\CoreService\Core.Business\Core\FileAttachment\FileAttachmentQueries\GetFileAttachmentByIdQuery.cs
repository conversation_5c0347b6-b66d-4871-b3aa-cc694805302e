﻿using System.Linq;
using Core.Data;
using Core.Shared;
using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.EntityFrameworkCore;
using System;

namespace Core.Business
{
    public class GetFileAttachmentByIdQuery : IRequest<FileAttachment>
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Lấy thông tin file đính kèm theo id
        /// </summary>
        /// <param name="id">Id trình độ đào tạo</param>
        public GetFileAttachmentByIdQuery(Guid id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetFileAttachmentByIdQuery, FileAttachment>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<FileAttachment> Handle(GetFileAttachmentByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                var entity = await _dataContext.FileAttachments.FirstOrDefaultAsync(x => x.Id == id, cancellationToken: cancellationToken);

                return entity;
            }
        }
    }
}
