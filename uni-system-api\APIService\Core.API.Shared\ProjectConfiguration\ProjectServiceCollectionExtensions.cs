using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Core.Business;
using Core.Data;
using Core.DataLog;
using Core.Shared;

namespace Core.API.Shared
{
    /// <summary>
    /// <see cref="IServiceCollection"/> extension methods add project services.
    /// </summary>
    /// <remarks>
    /// AddSingleton - Only one instance is ever created and returned.
    /// AddScoped - A new instance is created and returned for each request/response cycle.
    /// AddTransient - A new instance is created and returned each time.
    /// </remarks>
    public static class ProjectServiceCollectionExtensions
    {
        public static IServiceCollection RegisterMongoDBDataContextServiceComponents(this IServiceCollection services, IConfiguration configuration)
        {
            #region MongoDB
            // requires using Microsoft.Extensions.Options
            services.Configure<MongoDBDatabaseSettings>(
                configuration.GetSection(nameof(MongoDBDatabaseSettings)));

            services.AddSingleton<IMongoDBDatabaseSettings>(sp =>
                sp.GetRequiredService<IOptions<MongoDBDatabaseSettings>>().Value);
            #endregion

            return services;
        }
    }
}