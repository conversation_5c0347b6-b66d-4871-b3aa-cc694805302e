﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXuLy")]
    public class SvXuLy
    {

        public SvXuLy()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xu_ly")]
        public int IdXuLy { get; set; }

        [Column("ID_cap")]
        public int IdCap { get; set; }

        [Column("So_thang")]
        public int SoThang { get; set; }

        [Column("xu_ly"), MaxLength(50)]
        public string XuLy { get; set; }

        [Column("Diem_phat")]
        public float DiemPhat { get; set; }

        [Column("Muc_xu_ly")]
        public int MucXuLy { get; set; }


    }
}
