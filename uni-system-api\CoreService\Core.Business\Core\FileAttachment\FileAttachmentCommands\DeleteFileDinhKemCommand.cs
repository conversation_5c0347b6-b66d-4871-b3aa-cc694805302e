﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using Core.Shared.Enums;
using MediatR;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteFileDinhKemCommand : IRequest<Unit>
    {
        public Guid Id { get; set; }
        public bool IsRemoveFile { get; set; } = false;

        /// <summary>
        /// Xóa file đính kèm theo danh sách truyền vào
        /// </summary>
        /// <param name="id">id cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        /// <param name="isRemoveFile">Có xóa file vật lý hay không</param>
        public DeleteFileDinhKemCommand(Guid id, bool isRemoveFile = false)
        {
            Id = id;
            IsRemoveFile = isRemoveFile;
        }

        public class Handler : IRequestHandler<DeleteFileDinhKemCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IConfiguration _config;
            private readonly IWebHostEnvironment _webHostEnvironment;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IConfiguration config, IWebHostEnvironment webHostEnvironment, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _config = config;
                _webHostEnvironment = webHostEnvironment;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteFileDinhKemCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {FileAttachmentConstant.CachePrefix}: {id}");
                var entity = _dataContext.FileAttachments.FirstOrDefault(x => id == x.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.FileAttachments.Remove(entity);
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa file đính kèm: {id}",
                    ObjectCode = FileAttachmentConstant.CachePrefix,
                    ObjectId = id.ToString()
                });

                await _dataContext.SaveChangesAsync();

                if (request.IsRemoveFile)
                {
                    try
                    {
                        if (entity.Source == SourceFileEnums.MinIO.GetHashCode())
                        {
                            Log.Information("Delete file from MinIO");
                            var ms = new MinIOService(_config);
                            var fileName = Path.GetFileName(entity.ObjectName);
                            await ms.RemoveObjectAsync(entity.BucketName, entity.ObjectName);
                        }
                        else if (entity.Source == SourceFileEnums.Server.GetHashCode())
                        {
                            Log.Information($"Delete file from file storage: {entity.FilePath}");
                            var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, entity.FilePath);

                            if (File.Exists(fullFilePath))
                            {
                                File.Delete(fullFilePath);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, $"Delete file error: {ex.Message}");
                    }
                }

                return Unit.Value;
            }
        }
    }
}
