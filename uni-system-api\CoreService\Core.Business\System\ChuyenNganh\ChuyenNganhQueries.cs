﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxChuyenNganhQuery : IRequest<List<ChuyenNganhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy chuyên ngành cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxChuyenNganhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxChuyenNganhQuery, List<ChuyenNganhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChuyenNganhSelectItemModel>> Handle(GetComboboxChuyenNganhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChuyenNganhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvChuyenNganhs
                                select new ChuyenNganhSelectItemModel()
                                {
                                    IdChuyenNganh = dt.IdChuyenNganh,
                                    MaChuyenNganh = dt.MaChuyenNganh,
                                    ChuyenNganh = dt.ChuyenNganh,
                                    IdNganh = dt.IdNganh
                                }).Distinct() // Loại bỏ các bản ghi trùng lặp;
                                .OrderBy(x => x.ChuyenNganh);

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.ChuyenNganh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterChuyenNganhQuery : IRequest<PaginationList<ChuyenNganhBaseModel>>
    {
        public ChuyenNganhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách chuyên ngành có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChuyenNganhQuery(ChuyenNganhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChuyenNganhQuery, PaginationList<ChuyenNganhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChuyenNganhBaseModel>> Handle(GetFilterChuyenNganhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvChuyenNganhs
                            join ng in _dataContext.SvNganhs on dt.IdNganh equals ng.IdNganh
                            select new ChuyenNganhBaseModel
                            {
                                IdChuyenNganh = dt.IdChuyenNganh,
                                MaChuyenNganh = dt.MaChuyenNganh,
                                ChuyenNganh = dt.ChuyenNganh,
                                ChuyenNganhEn = dt.ChuyenNganhEn,
                                IdNganh = dt.IdNganh,
                                TenNganh = ng.TenNganh

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.ChuyenNganh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ChuyenNganhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetChuyenNganhByIdQuery : IRequest<ChuyenNganhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin chuyên ngành theo id
        /// </summary>
        /// <param name="id">Id chuyên ngành</param>
        public GetChuyenNganhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChuyenNganhByIdQuery, ChuyenNganhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChuyenNganhModel> Handle(GetChuyenNganhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ChuyenNganhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvChuyenNganhs.FirstOrDefaultAsync(x => x.IdChuyenNganh == id);

                    return AutoMapperUtils.AutoMap<SvChuyenNganh, ChuyenNganhModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetListRawChuyenNganhQuery : IRequest<List<ChuyenNganhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách chuyên ngành thô
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetListRawChuyenNganhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetListRawChuyenNganhQuery, List<ChuyenNganhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<ChuyenNganhSelectItemModel>> Handle(GetListRawChuyenNganhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                List<HeSelectItemModel> listHe = await _mediator.Send(new GetComboboxHeQuery());

                List<KhoaSelectItemModel> listKhoa = await _mediator.Send(new GetComboboxKhoaQuery());

                List<ChuyenNganhSelectItemModel> listChuyenNganh = await _mediator.Send(new GetComboboxChuyenNganhQuery());

                List<LopSelectItemModel> listLop = await _mediator.Send(new GetComboboxLopQuery());

                var list = (from cn in listChuyenNganh
                            join lop in listLop on cn.IdChuyenNganh equals lop.IdChuyenNganh
                            join he in listHe on lop.IdHe equals he.IdHe
                            join khoa in listKhoa on lop.IdKhoa equals khoa.IdKhoa
                            select new ChuyenNganhSelectItemModel()
                            {
                                IdChuyenNganh = cn.IdChuyenNganh,
                                MaChuyenNganh = cn.MaChuyenNganh,
                                ChuyenNganh = cn.ChuyenNganh,
                                IdNganh = cn.IdNganh,
                                IdHe = lop.IdHe,
                                IdKhoa = lop.IdKhoa,
                                KhoaHoc = lop.KhoaHoc
                            })
                                .OrderByDescending(x => x.ChuyenNganh)
                                .DistinctBy(x => new { x.ChuyenNganh, x.IdHe, x.IdKhoa })
                                .ToList();

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.ChuyenNganh.ToString().ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
