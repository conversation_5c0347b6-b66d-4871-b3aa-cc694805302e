﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/doi-tuong-hoc-bong")]
    [ApiExplorerSettings(GroupName = "28. Đối tượng học bổng")]
    [Authorize]
    public class DoiTuongHocBongController : ApiControllerBase
    {
        public DoiTuongHocBongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách đối tượng học bổng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<DoiTuongHocBongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxDoiTuongHocBongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách đối tượng học bổng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<DoiTuongHocBongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] DoiTuongHocBongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterDoiTuongHocBongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết đối tượng học bổng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<DoiTuongHocBongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetDoiTuongHocBongByIdQuery(id)));
        }


        /// <summary>
        /// Thêm mới đối tượng học bổng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateDoiTuongHocBongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_CREATE;


                return await _mediator.Send(new CreateDoiTuongHocBongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel đối tượng học bổng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyDoiTuongHocBongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_CREATE_MANY;


                return await _mediator.Send(new CreateManyDoiTuongHocBongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa đối tượng học bổng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDoiTuongHocBongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_UPDATE;
                return await _mediator.Send(new UpdateDoiTuongHocBongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa đối tượng học bổng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DOI_TUONG_HOC_BONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DOI_TUONG_HOC_BONG_DELETE;

                return await _mediator.Send(new DeleteDoiTuongHocBongCommand(id, u.SystemLog));
            });
        }

    }
}
