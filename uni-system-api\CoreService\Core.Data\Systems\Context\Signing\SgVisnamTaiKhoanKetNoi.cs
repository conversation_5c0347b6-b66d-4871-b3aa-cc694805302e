﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Shared;

namespace Core.Data.Signing;

[Table("sgVisnamTaiKhoanKetNoi")]
public class SgVisnamTaiKhoanKetNoi:BaseEntity
{
    [Column("user_id")]
    [Required]
    public int UserId { get; set; }

    [Column("key")]
    [Required]
    public string Key { get; set; }

    [Column("secret")]
    [Required]
    public string Secret { get; set; }
}