﻿using Core.API.Shared;
using Core.Business;
using Leader.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module Thống kê tài chính
    /// </summary>
    [ApiController]
    [Route("leader/v1/thong-ke-tai-chinh")]
    [ApiExplorerSettings(GroupName = "07. Thống kê tài chính")]
    [Authorize]
    public class ThongKeTaiChinhController : ApiControllerBase
    {
        public ThongKeTaiChinhController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách thống kê tài chính theo điều kiện lọc
        /// </summary> 
        /// <param name="filter"><PERSON><PERSON><PERSON><PERSON> kiện lọc</param>
        /// <returns><PERSON>h sách thống kê tài chính</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeTaiChinhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "THONG_KE_TAI_CHINH_VIEW")]
        public async Task<IActionResult> Filter([FromBody] ThongKeTaiChinhQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Lấy danh sách claims của người dùng từ http context
                string[] permission = HttpContext.User.Claims?.FirstOrDefault(c => c.Type == ClaimConstants.PERMISSTTIONS)?.Value?.Split(',');

                //Nếu người dùng không có quyền view all data thì cập nhật Id khoa về thành thông tin khoa hiện tại của người dùng
                if (!permission.Any(x => x == "VIEW_ALL_DATA"))
                {
                    var user = await _mediator.Send(new GetUserByIdQuery(u.UserId));
                    if (user != null)
                    {
                        filter.IDKhoa = user.IdKhoa;
                    }
                }

                return await _mediator.Send(new GetThongKeTaiChinhByFilterQuery(filter, u.SystemLog));
            });
        }

    }
}
