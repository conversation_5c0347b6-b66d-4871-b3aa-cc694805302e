﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Xóa nhật ký gửi mail
    /// </summary>
    /// <param name="id">Id nhật ký gửi mail</param>
    /// <returns>Danh sách kết quả xóa</returns>
    public class SendMailLogDeleteCommand : IRequest<Unit>
    {
        public string Id { get; set; }

        public SendMailLogDeleteCommand(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<SendMailLogDeleteCommand, Unit>
        {
            private readonly IMongoCollection<SendMailLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SendMailLog>(MongoCollections.SendMailLog);
                }
            }

            public async Task<Unit> Handle(SendMailLogDeleteCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<SendMailLog>.Filter.And(Builders<SendMailLog>.Filter.Where(p => request.Id == p.Id));

                    await _logs.DeleteManyAsync(builder).ConfigureAwait(false);
                }
                else
                {
                    int idLog = 0;
                    int.TryParse(request.Id, out idLog);
                    var dt = await _dataContext.SendMailLogs.FirstOrDefaultAsync(x => x.Id == idLog);
                    _dataContext.SendMailLogs.Remove(dt);
                    await _dataContext.SaveChangesAsync(cancellationToken);
                }
                return Unit.Value;
            }
        }
    }
}
