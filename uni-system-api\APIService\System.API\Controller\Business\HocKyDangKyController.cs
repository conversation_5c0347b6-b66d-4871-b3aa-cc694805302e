﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hoc-ky-dang-ky")]
    [ApiExplorerSettings(GroupName = "36. Học kỳ đăng ký")]
    [Authorize]
    public class HocKyDangKyController : ApiControllerBase
    {
        public HocKyDangKyController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách học kỳ đăng ký cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HocKyDangKySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHocKyDangKyQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách học kỳ đăng ký có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HocKyDangKyBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HocKyDangKyFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHocKyDangKyQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết học kỳ đăng ký
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HocKyDangKyModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocKyDangKyByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới học kỳ đăng ký
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHocKyDangKyModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_KY_DANG_KY_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_KY_DANG_KY_CREATE;


                return await _mediator.Send(new CreateHocKyDangKyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel học kỳ đăng ký
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHocKyDangKyModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_KY_DANG_KY_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_KY_DANG_KY_CREATE_MANY;


                return await _mediator.Send(new CreateManyHocKyDangKyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa học kỳ đăng ký
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHocKyDangKyModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_KY_DANG_KY_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_KY_DANG_KY_UPDATE;
                return await _mediator.Send(new UpdateHocKyDangKyCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa học kỳ đăng ký
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_KY_DANG_KY_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_KY_DANG_KY_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_KY_DANG_KY_DELETE;

                return await _mediator.Send(new DeleteHocKyDangKyCommand(id, u.SystemLog));
            });
        }

    }
}
