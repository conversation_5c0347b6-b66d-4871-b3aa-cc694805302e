﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class NoiThucTapSelectItemModel
    {
        public int IdNoiThucTap { get; set; }
        public string MaNoiThucTap { get; set; }
        public string NoiThucTap { get; set; }
    }

    public class NoiThucTapBaseModel
    {
        public int IdNoiThucTap { get; set; }
        public string MaNoiThucTap { get; set; }
        public string NoiThucTap { get; set; }
        public string DiaChithucTap { get; set; }
    }


    public class NoiThucTapModel : NoiThucTapBaseModel
    {

    }

    public class NoiThucTapFilterModel : BaseQueryFilterModel
    {
        public NoiThucTapFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNoiThucTap";
        }
    }

    public class CreateNoiThucTapModel
    {
        [Required(ErrorMessage = "NoiThucTap.IdNoiThucTap.NotRequire")]
        public int IdNoiThucTap { get; set; }

        [MaxLength(20, ErrorMessage = "NoiThucTap.MaNoiThucTap.MaxLength(20)")]
        [Required(ErrorMessage = "NoiThucTap.MaNoiThucTap.NotRequire")]
        public string MaNoiThucTap { get; set; }

        [MaxLength(200, ErrorMessage = "NoiThucTap.NoiThucTap.MaxLength(200)")]
        [Required(ErrorMessage = "NoiThucTap.NoiThucTap.NotRequire")]
        public string NoiThucTap { get; set; }

        [MaxLength(50, ErrorMessage = "NoiThucTap.DiaChithucTap.MaxLength(250)")]
        public string DiaChithucTap { get; set; }

    }

    public class CreateManyNoiThucTapModel
    {
        public List<CreateNoiThucTapModel> listNoiThucTapModels { get; set; }
    }

    public class UpdateNoiThucTapModel : CreateNoiThucTapModel
    {
        public void UpdateEntity(SvNoiThucTap input)
        {
            input.IdNoiThucTap = IdNoiThucTap;
            input.MaNoiThucTap = MaNoiThucTap;
            input.NoiThucTap = NoiThucTap;
            input.DiaChithucTap = DiaChithucTap;

        }
    }
}
