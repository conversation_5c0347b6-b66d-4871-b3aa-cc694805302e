﻿using Core.Shared;
using Serilog;
using System;
using System.Data;
using System.Data.SqlClient;

namespace Leader.Business
{
    public class LeaderCallStoreHelper : ILeaderCallStoreHelper
    {
        public DataTable CallStoreKhoanThuAsync(string idLopList, string hocKyList, string namHoc)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svDanhsachKhoanThu_TongHop_Load_List_All", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_lop_list", idLopList));
                cmd.Parameters.Add(new SqlParameter("@Hoc_ky_list", hocKyList));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreKhoanThuByConditionsAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, DateTime? tuNgay, DateTime? denNgay)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svBaoCaoLanhDao_ThongKeThuChiHocPhiTheoThoiGian", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_khoa", idKhoa));
                cmd.Parameters.Add(new SqlParameter("@Hoc_ky", hocKy));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@Tu_ngay", (object)tuNgay?.ToString("yyyy-MM-dd") ?? DBNull.Value));
                cmd.Parameters.Add(new SqlParameter("@Den_ngay", (object)denNgay?.ToString("yyyy-MM-dd") ?? DBNull.Value));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreDanhSachLopTinChiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svBaoCaoLanhDao_TraCuuDanhSachLopTinChi", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_khoa", idKhoa));
                cmd.Parameters.Add(new SqlParameter("@ID_nganh", idNganh));
                cmd.Parameters.Add(new SqlParameter("@Hoc_ky", hocKy));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@Khoa_hoc", khoaHoc));
                cmd.Parameters.Add(new SqlParameter("@Dot", dot));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreSoLuongLopTinChiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svBaoCaoLanhDao_ThongkeTongSoLopTinChi", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_khoa", idKhoa));
                cmd.Parameters.Add(new SqlParameter("@ID_nganh", idNganh));
                cmd.Parameters.Add(new SqlParameter("@Hoc_ky", hocKy));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@Khoa_hoc", khoaHoc));
                cmd.Parameters.Add(new SqlParameter("@Dot", dot));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreThongKeToChucThiAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc, int dot)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svBaoCaoLanhDao_TraCuuDanhSachToChucThi", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_khoa", idKhoa));
                cmd.Parameters.Add(new SqlParameter("@ID_nganh", idNganh));
                cmd.Parameters.Add(new SqlParameter("@Hoc_ky", hocKy));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@Khoa_hoc", khoaHoc));
                cmd.Parameters.Add(new SqlParameter("@Dot", dot));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreQuyMoSinhVienAsync(int idHe, int idKhoa, int idNganh, int hocKy, string namHoc, int khoaHoc)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_svBaoCaoLanhDao_ThongkeQuyMoSinhVien", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_khoa", idKhoa));
                cmd.Parameters.Add(new SqlParameter("@ID_nganh", idNganh));
                //cmd.Parameters.Add(new SqlParameter("@Hoc_ky", hocKy));
                //cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@Khoa_hoc", khoaHoc));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreThongKeNhapHocAsync(int idHe, int namHoc, int idNganh)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            var connectString = Utils.GetConfig("Database:Leader:ConnectionString:MSSQLDatabase");
            SqlConnection connection = new SqlConnection(connectString);
            try
            {
                cmd = new SqlCommand("sp_THongTinNhapHoc_Get", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@Nam_hoc", namHoc));
                cmd.Parameters.Add(new SqlParameter("@ID_nganh", idNganh));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }
    }
}
