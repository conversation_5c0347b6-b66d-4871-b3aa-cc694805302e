﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbBoMon")]
    public class TkbBoMon
    {

        public TkbBoMon()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_bm")]
        public int IdBoMon { get; set; }

        [Column("Ma_bo_mon"), MaxLength(10)]
        public string MaBoMon { get; set; }

        [Column("Bo_mon"), MaxLength(100)]
        public string <PERSON><PERSON>on { get; set; }

        [Column("So_nhom")]
        public int? SoNhom { get; set; }
    }
}
