﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxLoaiChungChiQuery : IRequest<List<LoaiChungChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiChungChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiChungChiQuery, List<LoaiChungChiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiChungChiSelectItemModel>> Handle(GetComboboxLoaiChungChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiChungChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiChungChis.OrderBy(x => x.LoaiChungChi)
                                select new LoaiChungChiSelectItemModel()
                                {
                                    IdChungChi = dt.IdChungChi,
                                    KyHieu = dt.KyHieu,
                                    LoaiChungChi = dt.LoaiChungChi,
                                    IdNhomChungChi = dt.IdNhomChungChi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.LoaiChungChi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiChungChiQuery : IRequest<PaginationList<LoaiChungChiBaseModel>>
    {
        public LoaiChungChiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại chứng chỉ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiChungChiQuery(LoaiChungChiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiChungChiQuery, PaginationList<LoaiChungChiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiChungChiBaseModel>> Handle(GetFilterLoaiChungChiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiChungChis
                            join ct in _dataContext.SvNhomChungChis on dt.IdNhomChungChi equals ct.IdNhomChungChi
                            select new LoaiChungChiBaseModel
                            {
                                IdChungChi = dt.IdChungChi,
                                KyHieu = dt.KyHieu,
                                LoaiChungChi = dt.LoaiChungChi,
                                IdNhomChungChi = dt.IdNhomChungChi,
                                CapDoChungChi = dt.CapDoChungChi,
                                NhomChungChi = ct.NhomChungChi
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.LoaiChungChi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiChungChiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiChungChiByIdQuery : IRequest<LoaiChungChiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại chứng chỉ theo id
        /// </summary>
        /// <param name="id">Id Xếp loại chứng chỉ</param>
        public GetLoaiChungChiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiChungChiByIdQuery, LoaiChungChiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiChungChiModel> Handle(GetLoaiChungChiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiChungChiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiChungChis.FirstOrDefaultAsync(x => x.IdChungChi == id);

                    return AutoMapperUtils.AutoMap<SvLoaiChungChi, LoaiChungChiModel>(entity);
                });
                return item;
            }
        }
    }
}
