﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXa")]
    public class SvXa
    {

        public SvXa()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xa"), MaxLength(6)]
        public string IdXa { get; set; }

        [Column("ID_huyen"), MaxLength(4)]
        public string IdHuyen { get; set; }

        [Column("Ten_xa"), <PERSON><PERSON>ength(30)]
        public string TenXa { get; set; }

        [Column("Ten_xa_en"), <PERSON><PERSON>ength(50)]
        public string TenXaEn { get; set; }


    }
}
