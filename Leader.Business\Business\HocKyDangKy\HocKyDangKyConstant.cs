﻿using Core.Shared;
using <PERSON>.Shared;

namespace Leader.Business
{
    public class HocKyDangKyConstant
    {
        public const string CachePrefix = LeaderCacheConstants.HOC_KY_DANG_KY;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
