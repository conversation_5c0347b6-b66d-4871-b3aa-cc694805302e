{"redis.test.label": "Check redis connection", "redis.test.current-time": "The present time", "redis.test.cache-time": "Cache time", "data.not-found": "Data does not exist", "message.error-500": "An error occurred", "message.error-500-minimum": "An error occurred, contact the system administrator for more details", "system-application.code.existed": "The application code already exists", "InsertData.Duplicate": "Newly added data is duplicated", "ChucDanh.Existed": "The title already exists", "ChucDanh.IdChucDanh.NotRequire": "IdChucDanh must be transmitted", "ChucDanh.MaChucDanh.MaxLength(10)": "Ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 10 characters", "ChucDanh.MaChucDanh.NotRequire": "MaChucDanh must be transmitted", "ChucDanh.ChucDanh.MaxLength(10)": "ChucDanh must not exceed 100 characters", "ChucDanh.ChucDanh.NotRequire": "ChucDanh must be transmitted", "GioiTinh.Existed": "Gender already exists", "GioiTinh.IdGioiTinh.NotRequire": "IdGioiTinh must be transmitted", "GioiTinh.GioiTinh.NotRequire": "GioiTinh must be transmitted", "GioiTinh.GioiTinh.MaxLength(3)": "GioiTinh must not exceed 3 characters", "He.Existed": "Training  already exists", "He.IdHe.NotRequire": "IdHe must be transmitted", "He.MaHe.MaxLength(50)": "<PERSON><PERSON><PERSON> must not exceed 50 characters", "He.MaHe.NotRequire": "MaHe must be transmitted", "He.TenHe.MaxLength(50)": "TenHe must not exceed 50 characters", "He.TenHe.NotRequire": "TenHe must be transmitted", "He.TenHeEn.MaxLength(50)": "TenHeEn must not exceed 50 characters", "He.TenBacDaoTao.MaxLength(50)": "TenBacDaoTao must not exceed 50 characters", "He.TenBacDaoTao.NotRequire": "TenBacDaoTao must be transmitted", "He.TenBacDaoTaoEn.MaxLength(50)": "TenBacDaoTaoEn must not exceed 5 characters", "He.TenBacDaoTaoEn.NotRequire": "TenBacDaoTaoEn must be transmitted", "He.HinhThucDaoTao.MaxLength(50)": "HinhThucDaoTao must not exceed 50 characters", "He.HinhThucDaoTao.NotRequire": "HinhThucDaoTao must be transmitted", "He.HinhThucDaoTaoEn.MaxLength(50)": "HinhThucDaoTaoEn must not exceed 50 characters", "He.HinhThucDaoTaoEn.NotRequire": "HinhThucDaoTaoEn must be transmitted", "He.SttTrinhDo.NotRequire": "SttTrinhDo must be transmitted", "HocHam.Existed": "Academic Rank  already exists", "HocHam.IdHocHam.NotRequire": "IdHocHam must be transmitted", "HocHam.MaHocHam.MaxLength(10)": "MaHocHam must not exceed 10 characters", "HocHam.MaHocHam.NotRequire": "MaHocHam must be transmitted", "HocHam.HocHam.MaxLength(100)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 100 characters", "HocHam.HocHam.NotRequire": "HocHam must be transmitted", "HocVi.Existed": "The degree already exists", "HocVi.IdHocVi.NotRequire": "IdHocVi must be transmitted", "HocVi.MaHocVi.MaxLength(10)": "MaHocVi must not exceed 10 characters", "HocVi.MaHocVi.NotRequire": "MaHocVi must be transmitted", "HocVi.HocVi.MaxLength(100)": "HocVi must not exceed 100 characters", "HocVi.HocVi.NotRequire": "HocVi must be transmitted", "QuocTich.Existed": "Nationality already exists", "QuocTich.IdQuocTich.NotRequire": "IdQuocTich must be transmitted", "QuocTich.MaQuocTich.MaxLength(10)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 10 characters", "QuocTich.MaQuocTich.NotRequire": "MaQuocTich must be transmitted", "QuocTich.QuocTich.MaxLength(50)": "<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 50 characters", "QuocTich.QuocTich.NotRequire": "QuocTich must be transmitted", "TonGiao.Existed": "Religion already exists", "TonGiao.IdTonGiao.NotRequire": "IdTonGiao must be transmitted", "TonGiao.MaTonGiao.MaxLength(5)": "MaTonGiao must not exceed 5 characters", "TonGiao.MaTonGiao.NotRequire": "MaTonGiao must be transmitted", "TonGiao.TonGiao.MaxLength(50)": "Ton<PERSON><PERSON>o must not exceed 50 characters", "TonGiao.TonGiao.NotRequire": "TonGiao must be transmitted", "TonGiao.TonGiaoEn.MaxLength(50)": "TonGiaoEn must not exceed 50 characters", "TonGiao.TonGiaoEn.NotRequire": "TonGiaoEn must be transmitted", "DanToc.Existed": "Ethnic already exists", "DanToc.IdDanToc.NotRequire": "IdDanToc must be transmitted", "DanToc.MaDanToc.MaxLength(5)": "MaDanToc must not exceed 5 characters", "DanToc.MaDanToc.NotRequire": "MaDanToc must be transmitted", "DanToc.DanToc.MaxLength(50)": "DanToc must not exceed 50 characters", "DanToc.DanToc.NotRequire": "DanToc must be transmitted", "DanToc.DanTocEn.MaxLength(50)": "DanTocEn must not exceed 50 characters", "DanToc.DanTocEn.NotRequire": "DanTocEn must be transmitted", "Khoa.Existed": "Department  already exists", "Khoa.IdKhoa.NotRequire": "IdKhoa must be transmitted", "Khoa.MaKhoa.MaxLength(5)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 5 characters", "Khoa.MaKhoa.NotRequire": "MaKhoa must be transmitted", "Khoa.TenKhoa.MaxLength(50)": "TenKhoa must not exceed 50 characters", "Khoa.TenKhoa.NotRequire": "TenKhoa must be transmitted", "Khoa.TenKhoaEn.MaxLength(50)": "TenKhoaEn must not exceed 50 characters", "Khoa.TEnKhoaEn.NotRequire": "TenKhoaEn must be transmitted", "Nganh.Existed": "Industry already exists", "Nganh.IdNganh.NotRequire": "IdNganh must be transmitted", "Nganh.MaNganh.MaxLength(20)": "Ma<PERSON><PERSON><PERSON> must not exceed 20 characters", "Nganh.MaNganh.NotRequire": "MaNganh must be transmitted", "Nganh.TenNganh.MaxLength(200)": "TenNganh must not exceed 200 characters", "Nganh.TenNganh.NotRequire": "TenNganh must be transmitted", "Nganh.TenNganhEn.MaxLength(200)": "TenNganhEn must not exceed 200 characters", "Nganh.TenNganhEn.NotRequire": "TenNganhEn must be transmitted", "Nganh.SuPham.NotRequire": "SuPham must be transmitted", "Huyen.Existed": "District already exists", "Huyen.IdHuyen.MaxLength(5)": "<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 5 characters", "Huyen.IdHuyen.NotRequire": "IdHuyen must be transmitted", "Huyen.IdTinh.MaxLength(5)": "Id<PERSON><PERSON><PERSON> must not exceed 5 characters", "Huyen.IdTinh.NotRequire": "IdTinh must be transmitted", "Huyen.TenHuyen.MaxLength(50)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 50 characters", "Huyen.TenHuyen.NotRequire": "TenHuyen must be transmitted", "Huyen.TenHuyenEn.MaxLength(50)": "Ten<PERSON>uyenEn must not exceed 50 characters", "Huyen.IdHuyenCu.MaxLength(5)": "Id<PERSON>uyen<PERSON><PERSON> must not exceed 5 characters", "Huyen.IdHuyenCu.NotRequire": "IdHuyenCu must be transmitted", "Huyen.IdHuyenCu1.MaxLength(5)": "IdHuyenCu1 must not exceed 5 characters", "Huyen.IdHuyenCu1.NotRequire": "IdHuyenCu1 must be transmitted", "Huyen.TenHuyenCu.MaxLength(50)": "TenHuyen<PERSON>u must not exceed 50 characters", "Tinh.Existed": "Province already exists", "Tinh.IdTinh.NotRequire": "IdTinh must be transmitted", "Tinh.MaTinh.MaxLength(5)": "Id<PERSON><PERSON><PERSON> must not exceed 5 characters", "Tinh.TenTinh.MaxLength(50)": "TenTinh must not exceed 50 characters", "Tinh.TenTinh.NotRequire": "TenTinh must be transmitted", "Tinh.TenTinhEn.MaxLength(50)": "TenTinhEn must not exceed 50 characters", "Tinh.TenTinhEn.NotRequire": "TenTinhEn must be transmitted", "ChuyenNganh.Existed": "Majors already exists", "ChuyenNganh.IdChuyenNganh.NotRequire": "IdChuyenNganh must be transmitted", "ChuyenNganh.MaChuyenNganh.MaxLength(20)": "MaChuyenNganh must not exceed 20 characters", "ChuyenNganh.MaChuyenNganh.NotRequire": "MaChuyenNganh must be transmitted", "ChuyenNganh.ChuyenNganh.MaxLength(200)": "ChuyenNganh must not exceed 200 characters", "ChuyenNganh.ChuyenNganh.NotRequire": "ChuyenNganh must be transmitted", "ChuyenNganh.ChuyenNganhEn.MaxLength(200)": "ChuyenNganhEn must not exceed 200 characters", "ChuyenNganh.ChuyenNganhEn.NotRequire": "ChuyenNganhEn must be transmitted", "ChuyenNganh.IdNganh.NotRequire": "IdNganh must be transmitted", "KhuVuc.Existed": "Area already exists", "KhuVuc.IdKhuVuc.NotRequire": "IdKhuVuc must be transmitted", "KhuVuc.MaKhuVuc.MaxLength(10)": "<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 10 characters", "KhuVuc.MaKhuVuc.NotRequire": "MaKhuVuc must be transmitted", "KhuVuc.TenKhuVuc.MaxLength(50)": "<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 50 characters", "KhuVuc.TenKhuVuc.NotRequire": "TenKhuVuc must be transmitted", "NhomDoiTuong.Existed": "Object group already exists", "NhomDoiTuong.IdNhomDoiTuong.NotRequire": "IdNhomDoiTuong must be transmitted", "NhomDoiTuong.MaNhom.MaxLength(5)": "<PERSON><PERSON><PERSON> must not exceed 5 characters", "NhomDoiTuong.MaNhom.NotRequire": "MaNhom must be transmitted", "NhomDoiTuong.TenNhom.MaxLength(100)": "TenNhom must not exceed 100 characters", "NhomDoiTuong.TenNhom.NotRequire": "TenNhom must be transmitted", "NhomDoiTuong.TenNhomEn.MaxLength(50)": "TenNhomEn must not exceed 50 characters", "DoiTuong.Existed": "Object already exists", "DoiTuong.IdDoiTuong.NotRequire": "IdDoiTuong must be transmitted", "DoiTuong.MaDoiTuong.MaxLength(5)": "Ma<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 5 characters", "DoiTuong.MaDoiTuong.NotRequire": "MaDoiTuong must be transmitted", "DoiTuong.TenDoiTuong.MaxLength(50)": "TenDoiTuong must not exceed 50 characters", "DoiTuong.TenDoiTuong.NotRequire": "TenDoiTuong must be transmitted", "DoiTuong.DoiTuongEn.NotRequire": "PhanTramMienGiam must be transmitted", "DoiTuongHocBong.Existed": "Scholarship object  already exists", "DoiTuongHocBong.IdDoiTuongHocBong.NotRequire": "IdDoiTuongHocBong must be transmitted", "DoiTuongHocBong.MaDoiTuongHocBong.MaxLength(5)": "MaDoiTuongHocBong must not exceed 5 characters", "DoiTuongHocBong.MaDoiTuongHocBong.NotRequire": "MaDoiTuongHocBong must be transmitted", "DoiTuongHocBong.DoiTuongHocBong.MaxLength(50)": "DoiTuongHocBong must not exceed 50 characters", "DoiTuongHocBong.DoiTuongHocBong.NotRequire": "DoiTuongHocBong must be transmitted", "DoiTuongHocBong.SoTienTroCap.NotRequire": "SoTienTroCap must be transmitted", "DoiTuongHocBong.PhanTramTroCap.NotRequire": "PhanTramTroCap must be transmitted", "CapKhenThuongKyLuat.Existed": "Level already exists", "CapKhenThuongKyLuat.IdCap.NotRequire": "IdCap must be transmitted", "CapKhenThuongKyLuat.MaCap.MaxLength(20)": "MaCap must not exceed 20 characters", "CapKhenThuongKyLuat.TenCap.MaxLength(50)": "TenCap must not exceed 50 characters", "CapKhenThuongKyLuat.TenCap.NotRequire": "TenCap must be transmitted", "LoaiKhenThuong.Existed": "Reward type already exists", "LoaiKhenThuong.IdLoaiKhenThuong.NotRequire": "IdLoaiKhenThuong must be transmitted", "LoaiKhenThuong.IdCap.NotRequire": "IdCap must be transmitted", "LoaiKhenThuong.LoaiKhenThuong.MaxLength(100)": "<PERSON>ai<PERSON><PERSON><PERSON> must not exceed 100 characters", "LoaiKhenThuong.LoaiKhenThuong.NotRequire": "LoaiKhenThuong must be transmitted", "LoaiKhenThuong.DiemThuong.NotRequire": "Diem<PERSON><PERSON>ong must be transmitted", "HanhVi.Existed": "Disciplinary behavior already exists", "HanhVi.IdHanhVi.NotRequire": "IdHanhVi must be transmitted", "HanhVi.MaHanhVi.MaxLength(5)": "MaHanhVi must not exceed 5 characters", "HanhVi.MaHanhVi.NotRequire": "MaHanhVi must be transmitted", "HanhVi.HanhVi.MaxLength(100)": "HanhVi must not exceed 100 characters", "HanhVi.HanhVi.NotRequire": "HanhVi must be transmitted", "XuLy.Existed": "Disciplinary action already exists", "XuLy.IdXuLy.NotRequire": "IdXuLy must be transmitted", "XuLy.IdCap.NotRequire": "IdCap must be transmitted", "XuLy.XuLy.MaxLength(50)": "<PERSON><PERSON><PERSON> must not exceed 50 characters", "XuLy.XuLy.NotRequire": "XuLy must be transmitted", "XuLy.SoThang.NotRequire": "SoThang must be transmitted", "XuLy.DiemPhat.NotRequire": "DiemPhat must be transmitted", "XuLy.MucXuLy.NotRequire": "MucXuLy must be transmitted", "HinhThucHoc.Existed": "Form of learning already exists", "HinhThucHoc.IdHinhThucHoc.NotRequire": "IdHinhThucHoc must be transmitted", "HinhThucHoc.MaHinhThucHoc.MaxLength(50)": "MaHinhThucHoc must not exceed 50 characters", "HinhThucHoc.MaHinhThucHoc.NotRequire": "MaHinhThucHoc must be transmitted", "HinhThucHoc.TenHinhThucHoc.MaxLength(50)": "TenHinhThucHoc must not exceed 50 characters", "HinhThucHoc.TenHinhThucHoc.NotRequire": "TenHinhThucHoc must be transmitted", "HinhThucHoc.GhiChu.MaxLength(200)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 200 characters", "ChucVu.Existed": "Position already exists", "ChucVu.IdChucVu.NotRequire": "IdChucVu must be transmitted", "ChucVu.MaChucVu.MaxLength(5)": "MaChucVu must not exceed 5 characters", "ChucVu.MaChucVu.NotRequire": "MaChucVu must be transmitted", "ChucVu.ChucVu.MaxLength(100)": "ChucVu must not exceed 100 characters", "ChucVu.ChucVu.NotRequire": "ChucVu must be transmitted", "HocKyDangKy.Existed": "Registration semester already exists", "HocKyDangKy.Dot.NotRequire": "Dot must be transmitted", "HocKyDangKy.HocKy.NotRequire": "HocKy must be transmitted", "HocKyDangKy.NamHoc.NotRequire": "NamHoc must be transmitted", "HocKyDangKy.NamHoc.MaxLength(10)": "NamHoc must not exceed 10 characters", "HocKyDangKy.TuNgay.NotRequire": "TuNgay must be transmitted", "HocKyDangKy.DenNgay.NotRequire": "DenNgay must be transmitted", "HocKyDangKy.ChonDangKy.NotRequire": "ChonDangKy must be transmitted", "HocKyDangKy.KhoaTkb.NotRequire": "KhoaTkb must be transmitted", "LoaiRenLuyen.Existed": "Type of training semester already exists", "LoaiRenLuyen.IdLoaiRenLuyen.NotRequire": "IdLoaiRenLuyen must be transmitted", "LoaiRenLuyen.IdCapRenLuyen.NotRequire": "IdCapRenLuyen must be transmitted", "LoaiRenLuyen.KyHieu.NotRequire": "KyHieu must be transmitted", "LoaiRenLuyen.KyHieu.MaxLength(10)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 10 characters", "LoaiRenLuyen.TenLoai.MaxLength(200)": "TenLoai must not exceed 200 characters", "LoaiRenLuyen.TenLoai.NotRequire": "TenLoai must be transmitted", "LoaiRenLuyen.Diem.NotRequire": "Diem must be transmitted", "XepLoaiRenLuyen.Existed": "Conduct rating semester already exists", "XepLoaiRenLuyen.IdXepLoai.NotRequire": "IdXepLoai must be transmitted", "XepLoaiRenLuyen.XepLoai.NotRequire": "XepLoai must be transmitted", "XepLoaiRenLuyen.XepLoai.MaxLength(50)": "XepLoai must not exceed 50 characters", "XepLoaiRenLuyen.XepLoaiEn.NotRequire": "XepLoaiEn must be transmitted", "XepLoaiRenLuyen.XepLoaiEn.MaxLength(50)": "XepLoaiEn must not exceed 50 characters", "XepLoaiRenLuyen.TuDiem.NotRequire": "TuDiem must be transmitted", "XepLoaiRenLuyen.DenDiem.NotRequire": "DenDiem must be transmitted", "XepLoaiRenLuyen.Heso.NotRequire": "Heso must be transmitted", "LoaiGiayTo.Existed": "Type of documents semester already exists", "LoaiGiayTo.IdGiayTo.NotRequire": "IdGiayTo must be transmitted", "LoaiGiayTo.MaGiayTo.NotRequire": "MaGiayTo must be transmitted", "LoaiGiayTo.MaGiayTo.MaxLength(5)": "MaGiayTo must not exceed 50 characters", "LoaiGiayTo.TenGiayTo.NotRequire": "TenGiayTo must be transmitted", "LoaiGiayTo.TenGiayTo.MaxLength(100)": "TenGiayTo must not exceed 50 characters", "LoaiGiayTo.Stt.NotRequire": "Stt must be transmitted", "LoaiGiayTo.BatBuoc.NotRequire": "BatBuoc must be transmitted", "LoaiGiayTo.MacDinh.NotRequire": "MacDinh must be transmitted", "LoaiGiayTo.Nhom.NotRequire": "Nhom must be transmitted", "LoaiGiayTo.GhiChu.NotRequire": "<PERSON><PERSON><PERSON><PERSON> must be transmitted", "LoaiGiayTo.GhiChu.MaxLength(150)": "<PERSON><PERSON><PERSON><PERSON> must not exceed 50 characters", "LoaiGiayTo.IdHe.NotRequire": "IdHe must be transmitted", "LoaiGiayTo.IdPhong.NotRequire": "IdPhong must be transmitted", "LoaiGiayTo.TuyenSinh.NotRequire": "TuyenSinh must be transmitted", "XepLoaiHocBong.Existed": "Scholarship classification semester already exists", "XepLoaiHocBong.IdXepLoaiHb.NotRequire": "IdXepLoaiHb must be transmitted", "XepLoaiHocBong.TenXepLoai.NotRequire": "TenXepLoai must be transmitted", "XepLoaiHocBong.TenXepLoai.MaxLength(50)": "TenXepLoai must not exceed 50 characters", "XepLoaiHocBong.TuDiemHt.NotRequire": "TuDiemHt must be transmitted", "XepLoaiHocBong.TuDiemRl.NotRequire": "TuDiemRl must be transmitted", "XepLoaiHocBong.IdHe.NotRequire": "IdHe must be transmitted", "XepLoaiHocBong.TuDiemHt4.NotRequire": "TuDiemHt4 must be transmitted", "XepLoaiHocBong.MaXepLoai.NotRequire": "MaXepLoai must be transmitted", "XepLoaiHocBong.MaXepLoai.MaxLength(50)": "MaXepLoai must not exceed 50 characters", "XepLoaiHocBong.SoTien.NotRequire": "SoTien must be transmitted", "Phuong.Existed": "Ward semester already exists", "Phuong.IdPhuong.NotRequire": "IdXepLoaiHb must be transmitted", "Phuong.TenPhuong.NotRequire": "TenXepLoai must be transmitted", "Phuong.TenPhuong.MaxLength(100)": "TenXepLoai must not exceed 100 characters", "Xa.Existed": "Religion already exists", "Xa.IdXa.MaxLength(5)": "IdXa must not exceed 6 characters", "Xa.IdXa.NotRequire": "IdXa must be transmitted", "Xa.IdHuyen.MaxLength(4)": "<PERSON><PERSON><PERSON><PERSON><PERSON> must not exceed 4 characters", "Xa.IdHuyen.NotRequire": "IdHuyen must be transmitted", "Xa.TenXa.MaxLength(30)": "TenXa must not exceed 30 characters", "Xa.TenXa.NotRequire": "TenXa must be transmitted", "Xa.TenXaEn.MaxLength(50)": "TenXaEn must not exceed 50 characters", "Xa.TenXaEn.NotRequire": "TenXaEn must be transmitted"}