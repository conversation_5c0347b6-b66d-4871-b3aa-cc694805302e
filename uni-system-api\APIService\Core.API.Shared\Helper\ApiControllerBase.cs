﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Security.Authentication;
using Core.Shared;
using Serilog;
using Core.Business;
using MediatR;
using Microsoft.Extensions.Localization;
using Minio.Exceptions;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using System.Threading;
using Serilog.Context;

namespace Core.API.Shared
{
    public class ApiControllerBase : ControllerBase
    {
        protected readonly IMediator _mediator;
        protected readonly IStringLocalizer<Resources> _localizer;
        protected readonly IConfiguration _config;
        private readonly bool showRequestDuration = false;

        public ApiControllerBase(
            IMediator mediator, 
            IStringLocalizer<Resources> localizer,
            IConfiguration config
            )
        {
            _mediator = mediator;
            _localizer = localizer;
            _config = config;
            showRequestDuration = _config["AppSettings:ShowRequestDuration"] == "true";
        }

        protected async Task<IActionResult> ExecuteFunction<T>(Func<RequestUser, Task<T>> func)
        {
            var timer = new Stopwatch();

            if (showRequestDuration)
                timer.Start();
            
            var currentUser = await GetCurrentUserInfo();
            LogContext.PushProperty("TraceIdentifier", currentUser.TraceId.ToString());

            try
            {
                var result = await func(currentUser);

                if (showRequestDuration)
                    timer.Stop();

                if (currentUser.SystemLog.ListAction.Any())
                {
                    currentUser.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(currentUser.SystemLog)).ConfigureAwait(false);
                }

                if (result == null)
                {
                    Log.Information($"Data return null");
                    return Helper.TransformData(new ResponseError(Code.NotFound, $"{_localizer["data.not-found"]}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }

                if (result is Response)
                {
                    var rs = result as Response;
                    rs.TraceId = currentUser.SystemLog.TraceId;
                    rs.RequestDuration = timer.ElapsedMilliseconds;
                    return Helper.TransformData(rs);
                }
                if (result is IActionResult)
                {
                    return (IActionResult)result;
                }
                return Helper.TransformData(new ResponseObject<T>(result) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ArgumentException agrEx)
            {
                if (showRequestDuration)
                    timer.Stop();
                Log.Information("{Error}", agrEx.ToString());

                return Helper.TransformData(new ResponseError(Code.BadRequest, agrEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (NullReferenceException nullEx)
            {
                Log.Debug("{Error}", nullEx.ToString());

                timer.Stop();
                return Helper.TransformData(new ResponseError(Code.NotFound, nullEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ForbiddenException forbiddenEx)
            {
                Log.Debug("{Error}", forbiddenEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Forbidden, forbiddenEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (AuthenticationException authEx)
            {
                Log.Debug("{Error}", authEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Unauthorized, authEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());
           
                if (showRequestDuration)
                    timer.Stop();
                if (_config["AppSettings:ReturnDetailError500Message"] == "true")
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500"]} {ex.Message}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
                else
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500-minimum"]}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
            }
        }

        protected async Task<IActionResult> ExecuteFunction<T>(Func<RequestUser, T> func)
        {
            var timer = new Stopwatch();
            if (showRequestDuration)
                timer.Start();

            var currentUser = await GetCurrentUserInfo();
            LogContext.PushProperty("TraceIdentifier", currentUser.TraceId.ToString());

            try
            {
                var result = func(currentUser);

                if (showRequestDuration)
                    timer.Stop();

                if (currentUser.SystemLog.ListAction.Any())
                {
                    currentUser.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(currentUser.SystemLog)).ConfigureAwait(false);
                }

                if (result == null)
                {
                    Log.Information($"Data return null");
                    return Helper.TransformData(new ResponseError(Code.NotFound, $"{_localizer["data.not-found"]}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }

                if (result is Response)
                {
                    var rs = result as Response;
                    rs.TraceId = currentUser.SystemLog.TraceId;
                    return Helper.TransformData(rs);
                }
                if (result is IActionResult)
                {
                    return (IActionResult)result;
                }
                return Helper.TransformData(new ResponseObject<T>(result) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ArgumentException agrEx)
            {
                if (showRequestDuration)
                    timer.Stop();
                Log.Information("{Error}", agrEx.ToString());

                return Helper.TransformData(new ResponseError(Code.BadRequest, agrEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (NullReferenceException nullEx)
            {
                Log.Debug("{Error}", nullEx.ToString());

                timer.Stop();
                return Helper.TransformData(new ResponseError(Code.NotFound, nullEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (ForbiddenException forbiddenEx)
            {
                Log.Debug("{Error}", forbiddenEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Forbidden, forbiddenEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (AuthenticationException authEx)
            {
                Log.Debug("{Error}", authEx.ToString());

                if (showRequestDuration)
                    timer.Stop();
                return Helper.TransformData(new ResponseError(Code.Unauthorized, authEx.Message) { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());

                if (showRequestDuration)
                    timer.Stop();
                if (_config["AppSettings:ReturnDetailError500Message"] == "true")
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500"]} {ex.Message}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
                else
                {
                    return Helper.TransformData(new ResponseError(Code.ServerError, $"{_localizer["message.error-500-minimum"]}") { TraceId = currentUser.SystemLog.TraceId, RequestDuration = timer.ElapsedMilliseconds });
                }
            }
        }

        private async Task<RequestUser> GetCurrentUserInfo()
        {
            var currentUser = await Helper.GetRequestInfo(HttpContext.Request);

            // Lấy thông tin người dùng đang truy cập để gán vào log
            var listUser = await _mediator.Send(new GetComboboxUserQuery());
            var user = listUser.FirstOrDefault(x => x.UserId == currentUser.UserId);
            currentUser.UserName = user?.UserName;
            currentUser.SystemLog.UserName = user?.UserName;
            currentUser.MaCB = user?.MaCB;

            // Kiểm tra xem điều kiện có lấy thông tin giáo viên trong middleware không
            if (_config["AppSettings:EnableLoadTeacherInfoInMiddleware"] == "true")
            {
                var listTeacher = await _mediator.Send(new GetComboboxTKBGiaoVienQuery());
                var teacher = listTeacher.FirstOrDefault(x => !string.IsNullOrEmpty(x.MaCB) && x.MaCB == user?.MaCB);
                currentUser.GiaoVienId = teacher?.Id;
            }

            return currentUser;
        }
    }
}
