﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>425b4f5f-9a2d-4859-8fc2-b0e291718a67</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <DockerfileContext>..\..</DockerfileContext>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CoreService\Core.Business\Core.Business.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.DataLog\Core.DataLog.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.Data\Core.Data.csproj" />
    <ProjectReference Include="..\..\CoreService\Core.Shared\Core.Shared.csproj" />
    <ProjectReference Include="..\Core.API.Shared\Core.API.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Include="wwwroot\Localization\en-US.json" />
    <None Include="wwwroot\Localization\vi-VN.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\templates\" />
  </ItemGroup>
</Project>