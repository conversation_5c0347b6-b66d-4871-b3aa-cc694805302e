﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateEmailTemplateCommand : IRequest<Unit>
    {
        public CreateEmailTemplateModel Model { get; set; }

        /// <summary>
        /// Thêm mới mẫu email
        /// </summary>
        /// <param name="model">Thông tin mẫu email cần thêm mới</param>
        public CreateEmailTemplateCommand(CreateEmailTemplateModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateEmailTemplateCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateEmailTemplateCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {EmailTemplateConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateEmailTemplateModel, EmailTemplate>(model);

                var checkCode = await _dataContext.EmailTemplates.AnyAsync(x => x.Code == entity.Code);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["system-application.code.existed"]}");
                }

                entity.CreatedUserId = _contextAccessor.UserId;
                entity.CreatedDate = DateTime.Now;

                await _dataContext.EmailTemplates.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {EmailTemplateConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới mẫu email mã: {entity.Code}",
                    ObjectCode = EmailTemplateConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(EmailTemplateConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
