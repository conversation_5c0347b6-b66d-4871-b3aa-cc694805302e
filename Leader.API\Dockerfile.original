#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["APIService/HRM.API/HRM.API.csproj", "APIService/HRM.API/"]
COPY ["CoreService/Core.Business/Core.Business.csproj", "CoreService/Core.Business/"]
COPY ["CoreService/Core.DataLog/Core.DataLog.csproj", "CoreService/Core.DataLog/"]
COPY ["CoreService/Core.Shared/Core.Shared.csproj", "CoreService/Core.Shared/"]
COPY ["CoreService/Core.Data/Core.Data.csproj", "CoreService/Core.Data/"]
RUN dotnet restore "APIService/HRM.API/HRM.API.csproj"
COPY . .
WORKDIR "/src/APIService/HRM.API"
RUN dotnet build "HRM.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "HRM.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "HRM.API.dll"]