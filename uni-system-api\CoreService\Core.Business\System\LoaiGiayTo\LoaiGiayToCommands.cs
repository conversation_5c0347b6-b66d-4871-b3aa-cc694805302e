﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiGiayToCommand : IRequest<Unit>
    {
        public CreateLoaiGiayToModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiGiayToCommand(CreateLoaiGiayToModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiGiayToCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiGiayToCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiGiayToConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiGiayToModel, SvLoaiGiayTo>(model);

                var checkCode = await _dataContext.SvLoaiGiayTos.AnyAsync(x => (x.TenGiayTo == entity.TenGiayTo && x.IdHe == entity.IdHe) || x.MaGiayTo == entity.MaGiayTo);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiGiayTo.Existed", entity.TenGiayTo.ToString()]}");
                }

                await _dataContext.SvLoaiGiayTos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiGiayToConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại giấy tờ: {entity.TenGiayTo}",
                    ObjectCode = LoaiGiayToConstant.CachePrefix,
                    ObjectId = entity.IdGiayTo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiGiayToCommand : IRequest<Unit>
    {
        public CreateManyLoaiGiayToModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiGiayToCommand(CreateManyLoaiGiayToModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiGiayToCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiGiayToCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiGiayToConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listLoaiGiayToAdd = model.listLoaiGiayToModels.Select(x => new { x.TenGiayTo, x.IdHe }).ToList();
                var listMaGiayToAdd = model.listLoaiGiayToModels.Select(x => x.MaGiayTo).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiGiayToModel, SvLoaiGiayTo>(model);

                // Check data duplicate
                if (listLoaiGiayToAdd.Count() != listLoaiGiayToAdd.Distinct().Count() || listMaGiayToAdd.Count() != listMaGiayToAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB

                for( var i = 0; i< listLoaiGiayToAdd.Count(); i++)
                {
                    if (await _dataContext.SvLoaiGiayTos.AnyAsync(x => (x.TenGiayTo == listLoaiGiayToAdd[i].TenGiayTo && x.IdHe == listLoaiGiayToAdd[i].IdHe) || listMaGiayToAdd.Contains(x.MaGiayTo)) )
                    {
                        throw new ArgumentException($"{_localizer["LoaiGiayTo.Existed"]}");
                    }
                }
               

                var listEntity = model.listLoaiGiayToModels.Select(x => new SvLoaiGiayTo()
                {
                    IdGiayTo = x.IdGiayTo,
                    MaGiayTo = x.MaGiayTo,
                    TenGiayTo = x.TenGiayTo,
                    Stt = x.Stt,
                    IdHe = x.IdHe,
                    IdPhong = x.IdPhong,
                    BatBuoc = x.BatBuoc,
                    MacDinh = x.MacDinh,
                    Nhom = x.Nhom,
                    GhiChu = x.GhiChu,
                    TuyenSinh = x.TuyenSinh


                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdGiayTo).ToList();

                Log.Information($"Create many {LoaiGiayToConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import loại giấy tờ từ file excel",
                    ObjectCode = LoaiGiayToConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiGiayToCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiGiayToModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiGiayToCommand(int id, UpdateLoaiGiayToModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiGiayToCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiGiayToCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiGiayToConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiGiayTos.FirstOrDefaultAsync(dt => dt.IdGiayTo == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvLoaiGiayTos.AnyAsync(x => ((x.TenGiayTo == model.TenGiayTo && x.IdHe == model.IdHe) || x.MaGiayTo == model.MaGiayTo) && x.IdGiayTo != model.IdGiayTo);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiGiayTo.Existed", model.TenGiayTo.ToString()]}");
                }

                Log.Information($"Before Update {LoaiGiayToConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiGiayTos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiGiayToConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiGiayToConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại giấy tờ: {entity.TenGiayTo}",
                    ObjectCode = LoaiGiayToConstant.CachePrefix,
                    ObjectId = entity.IdGiayTo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey(entity.IdGiayTo.ToString()));
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiGiayToCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiGiayToCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiGiayToCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiGiayToCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiGiayToConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiGiayTos.FirstOrDefaultAsync(x => x.IdGiayTo == id);

                _dataContext.SvLoaiGiayTos.Remove(entity);

                Log.Information($"Delete {LoaiGiayToConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại giấy tờ: {entity.TenGiayTo}",
                    ObjectCode = LoaiGiayToConstant.CachePrefix,
                    ObjectId = entity.IdGiayTo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey());
                _cacheService.Remove(LoaiGiayToConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
