﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business.System
{
    //public class GetPhanHeByIdQuery : IRequest<PhanHeModel>
    //{
    //    public int Id { get; set; }

    //    /// <summary>
    //    /// Lấy thông tin phân hệ theo id
    //    /// </summary>
    //    /// <param name="id">Id phân hệ</param>
    //    public GetPhanHeByIdQuery(int id)
    //    {
    //        Id = id;
    //    }

    //    public class Handler : IRequestHandler<GetPhanHeByIdQuery, PhanHeModel>
    //    {
    //        private readonly ReadDbContext _dataContext;
    //        private readonly ICacheService _cacheService;

    //        public Handler(ReadDbContext dataContext, ICacheService cacheService)
    //        {
    //            _dataContext = dataContext;
    //            _cacheService = cacheService;
    //        }

    //        public async Task<PhanHeModel> Handle(GetPhanHeByIdQuery request, CancellationToken cancellationToken)
    //        {
    //            var id = request.Id;

    //            string cacheKey = PhanHeConstant.BuildCacheKey(id.ToString());
    //            var item = await _cacheService.GetOrCreate(cacheKey, async () =>
    //            {
    //                var entity = await _dataContext.HtPhanHes.FirstOrDefaultAsync(x => x.IdPh == id);

    //                return AutoMapperUtils.AutoMap<HtPhanHe, PhanHeModel>(entity);
    //            });
    //            return item;
    //        }
    //    }
    //}

    public class GetComboboxPhanHeQuery : IRequest<List<PhanHeSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách phân hệ cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxPhanHeQuery(int count = 0, string textSearch = "")
        {
            Count = count;
            TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxPhanHeQuery, List<PhanHeSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<PhanHeSelectItemModel>> Handle(GetComboboxPhanHeQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                //string cacheKey = PhanHeConstant.BuildCacheKey();
                //var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                //{
                var entity = await _dataContext.HtPhanHes.Where(x => !x.Hiden).OrderBy(x => x.STT).ToListAsync(); ;

                var list = AutoMapperUtils.AutoMap<HtPhanHe, PhanHeSelectItemModel>(entity);

                //return list;
                //});

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.PhanHe.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetListPhanHeAuthorizedByUserIdQuery : IRequest<List<PhanHeModel>>
    {
        public int UserId { get; set; }

        /// <summary>
        /// Lấy danh sách phân hệ được phân quyền theo người dùng
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetListPhanHeAuthorizedByUserIdQuery(int userId)
        {
            UserId = userId;
        }

        public class Handler : IRequestHandler<GetListPhanHeAuthorizedByUserIdQuery, List<PhanHeModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<PhanHeModel>> Handle(GetListPhanHeAuthorizedByUserIdQuery request, CancellationToken cancellationToken)
            {
                var userId = request.UserId;

                string cacheKey = PhanHeConstant.BuildCacheKey(userId.ToString());

                // Tạm fix lấy ra tất cả danh sách phân hệ
                //var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                //{
                var entity = await _dataContext.HtPhanHes.Where(x => !x.Hiden).OrderBy(x => x.STT).ToListAsync(); ;

                return AutoMapperUtils.AutoMap<HtPhanHe, PhanHeModel>(entity);
                //});
                //return item;
            }
        }
    }

    public class GetPhanHeByMaQuery : IRequest<PhanHeSelectItemModel>
    {
        public string MaPhanHe { get; set; }

        /// <summary>
        /// Lấy id phân hệ theo mã
        /// </summary>
        /// <param name="maPhanHe">Mã phân hệ</param>
        public GetPhanHeByMaQuery(string maPhanHe)
        {
            MaPhanHe = maPhanHe;
        }

        public class Handler : IRequestHandler<GetPhanHeByMaQuery, PhanHeSelectItemModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<PhanHeSelectItemModel> Handle(GetPhanHeByMaQuery request, CancellationToken cancellationToken)
            {
                string maPhanHe = request.MaPhanHe;

                var query = new GetComboboxPhanHeQuery();
                List<PhanHeSelectItemModel> listPh = await _mediator.Send(query);
                PhanHeSelectItemModel phanHe = listPh.FirstOrDefault(x => x.MaPhanHe == maPhanHe);
                return phanHe;
            }
        }
    }
}
