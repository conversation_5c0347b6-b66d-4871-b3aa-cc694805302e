﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiChungChiCommand : IRequest<Unit>
    {
        public CreateLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiChungChiCommand(CreateLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiChungChiModel, SvLoaiChungChi>(model);

                var checkCode = await _dataContext.SvLoaiChungChis.AnyAsync(x => x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KyHieu.Existed", entity.KyHieu.ToString()]}");
                }

                await _dataContext.SvLoaiChungChis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp hạng học lực: {entity.LoaiChungChi}",
                    ObjectCode = LoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdChungChi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiChungChiCommand : IRequest<Unit>
    {
        public CreateManyLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiChungChiCommand(CreateManyLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listLoaiChungChiAdd = model.listLoaiChungChiModels.Select(x => x.KyHieu).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiChungChiModel, SvLoaiChungChi>(model);

                // Check data duplicate
                if (listLoaiChungChiAdd.Count() != listLoaiChungChiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvLoaiChungChis.AnyAsync(x => listLoaiChungChiAdd.Contains(x.KyHieu)))

                {
                    throw new ArgumentException($"{_localizer["KyHieu.Existed"]}");
                }


                var listEntity = model.listLoaiChungChiModels.Select(x => new SvLoaiChungChi()
                {
                    KyHieu = x.KyHieu,
                    LoaiChungChi = x.LoaiChungChi,
                    IdNhomChungChi = x.IdNhomChungChi,
                    CapDoChungChi = x.CapDoChungChi
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdChungChi ).ToList();

                Log.Information($"Create many {LoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp hạng học lực từ file excel",
                    ObjectCode = LoaiChungChiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiChungChiCommand(int id, UpdateLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiChungChis.FirstOrDefaultAsync(dt => dt.IdChungChi  == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvLoaiChungChis.AnyAsync(x => x.IdChungChi != entity.IdChungChi && x.KyHieu == model.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KyHieu.Existed", model.KyHieu.ToString()]}");
                }


                Log.Information($"Before Update {LoaiChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiChungChis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp hạng học lực: {entity.LoaiChungChi}",
                    ObjectCode = LoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdChungChi .ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey(entity.IdChungChi .ToString()));
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiChungChiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiChungChis.FirstOrDefaultAsync(x => x.IdChungChi  == id);

                _dataContext.SvLoaiChungChis.Remove(entity);

                Log.Information($"Delete {LoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp hạng học lực: {entity.LoaiChungChi}",
                    ObjectCode = LoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdChungChi .ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey());
                _cacheService.Remove(LoaiChungChiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
