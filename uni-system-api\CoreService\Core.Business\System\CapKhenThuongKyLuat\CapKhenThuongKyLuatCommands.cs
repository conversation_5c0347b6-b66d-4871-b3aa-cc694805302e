﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateCapKhenThuongKyLuatCommand : IRequest<Unit>
    {
        public CreateCapKhenThuongKyLuatModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateCapKhenThuongKyLuatCommand(CreateCapKhenThuongKyLuatModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateCapKhenThuongKyLuatCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateCapKhenThuongKyLuatCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {CapKhenThuongKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateCapKhenThuongKyLuatModel, SvCapKhenThuongKyLuat>(model);

                var checkCode = await _dataContext.SvCapKhenThuongKyLuats.AnyAsync(x => x.IdCap == entity.IdCap || x.TenCap == entity.TenCap || x.MaCap == entity.MaCap);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CapKhenThuongKyLuat.Existed", entity.TenCap.ToString()]}");
                }

                await _dataContext.SvCapKhenThuongKyLuats.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {CapKhenThuongKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới cấp khen thưởng kỷ luật: {entity.TenCap}",
                    ObjectCode = CapKhenThuongKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdCap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyCapKhenThuongKyLuatCommand : IRequest<Unit>
    {
        public CreateManyCapKhenThuongKyLuatModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyCapKhenThuongKyLuatCommand(CreateManyCapKhenThuongKyLuatModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyCapKhenThuongKyLuatCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyCapKhenThuongKyLuatCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {CapKhenThuongKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listCapKhenThuongKyLuatAdd = model.listCapKhenThuongKyLuatModels.Select(x => x.TenCap).ToList();
                var listMaCapKhenThuongKyLuatAdd = model.listCapKhenThuongKyLuatModels.Select(x => x.MaCap).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyCapKhenThuongKyLuatModel, SvCapKhenThuongKyLuat>(model);

                // Check data duplicate
                if (listCapKhenThuongKyLuatAdd.Count() != listCapKhenThuongKyLuatAdd.Distinct().Count() || listMaCapKhenThuongKyLuatAdd.Count() != listMaCapKhenThuongKyLuatAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvCapKhenThuongKyLuats.AnyAsync(x => listCapKhenThuongKyLuatAdd.Contains(x.TenCap)) || await _dataContext.SvCapKhenThuongKyLuats.AnyAsync(x => listMaCapKhenThuongKyLuatAdd.Contains(x.MaCap)))
                {
                    throw new ArgumentException($"{_localizer["CapKhenThuongKyLuat.Existed"]}");
                }

                var listEntity = model.listCapKhenThuongKyLuatModels.Select(x => new SvCapKhenThuongKyLuat()
                {
                    IdCap = x.IdCap,
                    MaCap = x.MaCap,
                    TenCap = x.TenCap
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdCap).ToList();

                Log.Information($"Create many {CapKhenThuongKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import cấp khen thưởng kỷ luật từ file excel",
                    ObjectCode = CapKhenThuongKyLuatConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateCapKhenThuongKyLuatCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateCapKhenThuongKyLuatModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateCapKhenThuongKyLuatCommand(int id, UpdateCapKhenThuongKyLuatModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateCapKhenThuongKyLuatCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateCapKhenThuongKyLuatCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {CapKhenThuongKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvCapKhenThuongKyLuats.FirstOrDefaultAsync(dt => dt.IdCap == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.SvCapKhenThuongKyLuats.AnyAsync(x => (x.TenCap == model.TenCap || x.MaCap == model.MaCap) && x.IdCap != model.IdCap);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CapKhenThuongKyLuat.Existed", model.TenCap.ToString()]}");
                }

                Log.Information($"Before Update {CapKhenThuongKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvCapKhenThuongKyLuats.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {CapKhenThuongKyLuatConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {CapKhenThuongKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật cấp khen thưởng kỷ luật: {entity.TenCap}",
                    ObjectCode = CapKhenThuongKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdCap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey(entity.IdCap.ToString()));
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteCapKhenThuongKyLuatCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteCapKhenThuongKyLuatCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteCapKhenThuongKyLuatCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteCapKhenThuongKyLuatCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {CapKhenThuongKyLuatConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvCapKhenThuongKyLuats.FirstOrDefaultAsync(x => x.IdCap == id);

                _dataContext.SvCapKhenThuongKyLuats.Remove(entity);

                Log.Information($"Delete {CapKhenThuongKyLuatConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa cấp khen thưởng kỷ luật: {entity.TenCap}",
                    ObjectCode = CapKhenThuongKyLuatConstant.CachePrefix,
                    ObjectId = entity.IdCap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey());
                _cacheService.Remove(CapKhenThuongKyLuatConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
