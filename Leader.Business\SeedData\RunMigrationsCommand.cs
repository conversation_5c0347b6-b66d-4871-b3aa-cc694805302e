﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business;
using Leader.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Leader.Data;
using Microsoft.Extensions.Logging;

namespace Leader.Business
{
    public class RunLeaderMigrationsCommand : IRequest<Unit>
    {
        public RunLeaderMigrationsCommand()
        {
        }

        public class Handler : IRequestHandler<RunLeaderMigrationsCommand, Unit>
        {
            private readonly LeaderDataContext _dataContext;
            private readonly ILogger<RunLeaderMigrationsCommand> _logger;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            private readonly IConfiguration _config;
            public Handler(LeaderDataContext dataContext, IMediator mediator, ICacheService cacheService, IConfiguration config, ILogger<RunLeaderMigrationsCommand> logger)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
                _config = config;
                _logger = logger;
            }

            public async Task<Unit> Handle(RunLeaderMigrationsCommand request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Bắt đầu chạy migration...");
                await _dataContext.Database.MigrateAsync();
                _logger.LogInformation("Migration hoàn tất!");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }
        }
    }
}