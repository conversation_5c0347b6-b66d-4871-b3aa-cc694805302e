﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxNoiThucTapQuery : IRequest<List<NoiThucTapSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y nơi thực tập cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxNoiThucTapQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxNoiThucTapQuery, List<NoiThucTapSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NoiThucTapSelectItemModel>> Handle(GetComboboxNoiThucTapQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = NoiThucTapConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvNoiThucTaps.OrderBy(x => x.NoiThucTap)
                                select new NoiThucTapSelectItemModel()
                                {
                                    IdNoiThucTap = dt.IdNoiThucTap,
                                    MaNoiThucTap = dt.MaNoiThucTap,
                                    NoiThucTap = dt.NoiThucTap
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.NoiThucTap.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterNoiThucTapQuery : IRequest<PaginationList<NoiThucTapBaseModel>>
    {
        public NoiThucTapFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách nơi thực tập có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterNoiThucTapQuery(NoiThucTapFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterNoiThucTapQuery, PaginationList<NoiThucTapBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<NoiThucTapBaseModel>> Handle(GetFilterNoiThucTapQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvNoiThucTaps
                            select new NoiThucTapBaseModel
                            {
                                IdNoiThucTap = dt.IdNoiThucTap,
                                MaNoiThucTap = dt.MaNoiThucTap,
                                NoiThucTap = dt.NoiThucTap,
                                DiaChithucTap = dt.DiaChithucTap

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.NoiThucTap.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<NoiThucTapBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetNoiThucTapByIdQuery : IRequest<NoiThucTapModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin nơi thực tập theo id
        /// </summary>
        /// <param name="id">Id nơi thực tập</param>
        public GetNoiThucTapByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetNoiThucTapByIdQuery, NoiThucTapModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<NoiThucTapModel> Handle(GetNoiThucTapByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = NoiThucTapConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvNoiThucTaps.FirstOrDefaultAsync(x => x.IdNoiThucTap == id);

                    return AutoMapperUtils.AutoMap<SvNoiThucTap, NoiThucTapModel>(entity);
                });
                return item;
            }
        }
    }
}
