﻿using Core.API.Shared;
using Leader.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Leader.API.Controller
{
    [ApiController]
    [Route("leader/v1/lop")]
    [ApiExplorerSettings(GroupName = "09. Lớp")]
    [Authorize]
    public class LopController : ApiControllerBase
    {
        public LopController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {

        }

        /// <summary>
        /// L<PERSON>y danh sách khóa học cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("khoa-hoc-for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<KhoaHocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxKhoaHocQuery(count, ts));
            });
        }
    }
}
