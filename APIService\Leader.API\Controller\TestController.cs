﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Serilog;
using System;
using System.Text;
using Core.Business.Core;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business;
using Nest;
using Core.Business.Service.CheckConnectionService;
using System.Linq;
using Microsoft.AspNetCore.Hosting;
using Core.Shared.ContextAccessor;
using Leader.Business.ThirdPartyBussiness.Hrm;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("leader/v1/test")]
    [ApiExplorerSettings(GroupName = "00. Test", IgnoreApi = false)]
    [AllowAnonymous]
    public class TestController : ApiControllerBaseV2
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public TestController(
            IWebHostEnvironment webHostEnvironment,
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator, 
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Test Connection
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("connection")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync()
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new ConnectionCheckQuery());
            });
        }

        /// <summary>
        /// Get Client info
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("get-client-info")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetClientInfo()
        {
            return await ExecuteFunction(async () =>
            {
                string clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var ip = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                var realIp = HttpContext.Request.Headers["X-Real-Ip"].FirstOrDefault();
                string userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                string requestMethod = HttpContext.Request.Method;
                string requestPath = HttpContext.Request.Path;

                var parser = UAParser.Parser.GetDefault();
                var clientInfo = parser.Parse(userAgent);

                // get all header
                var headers = HttpContext.Request.Headers;

                var rs = new
                {
                    ClientIP = Helper.GetIPAddress(HttpContext.Request),
                    RealIp = realIp ?? "",
                    ForwardedForIP = ip ?? "",
                    RemoteIpAddress = clientIp,
                    UserAgent = userAgent,
                    RequestMethod = requestMethod,
                    RequestPath = requestPath,
                    Os = clientInfo.OS.ToString(),
                    Browser = clientInfo.UA.ToString(),
                    ClientInfo = clientInfo.ToString(),
                    headers = headers,
                };

                return rs;
            });
        }
    }
}
