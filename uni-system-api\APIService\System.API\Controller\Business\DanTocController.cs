﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/dan-toc")]
    [ApiExplorerSettings(GroupName = "20. Dân tộc")]
    [Authorize]
    public class DanTocController : ApiControllerBase
    {
        public DanTocController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách dân tộc cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<DanTocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxDanTocQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách dân tộc có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<DanTocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] DanTocFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterDanTocQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết dân tộc
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<DanTocModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetDanTocByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới dân tộc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateDanTocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DAN_TOC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DAN_TOC_CREATE;


                return await _mediator.Send(new CreateDanTocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel dân tộc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyDanTocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DAN_TOC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DAN_TOC_CREATE_MANY;


                return await _mediator.Send(new CreateManyDanTocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa dân tộc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDanTocModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DAN_TOC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DAN_TOC_UPDATE;
                return await _mediator.Send(new UpdateDanTocCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa dân tộc
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DAN_TOC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DAN_TOC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DAN_TOC_DELETE;

                return await _mediator.Send(new DeleteDanTocCommand(id, u.SystemLog));
            });
        }

    }
}