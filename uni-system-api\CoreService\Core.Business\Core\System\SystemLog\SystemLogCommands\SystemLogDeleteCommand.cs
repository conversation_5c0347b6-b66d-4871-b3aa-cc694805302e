﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// <PERSON><PERSON>a nhật ký hệ thống
    /// </summary>
    /// <param name="listId">Danh sách Id nhật ký hệ thống</param>
    /// <returns>Danh sách kết quả xóa</returns>
    public class SystemLogDeleteCommand : IRequest<Unit>
    {
        public List<string> ListId { get; set; }

        public SystemLogDeleteCommand(List<string> listId)
        {
            ListId = listId;
        }

        public class Handler : IRequestHandler<SystemLogDeleteCommand, Unit>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<Unit> Handle(SystemLogDeleteCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<SystemLog>.Filter.And(Builders<SystemLog>.Filter.Where(p => request.ListId.Contains(p.Id)));

                    await _logs.DeleteManyAsync(builder).ConfigureAwait(false);
                }
                else
                {
                    var listId = AutoMapperUtils.AutoMap<string, int>(request.ListId);
                    // Không sử dụng MongoDB
                    var logs = await _dataContext.SystemLogs.Where(x => listId.Contains(x.Id)).ToListAsync(cancellationToken);
                    if (logs != null && logs.Count > 0)
                    {
                        _dataContext.SystemLogs.RemoveRange(logs);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }
                return Unit.Value;
            }
        }
    }
}
