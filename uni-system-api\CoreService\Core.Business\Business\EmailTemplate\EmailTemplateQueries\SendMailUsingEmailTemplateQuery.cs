﻿using Core.Data;
using Core.Shared;
using FluentEmail.Core.Models;
using FluentEmail.Core;
using MediatR;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;

namespace Core.Business
{
    public class SendMailUsingEmailTemplateQuery : IRequest<bool>
    {
        public SendMailUsingTemplateModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Gửi email sử dụng template
        /// </summary>
        /// <param name="model">Thông tin gửi mail</param>
        public SendMailUsingEmailTemplateQuery(SendMailUsingTemplateModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<SendMailUsingEmailTemplateQuery, bool>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IFluentEmail _fluentEmail;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IFluentEmail fluentEmail, IMediator mediator)
            {
                _dataContext = dataContext;
                _fluentEmail = fluentEmail;
                _mediator = mediator;
            }

            public async Task<bool> Handle(SendMailUsingEmailTemplateQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Sendmail {EmailTemplateConstant.CachePrefix} to: {JsonSerializer.Serialize(model.To)}");

                // Lấy dữ liệu từ DB
                //var emailTemplate = await _dataContext.EmailTemplates.AsNoTracking().FirstOrDefaultAsync(x => x.Code == model.EmailTemplateCode && x.IsActive);

                // Lấy dữ liệu từ cache
                var listSelectItem = await _mediator.Send(new GetComboboxEmailTemplateQuery());

                var templateSelectItem = listSelectItem.FirstOrDefault(x => x.Code == model.EmailTemplateCode);

                if (templateSelectItem == null)
                {
                    throw new ArgumentException($"Email template {model.EmailTemplateCode} not found");
                }

                var emailTemplate = await _mediator.Send(new GetEmailTemplateByIdQuery(templateSelectItem.Id));

                if (emailTemplate == null)
                {
                    throw new ArgumentException($"Email template {model.EmailTemplateCode} not found");
                }

                List<Address> toEmails = [];
                List<Address> ccEmails = [];
                List<Address> bccEmails = [];

                #region To Email
                if (model.To != null)
                    foreach (var item in model.To)
                    {
                        toEmails.Add(new Address(item));
                    }
                #endregion

                #region CC Email
                if (model.CC != null)
                    foreach (var item in model.CC)
                    {
                        ccEmails.Add(new Address(item));
                    }
                if (emailTemplate.CC != null)
                    foreach (var item in emailTemplate.CC)
                    {
                        ccEmails.Add(new Address(item));
                    }
                #endregion

                #region BCC Email
                if (model.BCC != null)
                    foreach (var item in model.BCC)
                    {
                        ccEmails.Add(new Address(item));
                    }
                if (emailTemplate.BCC != null)
                    foreach (var item in emailTemplate.BCC)
                    {
                        ccEmails.Add(new Address(item));
                    }
                #endregion

                if (toEmails.Count == 0)
                {
                    return true;
                }

                var sendMail = _fluentEmail.To(toEmails)
                    .Subject(emailTemplate.Subject)
                    .UsingTemplate(emailTemplate.Template, model.Data, true);

                if (ccEmails.Count > 0)
                    sendMail.CC(ccEmails);

                if (bccEmails.Count > 0)
                    sendMail.BCC(bccEmails);

                if (emailTemplate.IsHighPriority)
                {
                    sendMail.HighPriority();
                }

                if (model.Attachments != null && model.Attachments.Count > 0)
                {
                    sendMail.Attach(model.Attachments);
                }

                if (!string.IsNullOrEmpty(emailTemplate.FromEmail))
                {
                    sendMail.SetFrom(emailTemplate.FromEmail, emailTemplate.FromUser);
                }

                var mailLog = new DataLog.SendMailLog()
                {
                    EmailTemplateCode = emailTemplate.Code,
                    Subject = emailTemplate.Subject,
                    ToEmail = string.Join(";", toEmails.Select(x => x.EmailAddress).ToArray()),
                    CreatedDate = DateTime.Now,
                    TraceId = systemLog.TraceId,
                    EmailTemplateName = emailTemplate.Name
                };

                try
                {
                    var rs = await sendMail.SendAsync();
                    mailLog.SendMailStatus = rs.Successful ? SendMailStatus.Success.GetHashCode() : SendMailStatus.Error.GetHashCode();
                    mailLog.MessageId = rs.MessageId;
                    mailLog.ErrorMesssage = rs.ErrorMessages.Any() ? string.Join(";", rs.ErrorMessages) : string.Empty;
                }
                catch (Exception ex)
                {
                    mailLog.SendMailStatus = SendMailStatus.Error.GetHashCode();
                    mailLog.MessageId = string.Empty;
                    mailLog.ErrorMesssage = ex.Message;
                }

                // Thêm lịch sử gửi mail
                await _mediator.Send(new SendMailLogCreateCommand(mailLog));

                Log.Information($"Sendmail {EmailTemplateConstant.CachePrefix} completed: {mailLog.SendMailStatus == SendMailStatus.Success.GetHashCode()}");
                return mailLog.SendMailStatus == SendMailStatus.Success.GetHashCode();
            }
        }
    }
}
