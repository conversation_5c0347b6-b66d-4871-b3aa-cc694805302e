SELECT *
FROM sys_file_attachment hph;


-- UNISOFT_FULL6_DEV_HR.dbo.hrFileDinhKem definition

-- Drop table

-- DROP TABLE UNISOFT_FULL6_DEV_HR.dbo.hrFileDinhKem;

CREATE TABLE sys_file_attachment
(
	Id UNIQUEIDENTIFIER NOT NULL,
	BucketName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ObjectName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	FileName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	FilePath nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Length] bigint NULL,
	[Source] int NULL,
	CreateUserName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ModifyUserName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	CreateDate datetime2 NULL,
	ModifyDate datetime2 NULL,
	CONSTRAINT PK_sys_file_attachment PRIMARY KEY (Id)
);


CREATE TABLE sys_email_template
(
[id] [int] NOT NULL IDENTITY(1, 1),
[order] [int] NOT NULL,
[is_active] [bit] NOT NULL,
[description] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[created_date] [datetime2] NULL,
[created_user_id] [int] NULL,
[modified_date] [datetime2] NULL,
[modified_user_id] [int] NULL,
[code] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[name] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[from_email] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[from_user] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[is_high_priority] [bit] NOT NULL,
[cc] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[bcc] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[subject] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[template] [nvarchar] (max) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[sys_email_template] ADD CONSTRAINT [PK_sys_email_template] PRIMARY KEY CLUSTERED ([id]) ON [PRIMARY]
GO





















