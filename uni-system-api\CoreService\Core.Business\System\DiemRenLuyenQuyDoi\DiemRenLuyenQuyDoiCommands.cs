﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDiemRenLuyenQuyDoiCommand : IRequest<Unit>
    {
        public CreateDiemRenLuyenQuyDoiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDiemRenLuyenQuyDoiCommand(CreateDiemRenLuyenQuyDoiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDiemRenLuyenQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDiemRenLuyenQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DiemRenLuyenQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDiemRenLuyenQuyDoiModel, SvDiemRenLuyenQuyDoi>(model);

                var checkCode = await _dataContext.SvDiemRenLuyenQuyDois.AnyAsync(x =>  x.XepLoai == entity.XepLoai );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemRenLuyenQuyDoi.Existed", entity.XepLoai.ToString()]}");
                }

                await _dataContext.SvDiemRenLuyenQuyDois.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DiemRenLuyenQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới điểm rèn luyện cơ bản: {entity.XepLoai}",
                    ObjectCode = DiemRenLuyenQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDiemRenLuyenQuyDoiCommand : IRequest<Unit>
    {
        public CreateManyDiemRenLuyenQuyDoiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDiemRenLuyenQuyDoiCommand(CreateManyDiemRenLuyenQuyDoiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDiemRenLuyenQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDiemRenLuyenQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DiemRenLuyenQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDiemRenLuyenQuyDoiAdd = model.listDiemRenLuyenQuyDoiModels.Select(x => x.XepLoai).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDiemRenLuyenQuyDoiModel, SvDiemRenLuyenQuyDoi>(model);

                // Check data duplicate
                if (listDiemRenLuyenQuyDoiAdd.Count() != listDiemRenLuyenQuyDoiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvDiemRenLuyenQuyDois.AnyAsync(x => listDiemRenLuyenQuyDoiAdd.Contains(x.XepLoai)) )
                {
                    throw new ArgumentException($"{_localizer["DiemRenLuyenQuyDoi.Existed"]}");
                }

                var listEntity = model.listDiemRenLuyenQuyDoiModels.Select(x => new SvDiemRenLuyenQuyDoi()
                {

                    IdXepLoai = x.IdXepLoai,
                    XepLoai = x.XepLoai,
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    DiemCong10 = x.DiemCong10,
                    DiemCong4 = x.DiemCong4

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepLoai).ToList();

                Log.Information($"Create many {DiemRenLuyenQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import điểm rèn luyện cơ bản từ file excel",
                    ObjectCode = DiemRenLuyenQuyDoiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDiemRenLuyenQuyDoiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDiemRenLuyenQuyDoiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDiemRenLuyenQuyDoiCommand(int id, UpdateDiemRenLuyenQuyDoiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDiemRenLuyenQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDiemRenLuyenQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DiemRenLuyenQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDiemRenLuyenQuyDois.FirstOrDefaultAsync(dt => dt.IdXepLoai == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
             
                var checkCode = await _dataContext.SvDiemRenLuyenQuyDois.AnyAsync(x => x.XepLoai == model.XepLoai  && x.IdXepLoai != model.IdXepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemRenLuyenQuyDoi.Existed", model.XepLoai.ToString()]}");
                }

                Log.Information($"Before Update {DiemRenLuyenQuyDoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDiemRenLuyenQuyDois.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DiemRenLuyenQuyDoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DiemRenLuyenQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật điểm rèn luyện cơ bản: {entity.XepLoai}",
                    ObjectCode = DiemRenLuyenQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey(entity.IdXepLoai.ToString()));
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDiemRenLuyenQuyDoiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDiemRenLuyenQuyDoiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDiemRenLuyenQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDiemRenLuyenQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DiemRenLuyenQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDiemRenLuyenQuyDois.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                _dataContext.SvDiemRenLuyenQuyDois.Remove(entity);

                Log.Information($"Delete {DiemRenLuyenQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa điểm rèn luyện cơ bản: {entity.XepLoai}",
                    ObjectCode = DiemRenLuyenQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey());
                _cacheService.Remove(DiemRenLuyenQuyDoiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
