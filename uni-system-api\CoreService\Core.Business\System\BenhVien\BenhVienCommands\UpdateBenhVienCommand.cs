﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateBenhVienCommand : IRequest<Unit>
    {
        public UpdateBenhVienModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Cập nhật bệnh viện
        /// </summary>
        /// <param name="model">Thông tin bệnh viện cần cập nhật</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public UpdateBenhVienCommand(UpdateBenhVienModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<UpdateBenhVienCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateBenhVienCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Update {BenhVienConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SvBenhViens.FirstOrDefaultAsync(x => x.IdBenhVien == model.IdBenhVien);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {BenhVienConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvBenhViens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {BenhVienConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(BenhVienConstant.BuildCacheKey(entity.IdBenhVien.ToString()));
                _cacheService.Remove(BenhVienConstant.BuildCacheKey());

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập bệnh viện mã: {entity.MaBenhVien}",
                    ObjectCode = BenhVienConstant.CachePrefix,
                    ObjectId = entity.IdBenhVien.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
