﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LopSelectItemModel
    {
        public int IdLop { get; set; }
        public string TenLop { get; set; }
        public int KhoaHoc { get; set; }
        public string NienKhoa { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int IdChuyenNganh { get; set; }
        public int IdDt { get; set; }
        public int SoSv { get; set; }
        public bool RaTruong { get; set; }
        public int? IdPhong { get; set; }
        public int? CaHoc { get; set; }
    }

    public class LopBaseModel
    {
        public int IdLop { get; set; }
        public string TenLop { get; set; }
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public string TenNganh { get; set; }
        public string TenChuyenNganh { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int IdChuyenNganh { get; set; }
        public int KhoaHoc { get; set; }
        public string NienKhoa { get; set; }
        public int IdDt { get; set; }
        public int SoSv { get; set; }
        public bool RaTruong { get; set; }
        public int? IdPhong { get; set; }
        public int? CaHoc { get; set; }
    }


    public class LopModel : LopSelectItemModel
    {

    }

    public class LopFilterModel : BaseQueryFilterModel
    {
        public LopFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdLop";
        }
    }

    public class CreateLopModel
    {
        public string TenLop { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int IdChuyenNganh { get; set; }
        public int KhoaHoc { get; set; }
        public string NienKhoa { get; set; }
        public int IdDt { get; set; }
        public int SoSv { get; set; }
        public bool RaTruong { get; set; }
        public int? IdPhong { get; set; }
        public int? CaHoc { get; set; }
    }

    public class UpdateLopModel : CreateLopModel
    {
        public int IdLop { get; set; }
        public void UpdateEntity(SvLop input)
        {
        }
    }
}
