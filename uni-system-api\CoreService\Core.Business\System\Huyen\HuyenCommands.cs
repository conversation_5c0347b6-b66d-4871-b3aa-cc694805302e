﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateHuyenCommand : IRequest<Unit>
    {
        public CreateHuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHuyenCommand(CreateHuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHuyenModel, SvHuyen>(model);

                var checkCode = await _dataContext.SvHuyens.AnyAsync(x => x.IdHuyen == entity.IdHuyen || (x.TenHuyen == entity.TenHuyen && x.IdTinh == entity.IdTinh));
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Huyen.Existed"]}");
                }
                entity.TenHuyenCu = string.Empty;
                entity.IdHuyenCu = string.Empty;
                entity.IdHuyenCu1 = string.Empty;
                await _dataContext.SvHuyens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới huyện: {entity.TenHuyen}",
                    ObjectCode = HuyenConstant.CachePrefix,
                    ObjectId = entity.IdHuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHuyenCommand : IRequest<Unit>
    {
        public CreateManyHuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHuyenCommand(CreateManyHuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHuyenAdd = model.listHuyenModels.Select(x => new { x.TenHuyen, x.IdTinh }).ToList();
                var listIdHuyenAdd = model.listHuyenModels.Select(x => x.IdHuyen).ToList();


                // Check data duplicate
                if (listHuyenAdd.Count() != listHuyenAdd.Distinct().Count() || listIdHuyenAdd.Count() != listIdHuyenAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                for( var i = 0; i < listHuyenAdd.Count(); i++)
                {
                    if (await _dataContext.SvHuyens.AnyAsync(x => (x.TenHuyen == listHuyenAdd[i].TenHuyen && x.IdTinh == listHuyenAdd[i].IdTinh) || listIdHuyenAdd.Contains(x.IdHuyen)))
                    {
                        throw new ArgumentException($"{_localizer["Huyen.Existed"]}");
                    }
                }
               



                var listEntity = model.listHuyenModels.Select(x => new SvHuyen()
                {
                    IdHuyen = x.IdHuyen,
                    IdTinh = x.IdTinh,
                    TenHuyen = x.TenHuyen,
                    TenHuyenEn = x.TenHuyenEn,
                    IdHuyenCu = x.IdHuyenCu,
                    IdHuyenCu1 = x.IdHuyenCu1,
                    TenHuyenCu = x.TenHuyenCu,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHuyen).ToList();

                Log.Information($"Create many {HuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import huyện từ file excel",
                    ObjectCode = HuyenConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHuyenCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public UpdateHuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHuyenCommand(string id, UpdateHuyenModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                string id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvHuyens.FirstOrDefaultAsync(dt => dt.IdHuyen == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvHuyens.AnyAsync(x => x.TenHuyen == model.TenHuyen && x.IdTinh == model.IdTinh && x.IdHuyen != model.IdHuyen);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Huyen.Existed", model.TenHuyen.ToString()]}");
                }

                Log.Information($"Before Update {HuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvHuyens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật huyện: {entity.TenHuyen}",
                    ObjectCode = HuyenConstant.CachePrefix,
                    ObjectId = entity.IdHuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HuyenConstant.BuildCacheKey(entity.IdHuyen.ToString()));
                _cacheService.Remove(HuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHuyenCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHuyenCommand(string id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHuyenCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvHuyens.FirstOrDefaultAsync(x => x.IdHuyen == id);

                _dataContext.SvHuyens.Remove(entity);

                Log.Information($"Delete {HuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa huyện: {entity.TenHuyen}",
                    ObjectCode = HuyenConstant.CachePrefix,
                    ObjectId = entity.IdHuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HuyenConstant.BuildCacheKey());
                _cacheService.Remove(HuyenConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
