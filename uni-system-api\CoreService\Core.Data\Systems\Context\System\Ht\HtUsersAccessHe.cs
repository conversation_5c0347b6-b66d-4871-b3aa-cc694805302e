﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("htUsersAccessHe")]
    public class HtUsersAccessHe
    {
        [Column("ID")]
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("UserID")]
        public int UserId { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_khoa")]
        public int IdKhoa { get; set; }

        [Column("Khoa_hoc")]
        public int KhoaHoc { get; set; }

        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [Column("ID_lop")]
        public string IdLop { get; set; }
    }
}
