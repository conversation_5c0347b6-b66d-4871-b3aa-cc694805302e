﻿using MediatR;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business;
using Core.Data;
using Microsoft.EntityFrameworkCore;
using Leader.Shared;

namespace Leader.Business
{
    public class SeedDataCommand : IRequest<Unit>
    {
        public SystemLogModel SystemLog { get; set; }

        public SeedDataCommand(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<SeedDataCommand, Unit>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            public Handler(SystemReadDataContext dataContext, IMediator mediator, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(SeedDataCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"{request.SystemLog.TraceId} - Khởi tạo dữ liệu hệ thống");

                var leaderCode = Utils.GetConfig("PhanHe:Leader");
                int order = 0;

                // Thêm quyền của Leader
                if (!string.IsNullOrEmpty(leaderCode))
                {
                    var phanhe = await _dataContext.HtPhanHes.FirstOrDefaultAsync(x => x.PhanHe == leaderCode);
                    if (phanhe != null && phanhe.IdPh != 0)
                    {
                        var listPermissionInDB = await _dataContext.Permissions.Where(x => x.IdPhanHe == phanhe.IdPh).ToListAsync();
                        var listPermissionInConstant = LeaderPermissionsShared.PERMISSION_LEADER;
                        foreach (var item in listPermissionInConstant)
                        {
                            item.Order = order++;
                        }
                        var listPermissionNeedAdd = listPermissionInConstant.Where(x => !listPermissionInDB.Any(y => y.Code == x.Code)).ToList();
                        if (listPermissionNeedAdd.Any())
                        {
                            var listNeedAdd = AutoMapperUtils.AutoMap<PermissionSeedModel, Permission>(listPermissionNeedAdd);
                            foreach (var item in listNeedAdd)
                            {
                                item.IdPhanHe = phanhe.IdPh;
                                item.CreatedDate = DateTime.Now;
                            }
                            Log.Information($"{request.SystemLog.TraceId} - Thêm mới {listNeedAdd.Count} quyền LEADER vào hệ thống");
                            await _dataContext.Permissions.AddRangeAsync(listNeedAdd);
                        }
                    }
                }

                await _dataContext.SaveChangesAsync();
                Log.Information($"{request.SystemLog.TraceId} - Khởi tạo dữ liệu thành công");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }
        }
    }
}