﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDiemQuyDoiCommand : IRequest<Unit>
    {
        public CreateDiemQuyDoiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDiemQuyDoiCommand(CreateDiemQuyDoiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDiemQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDiemQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DiemQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDiemQuyDoiModel, SvDiemQuyDoi>(model);

                var checkCode = await _dataContext.SvDiemQuyDois.AnyAsync(x => x.TuHocKy == entity.TuHocKy && x.DenHocKy == entity.DenHocKy && x.TuNamHoc == entity.TuNamHoc && x.DenNamHoc == entity.DenNamHoc && x.XepLoai == entity.XepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemQuyDoi.Existed", entity.XepLoai.ToString()]}");
                }

                await _dataContext.SvDiemQuyDois.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DiemQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới điểm quy đổi: {entity.XepLoai}",
                    ObjectCode = DiemQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemQuyDoiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateDiemQuyDoiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDiemQuyDoiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDiemQuyDoiCommand(int id, UpdateDiemQuyDoiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDiemQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDiemQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DiemQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDiemQuyDois.FirstOrDefaultAsync(dt => dt.IdXepLoai == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvDiemQuyDois.AnyAsync(x => (x.TuHocKy == model.TuHocKy && x.DenHocKy == model.DenHocKy && x.TuNamHoc == model.TuNamHoc && x.DenNamHoc == model.DenNamHoc && x.XepLoai == model.XepLoai) && x.IdXepLoai != model.IdXepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemQuyDoi.Existed", model.XepLoai.ToString()]}");
                }

                Log.Information($"Before Update {DiemQuyDoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDiemQuyDois.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DiemQuyDoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DiemQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật điểm quy đổi: {entity.XepLoai}",
                    ObjectCode = DiemQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemQuyDoiConstant.BuildCacheKey(entity.IdXepLoai.ToString()));
                _cacheService.Remove(DiemQuyDoiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDiemQuyDoiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDiemQuyDoiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDiemQuyDoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDiemQuyDoiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DiemQuyDoiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDiemQuyDois.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                _dataContext.SvDiemQuyDois.Remove(entity);

                Log.Information($"Delete {DiemQuyDoiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa điểm quy đổi: {entity.XepLoai}",
                    ObjectCode = DiemQuyDoiConstant.CachePrefix,
                    ObjectId = entity.IdXepLoai.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemQuyDoiConstant.BuildCacheKey());
                _cacheService.Remove(DiemQuyDoiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
