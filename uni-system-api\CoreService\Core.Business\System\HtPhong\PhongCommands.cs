﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreatePhongCommand : IRequest<Unit>
    {
        public CreatePhongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreatePhongCommand(CreatePhongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreatePhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreatePhongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {PhongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreatePhongModel, HtPhong>(model);

                var checkCode = await _dataContext.HtPhongs.AnyAsync(x => x.IdPhong == entity.IdPhong || x.Phong == entity.Phong || x.MaPhong == entity.MaPhong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Phong.Existed", entity.Phong.ToString()]}");
                }

                await _dataContext.HtPhongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {PhongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phòng: {entity.Phong}",
                    ObjectCode = PhongConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyPhongCommand : IRequest<Unit>
    {
        public CreateManyPhongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyPhongCommand(CreateManyPhongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyPhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyPhongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {PhongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listPhongAdd = model.listPhongModels.Select(x => x.Phong).ToList();
                var listMaPhongAdd = model.listPhongModels.Select(x => x.MaPhong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyPhongModel, HtPhong>(model);

                // Check data duplicate
                if (listPhongAdd.Count() != listPhongAdd.Distinct().Count() || listMaPhongAdd.Count() != listMaPhongAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.HtPhongs.AnyAsync(x => listPhongAdd.Contains(x.Phong)) || await _dataContext.HtPhongs.AnyAsync(x => listMaPhongAdd.Contains(x.MaPhong)))
                {
                    throw new ArgumentException($"{_localizer["Phong.Existed"]}");
                }

                var listEntity = model.listPhongModels.Select(x => new HtPhong()
                {
                    IdPhong = x.IdPhong,
                    MaPhong = x.MaPhong,
                    Phong = x.Phong

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdPhong).ToList();

                Log.Information($"Create many {PhongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import phòng từ file excel",
                    ObjectCode = PhongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(PhongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdatePhongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdatePhongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdatePhongCommand(int id, UpdatePhongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdatePhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdatePhongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {PhongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.HtPhongs.FirstOrDefaultAsync(dt => dt.IdPhong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.HtPhongs.AnyAsync(x => (x.Phong == model.Phong || x.MaPhong == model.MaPhong) && x.IdPhong != model.IdPhong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Phong.Existed", model.Phong.ToString()]}");
                }

                Log.Information($"Before Update {PhongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.HtPhongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {PhongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {PhongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật phòng: {entity.Phong}",
                    ObjectCode = PhongConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongConstant.BuildCacheKey(entity.IdPhong.ToString()));
                _cacheService.Remove(PhongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeletePhongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeletePhongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeletePhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeletePhongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {PhongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.HtPhongs.FirstOrDefaultAsync(x => x.IdPhong == id);

                _dataContext.HtPhongs.Remove(entity);

                Log.Information($"Delete {PhongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phòng: {entity.Phong}",
                    ObjectCode = PhongConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongConstant.BuildCacheKey());
                _cacheService.Remove(PhongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
