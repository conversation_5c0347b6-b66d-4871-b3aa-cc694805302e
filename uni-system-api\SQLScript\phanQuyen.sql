﻿CREATE TABLE sys_permission(
	[id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[group_name] [nvarchar](64) NOT NULL,
	[code] [nvarchar](64) NOT NULL,
	[name] [nvarchar](128) NOT NULL,
	[id_phan_he] [int] NOT NULL,
	[order] [int] NOT NULL,
	[is_active] [bit] NOT NULL,
	[description] [nvarchar](max) NULL,
	[created_date] [datetime2](7) NULL,
	[created_user_id] [int] NULL,
	[modified_date] [datetime2](7) NULL,
	[modified_user_id] [int] NULL
); 

CREATE TABLE sys_role_map_permission(
	[id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[role_id] [int] NOT NULL,
	[permission_id] [int] NOT NULL
);

CREATE TABLE sys_user_map_role(
	[id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[role_id] [int] NOT NULL,
	[user_id] [INT] NOT NULL
);
