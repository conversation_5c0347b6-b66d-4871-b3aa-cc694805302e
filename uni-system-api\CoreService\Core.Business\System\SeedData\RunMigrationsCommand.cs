﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Core.Business
{
    public class RunSystemMigrationsCommand : IRequest<Unit>
    {
        public RunSystemMigrationsCommand()
        {
        }

        public class Handler : IRequestHandler<RunSystemMigrationsCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ILogger<RunSystemMigrationsCommand> _logger;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, IMediator mediator, ICacheService cacheService, IConfiguration config, ILogger<RunSystemMigrationsCommand> logger)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
                _config = config;
                _logger = logger;
            }

            public async Task<Unit> Handle(RunSystemMigrationsCommand request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Bắt đầu chạy migration...");
                await _dataContext.Database.MigrateAsync();
                _logger.LogInformation("Migration hoàn tất!");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }
        }
    }
}