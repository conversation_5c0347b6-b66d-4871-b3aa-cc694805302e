﻿using System.Threading.Tasks;

namespace Core.Business.Core
{
    public interface ISendMailHandler
    {
        /// <summary>
        /// Gửi mail sử dụng acc google
        /// </summary>
        /// <param name="model"></param>
        void SendMailGoogle(SendMailModel model);

        /// <summary>
        /// Gửi mail vào queue (push message mail vào queue)
        /// </summary>
        /// <param name="model"></param>
        void SendMailQueue(SendMailModel model);

        /// <summary>
        /// Gửi mail sử dụng thư viện FluentEmail
        /// </summary>
        /// <param name="emailMetadata">Thông tin email</param>
        /// <returns></returns>
        Task SendMailFluentEmail(EmailMetadata emailMetadata);
        
        /// <summary>
        /// Gửi mail sử dụng template
        /// </summary>
        /// <param name="emailMetadata"></param>
        /// <param name="template"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        Task SendMailUsingTemplate(EmailMetadata emailMetadata, string template, object data);
    }
}
