﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiChungChiSelectItemModel
    {
        public int IdChungChi { get; set; }
        public string KyHieu { get; set; }
        public string LoaiChungChi { get; set; }
        public float IdNhomChungChi { get; set; }

    }

    public class LoaiChungChiBaseModel
    {
        public int IdChungChi { get; set; }
        public string KyHieu { get; set; }
        public string LoaiChungChi { get; set; }
        public float IdNhomChungChi { get; set; }
        public int? CapDoChungChi { get; set; }
        public string NhomChungChi { get; set; }
    }


    public class LoaiChungChiModel : LoaiChungChiBaseModel
    {
      
    }

    public class LoaiChungChiFilterModel : BaseQueryFilterModel
    {
        public LoaiChungChiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdChungChi";
        }
    }

    public class CreateLoaiChungChiModel
    {
        [MaxLength(20, ErrorMessage = "LoaiChungChi.KyHieu.MaxLength(20)")]
        [Required(ErrorMessage = "LoaiChungChi.KyHieu.NotRequire")]
        public string KyHieu { get; set; }

        [MaxLength(200, ErrorMessage = "LoaiChungChi.LoaiChungChi.MaxLength(200)")]
        [Required(ErrorMessage = "LoaiChungChi.LoaiChungChi.NotRequire")]
        public string LoaiChungChi { get; set; }

        [Required(ErrorMessage = "LoaiChungChi.IdNhomChungChi.NotRequire")]
        public float IdNhomChungChi { get; set; }

        [Required(ErrorMessage = "LoaiChungChi.CapDoChungChi.NotRequire")]
        public int CapDoChungChi { get; set; }
    }

    public class CreateManyLoaiChungChiModel
    {
        public List<CreateLoaiChungChiModel> listLoaiChungChiModels { get; set; }
    }

    public class UpdateLoaiChungChiModel : CreateLoaiChungChiModel
    {
        public void UpdateEntity(SvLoaiChungChi input)
        {
            input.KyHieu = KyHieu;
            input.LoaiChungChi = LoaiChungChi;
            input.IdNhomChungChi = IdNhomChungChi;
            input.CapDoChungChi = CapDoChungChi;
        }
    }
}
