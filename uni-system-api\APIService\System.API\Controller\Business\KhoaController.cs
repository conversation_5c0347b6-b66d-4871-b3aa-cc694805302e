﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/khoa")]
    [ApiExplorerSettings(GroupName = "12. Khoa")]
    [Authorize]
    public class KhoaController : ApiControllerBase
    {
        public KhoaController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Lấy danh sách khoa cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<KhoaSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxKhoaQuery(count, ts));
            });
        }


        /// <summary>
        /// Lấy danh sách khoa có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<KhoaBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_VIEW))]
        public async Task<IActionResult> Filter([FromBody] KhoaFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterKhoaQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết khoa
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<KhoaModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetKhoaByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới khoa
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateKhoaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHOA_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHOA_CREATE;


                return await _mediator.Send(new CreateKhoaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel khoa
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyKhoaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHOA_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHOA_CREATE_MANY;


                return await _mediator.Send(new CreateManyKhoaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa khoa
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateKhoaModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHOA_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHOA_UPDATE;
                return await _mediator.Send(new UpdateKhoaCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa khoa
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHOA_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHOA_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHOA_DELETE;

                return await _mediator.Send(new DeleteKhoaCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách khoa thô
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("list-khoa-map-he")]
        [ProducesResponseType(typeof(ResponseObject<List<KhoaSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListRaw(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetListRawKhoaQuery(count, ts));
            });
        }
    }

}
