﻿using Core.Business;
using Core.Shared;
using Leader.Data;
using System.ComponentModel.DataAnnotations;

namespace Leader.Business
{
    public class KhBacDaoTaoBaseModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "KhBacDaoTao.code.required")]
        public string Code { get; set; }

        [Required(ErrorMessage = "KhBacDaoTao.name.required")]
        public string Name { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }
    }

    public class KhBacDaoTaoModel : KhBacDaoTaoBaseModel
    {

    }

    public class CreateKhBacDaoTaoModel : KhBacDaoTaoModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateKhBacDaoTaoModel : KhBacDaoTaoModel
    {
        public void UpdateEntity(KhBacDaoTao entity)
        {
            entity.Name = this.Name;
            entity.IsActive = this.IsActive;
            entity.Order = this.Order;
        }
    }

    public class KhBacDaoTaoSelectItemModel : SelectItemModel
    {
    }

    public class KhBacDaoTaoQueryFilter : BaseQueryFilterModel
    {
    }

    public class SyncKhBacDaoTaoModel : KhBacDaoTaoBaseModel
    {
        public void UpdateEntity(KhBacDaoTao entity)
        {
            entity.Name = this.Name;
        }
    }
}
