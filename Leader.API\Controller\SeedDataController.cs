﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Serilog;
using System;
using System.Text;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Leader.Business;
using Core.Business.Core;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("leader/v1/seed-data")]
    [ApiExplorerSettings(GroupName = "101. Seed Data", IgnoreApi = false)]
    [AllowAnonymous]
    public class SeedDataController : ApiControllerBase
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IRedisHandler _redisHandler;
        private readonly IStringLocalizer<Resources> _localizer;
        public SeedDataController(IDistributedCache distributedCache, IRedisH<PERSON><PERSON> redisHandler, IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {
            _distributedCache = distributedCache;
            _localizer = localizer;
            _redisHandler = redisHandler;
        }

        /// <summary>
        /// DeleteAsync
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("seed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SeedDataCommand(u.SystemLog));
            });
        }
    }
}
