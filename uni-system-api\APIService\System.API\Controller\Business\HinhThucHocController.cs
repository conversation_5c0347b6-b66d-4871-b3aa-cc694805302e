﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hinh-thuc-hoc")]
    [ApiExplorerSettings(GroupName = "34. Hình thức học")]
    [Authorize]
    public class HinhThucHocController : ApiControllerBase
    {
        public HinhThucHocController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách hình thức học cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HinhThucHocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHinhThucHocQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách hình thức học có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HinhThucHocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HinhThucHocFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHinhThucHocQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết hình thức học
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HinhThucHocModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHinhThucHocByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới hình thức học
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHinhThucHocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_HOC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_HOC_CREATE;


                return await _mediator.Send(new CreateHinhThucHocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel hình thức học
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHinhThucHocModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_HOC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_HOC_CREATE_MANY;


                return await _mediator.Send(new CreateManyHinhThucHocCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa hình thức học
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHinhThucHocModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_HOC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_HOC_UPDATE;
                return await _mediator.Send(new UpdateHinhThucHocCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa hình thức học
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_HOC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_HOC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_HOC_DELETE;

                return await _mediator.Send(new DeleteHinhThucHocCommand(id, u.SystemLog));
            });
        }

    }
}
