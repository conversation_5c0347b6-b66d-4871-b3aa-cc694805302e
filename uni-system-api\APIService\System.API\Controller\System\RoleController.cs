﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/role")]
    [ApiExplorerSettings(GroupName = "06. Role (Nhóm người dùng)")]
    [Authorize]
    public class RoleController : ApiControllerBase
    {
        public RoleController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin nhóm người dùng</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateRoleModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_ROLE_CREATE);
                u.SystemLog.ActionName = LogConstants.ACTION_ROLE_CREATE;


                model.CreatedUserId = u.UserId;
                return await _mediator.Send(new CreateRoleCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy nhóm người dùng theo id
        /// </summary>
        /// <param name="id">Id nhóm người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<RoleModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetRoleByIdQuery(id));
            });
        }

        /// <summary>
        /// Cập nhật nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin nhóm người dùng cần cập nhật</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateRoleModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_ROLE_UPDATE);
                u.SystemLog.ActionName = LogConstants.ACTION_ROLE_UPDATE;


                model.Id = id;
                model.ModifiedUserId = u.UserId;
                return await _mediator.Send(new UpdateRoleCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa nhóm người dùng theo danh sách truyền vào
        /// </summary>
        /// <param name="id">id nhóm người dùng cần xóa</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_ROLE_DELETE);
                u.SystemLog.ActionName = LogConstants.ACTION_ROLE_DELETE;


                return await _mediator.Send(new DeleteRoleCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách nhóm người dùng theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<RoleBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] RoleQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetFilterRoleQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách nhóm người dùng cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<RoleSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var result = await _mediator.Send(new GetComboboxRoleQuery(count, ts));

                return result;
            });
        }
        #endregion

        #region Advance
        /// <summary>
        /// Lấy danh sách người dùng theo nhóm người dùng
        /// </summary>
        /// <param name="roleId">Id nhóm người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("get-user-by-role")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserOfRole([FromQuery] int roleId)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetUserOfRoleQuery(roleId));
            });
        }

        /// <summary>
        /// Lấy danh sách quyền theo nhóm người dùng
        /// </summary>
        /// <param name="roleId">Id nhóm người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("get-permission-by-role")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPermissionOfRoleQuery([FromQuery] int roleId)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetPermissionOfRoleQuery(roleId));
            });
        }

        /// <summary>
        /// Cập nhật người dùng thuộc nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPost, Route("update-user-by-role")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_UPDATE_USER))]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUserOfRoleCommand([FromBody] UserOfRoleCreateModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_ROLE_UPDATE_USER);
                u.SystemLog.ActionName = LogConstants.ACTION_ROLE_UPDATE_USER;


                return await _mediator.Send(new UpdateUserOfRoleCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Cập nhật quyền chức năng thuộc nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPost, Route("update-permission-by-role")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHOM_NGUOI_DUNG_UPDATE_PERMISSION))]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdatePermissionOfRoleCommand([FromBody] PermissionOfRoleCreateModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_ROLE_UPDATE_PERMISSION);
                u.SystemLog.ActionName = LogConstants.ACTION_ROLE_UPDATE_PERMISSION;

                return await _mediator.Send(new UpdateRoleMapPermissionCommand(model, u.SystemLog));
            });
        }
        #endregion
    }
}
