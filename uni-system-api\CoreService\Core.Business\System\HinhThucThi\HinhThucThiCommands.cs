﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateHinhThucThiCommand : IRequest<Unit>
    {
        public CreateHinhThucThiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHinhThucThiCommand(CreateHinhThucThiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHinhThucThiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHinhThucThiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HinhThucThiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHinhThucThiModel, SvHinhThucThi>(model);

                var checkCode = await _dataContext.SvHinhThucThis.AnyAsync(x => x.IdHinhThucThi == entity.IdHinhThucThi || x.HinhThucThi == entity.HinhThucThi || x.MaHinhThucThi == entity.MaHinhThucThi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HinhThucThi.Existed", entity.HinhThucThi.ToString()]}");
                }

                await _dataContext.SvHinhThucThis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HinhThucThiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới hình thức thi: {entity.HinhThucThi}",
                    ObjectCode = HinhThucThiConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucThi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHinhThucThiCommand : IRequest<Unit>
    {
        public CreateManyHinhThucThiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHinhThucThiCommand(CreateManyHinhThucThiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHinhThucThiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHinhThucThiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HinhThucThiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHinhThucThiAdd = model.listHinhThucThiModels.Select(x => x.HinhThucThi).ToList();
                var listMaHinhThucThiAdd = model.listHinhThucThiModels.Select(x => x.MaHinhThucThi).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyHinhThucThiModel, SvHinhThucThi>(model);

                // Check data duplicate
                if (listHinhThucThiAdd.Count() != listHinhThucThiAdd.Distinct().Count() || listMaHinhThucThiAdd.Count() != listMaHinhThucThiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvHinhThucThis.AnyAsync(x => listHinhThucThiAdd.Contains(x.HinhThucThi)) || await _dataContext.SvHinhThucThis.AnyAsync(x => listMaHinhThucThiAdd.Contains(x.MaHinhThucThi)))
                {
                    throw new ArgumentException($"{_localizer["HinhThucThi.Existed"]}");
                }

                var listEntity = model.listHinhThucThiModels.Select(x => new SvHinhThucThi()
                {
                    IdHinhThucThi = x.IdHinhThucThi,
                    MaHinhThucThi = x.MaHinhThucThi,
                    HinhThucThi = x.HinhThucThi,
                    GhiChu = x.GhiChu,
                    KhongKiemTraTrungLich = x.KhongKiemtraTrungLich,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHinhThucThi).ToList();

                Log.Information($"Create many {HinhThucThiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import hình thức thi từ file excel",
                    ObjectCode = HinhThucThiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHinhThucThiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHinhThucThiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHinhThucThiCommand(int id, UpdateHinhThucThiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHinhThucThiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHinhThucThiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HinhThucThiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvHinhThucThis.FirstOrDefaultAsync(dt => dt.IdHinhThucThi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvHinhThucThis.AnyAsync(x => (x.HinhThucThi == model.HinhThucThi || x.MaHinhThucThi == model.MaHinhThucThi) && x.IdHinhThucThi != model.IdHinhThucThi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HinhThucThi.Existed", model.HinhThucThi.ToString()]}");
                }

                Log.Information($"Before Update {HinhThucThiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvHinhThucThis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HinhThucThiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HinhThucThiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật hình thức thi: {entity.HinhThucThi}",
                    ObjectCode = HinhThucThiConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucThi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey(entity.IdHinhThucThi.ToString()));
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHinhThucThiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHinhThucThiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHinhThucThiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHinhThucThiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HinhThucThiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvHinhThucThis.FirstOrDefaultAsync(x => x.IdHinhThucThi == id);

                _dataContext.SvHinhThucThis.Remove(entity);

                Log.Information($"Delete {HinhThucThiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa hình thức thi: {entity.HinhThucThi}",
                    ObjectCode = HinhThucThiConstant.CachePrefix,
                    ObjectId = entity.IdHinhThucThi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey());
                _cacheService.Remove(HinhThucThiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
