using System.Reflection.Metadata;

namespace Leader.Shared
{
    /// <summary>
    /// Cachprefix - L<PERSON>u ý không được viết hoa vì elastic không nhận chữ hoa trong index
    /// </summary>
    public class LeaderCacheConstants
    {
        public const string KH_BAC_DAO_TAO = "SciKhBacDaoTao";

        #region Leader
        public const string TUYEN_SINH_PHUONG_THUC_XET_TUYEN = "TuyenSinhPhuongThucXetTuyen";
        public const string HE = "HE";
        public const string KHOA = "KHOA";
        public const string NGANH = "NGANH";
        public const string CHUYEN_NGANH = "CHUYEN_NGANH";
        public const string THONG_KE_TAI_CHINH = "THONG_KE_TAI_CHINH";
        public const string THONG_KE_DAO_TAO = "THONG_KE_DAO_TAO";
        public const string LOP = "LOP";
        public const string QUY_MO_SINH_VIEN = "QUY_MO_SINH_VIEN";
        public const string HOC_KY_DANG_KY = "HOC_KY_DANG_KY";
        public const string THONG_KE_NHAP_HOC = "THONG_KE_NHAP_HOC";
        #endregion
    }

    public class LeaderLogConstants
    {
        #region Bậc đào tạo khoa học
        public const string ACTION_KH_BAC_DAO_TAO_CREATE = "Thêm mới bậc đào tạo khoa học";
        public const string ACTION_KH_BAC_DAO_TAO_UPDATE = "Cập nhật bậc đào tạo khoa học";
        public const string ACTION_KH_BAC_DAO_TAO_DELETE = "Xóa bậc đào tạo khoa học";
        #endregion
     
    }

    public class LeaderThamSoHeThongConstants
    {
       
    }

    public class Actions
    {
        public const string ADD = "add";
        public const string EDIT = "edit";
        public const string DEL = "delete";
    }
}
