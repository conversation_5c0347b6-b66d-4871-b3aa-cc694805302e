﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class TonGiaoSelectItemModel
    {
        public int IdTonGiao { get; set; }
        public string MaTonGiao { get; set; }
        public string TonGiao { get; set; }
    }

    public class TonGiaoBaseModel
    {
        public int IdTonGiao { get; set; }
        public string MaTonGiao { get; set; }
        public string TonGiao { get; set; }
        public string TonGiaoEn { get; set; }
    }


    public class TonGiaoModel : TonGiaoBaseModel
    {
      
    }

    public class TonGiaoFilterModel : BaseQueryFilterModel
    {
        public TonGiaoFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTonGiao";
        }
    }

    public class CreateTonGiaoModel
    {
        [Required(ErrorMessage = "TonGiao.IdTonGiao.NotRequire")]
        public int IdTonGiao { get; set; }

        [MaxLength(5, ErrorMessage = "TonGiao.MaTonGiao.MaxLength(5)")]
        [Required(ErrorMessage = "TonGiao.MaTonGiao.NotRequire")]
        public string MaTonGiao { get; set; }

        [MaxLength(50, ErrorMessage = "TonGiao.TonGiao.MaxLength(50)")]
        [Required(ErrorMessage = "TonGiao.TonGiao.NotRequire")]
        public string TonGiao { get; set; }

        [MaxLength(50, ErrorMessage = "TonGiao.TonGiaoEn.MaxLength(50)")]
        public string TonGiaoEn { get; set; }

    }

    public class CreateManyTonGiaoModel
    {
        public List<CreateTonGiaoModel> listTonGiaoModels { get; set; }
    }

    public class UpdateTonGiaoModel : CreateTonGiaoModel
    {
        public void UpdateEntity(SvTonGiao input)
        {
            input.IdTonGiao = IdTonGiao;
            input.MaTonGiao = MaTonGiao;
            input.TonGiao = TonGiao;
            input.TonGiaoEn = TonGiaoEn;

        }
    }
}
