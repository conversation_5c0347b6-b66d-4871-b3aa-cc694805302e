﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepHangHocLuc")]
    public class SvXepHangHocLuc
    {
        
        public SvXepHangHocLuc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_hang")]
        public int IdXepHang { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Xep_hang"), Max<PERSON>ength(20)]
        public string XepHang { get; set; }

        [Column("Xep_hang_en"), Max<PERSON>ength(50)]
        public string XepHangEn { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }
    }
}
