﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svKhoa")]
    public class SvKhoa
    {
        public SvKhoa()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_khoa")]
        public int IdKhoa { get; set; }

        [Column("Ma_khoa"), MaxLength(5)]
        public string Ma<PERSON>hoa { get; set; }

        [Column("Ten_khoa"), Max<PERSON>ength(50)]
        public string TenKhoa { get; set; }

        [Column("Ten_khoa_en"), Max<PERSON>ength(50)]
        public string TenKhoaEn { get; set; }
    }
}
