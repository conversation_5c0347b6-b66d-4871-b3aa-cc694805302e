﻿using System;
using System.Text.Json.Serialization;
using Core.Shared;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Core.DataLog
{
    [BsonIgnoreExtraElements]
    public class SendMailLog
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("trace_id")]
        public string TraceId { get; set; }

        [BsonElement("to_email")]
        public string ToEmail { get; set; }

        [BsonElement("email_temp_code")]
        public string EmailTemplateCode { get; set; }

        [BsonElement("email_temp_name")]
        public string EmailTemplateName { get; set; }

        [BsonElement("subject")]
        public string Subject { get; set; }

        /// <summary>
        /// Enum: SendMailStatus
        /// </summary>
        [BsonElement("status")]
        public int SendMailStatus { get; set; }

        /// <summary>
        /// MessageId nhận về sau khi gọi FluentEmail gửi mail
        /// </summary>
        [BsonElement("message_id")]
        public string MessageId { get; set; }

        /// <summary>
        /// ErrorMesssage nhận về sau khi gọi FluentEmail gửi mail (nếu có lỗi sẽ có dữ liệu)
        /// </summary>
        [BsonElement("error_messsage")]
        public string ErrorMesssage { get; set; }

        [BsonElement("created_date")]
        [BsonRepresentation(BsonType.DateTime)]
        public DateTime CreatedDate { get; set; }
    }
}
