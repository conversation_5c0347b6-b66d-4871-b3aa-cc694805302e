using Microsoft.EntityFrameworkCore;

namespace Leader.Data
{
    public partial class LeaderReadDataContext : DbContext
    {
        public LeaderReadDataContext()
        {
        }

        public LeaderReadDataContext(DbContextOptions<LeaderReadDataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<KhBacDaoTao> KhBacDaoTaos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
