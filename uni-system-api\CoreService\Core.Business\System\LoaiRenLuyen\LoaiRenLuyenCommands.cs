﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiRenLuyenCommand : IRequest<Unit>
    {
        public CreateLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiRenLuyenCommand(CreateLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiRenLuyenModel, SvLoaiRenLuyen>(model);

                var checkCode = await _dataContext.SvLoaiRenLuyens.AnyAsync(x => x.TenLoai == entity.TenLoai || x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiRenLuyen.Existed", entity.TenLoai.ToString()]}");
                }

                await _dataContext.SvLoaiRenLuyens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại rèn luyện: {entity.TenLoai}",
                    ObjectCode = LoaiRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdLoaiRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiRenLuyenCommand : IRequest<Unit>
    {
        public CreateManyLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiRenLuyenCommand(CreateManyLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listLoaiRenLuyenAdd = model.listLoaiRenLuyenModels.Select(x => x.TenLoai).ToList();
                var listKyHieuAdd = model.listLoaiRenLuyenModels.Select(x => x.KyHieu).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiRenLuyenModel, SvLoaiRenLuyen>(model);

                // Check data duplicate
                if (listLoaiRenLuyenAdd.Count() != listLoaiRenLuyenAdd.Distinct().Count() || listKyHieuAdd.Count() != listKyHieuAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvLoaiRenLuyens.AnyAsync(x => listLoaiRenLuyenAdd.Contains(x.TenLoai)) || await _dataContext.SvLoaiRenLuyens.AnyAsync(x => listKyHieuAdd.Contains(x.KyHieu)))
                {
                    throw new ArgumentException($"{_localizer["LoaiRenLuyen.Existed"]}");
                }

                var listEntity = model.listLoaiRenLuyenModels.Select(x => new SvLoaiRenLuyen()
                {
                    IdLoaiRenLuyen = x.IdLoaiRenLuyen,
                    IdCapRenLuyen = x.IdCapRenLuyen,
                    KyHieu = x.KyHieu,
                    TenLoai = x.TenLoai,
                    Diem = x.Diem,
                    DiemTru = x.DiemTru,
                    HocTap = x.HocTap,
                    TinhDiem = x.TinhDiem,
                    HienThi = x.HienThi

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdLoaiRenLuyen).ToList();

                Log.Information($"Create many {LoaiRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import loại rèn luyện từ file excel",
                    ObjectCode = LoaiRenLuyenConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiRenLuyenCommand(int id, UpdateLoaiRenLuyenModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiRenLuyens.FirstOrDefaultAsync(dt => dt.IdLoaiRenLuyen == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvLoaiRenLuyens.AnyAsync(x => (x.TenLoai == model.TenLoai || x.KyHieu == model.KyHieu) && x.IdLoaiRenLuyen != model.IdLoaiRenLuyen);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiRenLuyen.Existed", model.TenLoai.ToString()]}");
                }

                Log.Information($"Before Update {LoaiRenLuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiRenLuyens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiRenLuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại rèn luyện: {entity.TenLoai}",
                    ObjectCode = LoaiRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdLoaiRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey(entity.IdLoaiRenLuyen.ToString()));
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiRenLuyenCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiRenLuyens.FirstOrDefaultAsync(x => x.IdLoaiRenLuyen == id);

                _dataContext.SvLoaiRenLuyens.Remove(entity);

                Log.Information($"Delete {LoaiRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại rèn luyện: {entity.TenLoai}",
                    ObjectCode = LoaiRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdLoaiRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey());
                _cacheService.Remove(LoaiRenLuyenConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
