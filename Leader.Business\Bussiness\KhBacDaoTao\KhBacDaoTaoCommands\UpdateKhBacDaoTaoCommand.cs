﻿using Core.Business;
using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Leader.Data;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class UpdateKhBacDaoTaoCommand : IRequest<Unit>
    {
        public UpdateKhBacDaoTaoModel Model { get; set; }

        /// <summary>
        /// Cập nhật bậc đào tạo khoa học
        /// </summary>
        /// <param name="model">Thông tin bậc đào tạo khoa học cần cập nhật</param>
        public UpdateKhBacDaoTaoCommand(UpdateKhBacDaoTaoModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateKhBacDaoTaoCommand, Unit>
        {
            private readonly LeaderDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(LeaderDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateKhBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {KhBacDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.KhBacDaoTaos.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                model.UpdateEntity(entity);
                _dataContext.KhBacDaoTaos.Update(entity);
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(KhBacDaoTaoConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(KhBacDaoTaoConstant.BuildCacheKey());

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập bậc đào tạo khoa học mã: {entity.Code}",
                    ObjectCode = KhBacDaoTaoConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
