﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class PhuongThucDongBaseModel
    {
        public int IdPhuongThucDong { get; set; }

        [Required(ErrorMessage = "PhuongThucDong.PhuongThucDong.NotRequire")]
        [MaxLength(255, ErrorMessage = "PhuongThucDong.PhuongThucDong.MaxLength(255)")]
        public string PhuongThucDong { get; set; }

        public string GhiChu { get; set; }
    }

    public class PhuongThucDongModel : PhuongThucDongBaseModel
    {
    }

    public class CreatePhuongThucDongModel
    {
        [Required(ErrorMessage = "PhuongThucDong.PhuongThucDong.NotRequire")]
        [MaxLength(255, ErrorMessage = "PhuongThucDong.PhuongThucDong.MaxLength(255)")]
        public string PhuongThucDong { get; set; }

        public string GhiChu { get; set; }
    }

    public class CreateManyPhuongThucDongModel
    {
        public List<CreatePhuongThucDongModel> ListPhuongThucDong { get; set; }
    }

    public class UpdatePhuongThucDongModel : CreatePhuongThucDongModel
    {
        [Required(ErrorMessage = "PhuongThucDong.IdPhuongThucDong.NotRequire")]
        public int IdPhuongThucDong { get; set; }

        public void UpdateEntity(SvPhuongThucDong entity)
        {
            entity.IdPhuongThucDong = IdPhuongThucDong;
            entity.PhuongThucDong = PhuongThucDong;
            entity.GhiChu = GhiChu;
        }
    }

    public class PhuongThucDongSelectItemModel
    {
        public int IdPhuongThucDong { get; set; }
        public string PhuongThucDong { get; set; }
    }

    public class PhuongThucDongQueryFilter : BaseQueryFilterModel
    {
    }
}
