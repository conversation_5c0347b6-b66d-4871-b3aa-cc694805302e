﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Core.Shared;
using Serilog;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Sinks.Elasticsearch;
using System;
using System.IO;
using System.Reflection;
using System.Text;

namespace Leader.API
{
    public class Program
    {
        //[Obsolete]
        public static void Main(string[] args)
        {
            //Fix lỗi: .NET6 and DateTime problem. Cannot write DateTime with Kind=UTC to PostgreSQL type 'timestamp without time zone'
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);

            var host = CreateHostBuilder(args).Build();
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            Log.Information($"API started in folder: {AppDomain.CurrentDomain.BaseDirectory} - (with evirontment - {environment}) at: " + DateTime.Now.ToString());
            //Log.Error("Demo Error Log - " + Guid.NewGuid().ToString());
            host.Run();
        }


        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                })
                .UseSerilog();
    }
}
