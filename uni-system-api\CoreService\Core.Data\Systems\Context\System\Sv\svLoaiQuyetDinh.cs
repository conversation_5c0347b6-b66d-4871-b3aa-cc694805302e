﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiQuyetDinh")]
    public class SvLoaiQuyetDinh
    {

        public SvLoaiQuyetDinh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_loai_qd")]
        public int IdLoaiQd { get; set; }

        [Column("Ma_QD"), MaxLength(10)]
        public string MaQd { get; set; }

        [Column("Ten_loai_QD"), MaxLength(100)]
        public string TenLoaiQd { get; set; }

        [Column("Chuyen_lop")]
        public bool ChuyenLop { get; set; }

        [Column("Thoi_hoc")]
        public bool ThoiHoc { get; set; }

        [Column("Ngung_hoc")]
        public bool NgungHoc { get; set; }

        [Column("Hoc_tiep")]
        public bool HocTiep { get; set; }

        [Column("Chuyen_truong_di")]
        public bool ChuyenTruongDi { get; set; }

        [Column("Chuyen_truong_den")]
        public bool ChuyentruongDen { get; set; }

        [Column("Thoi_hoc_quy_che")]
        public bool ThoiHocQuyChe { get; set; }


        [Column("Xoa_ten_khoi_lop")]
        public bool XoaTenkhoiLop { get; set; }


    }
}
