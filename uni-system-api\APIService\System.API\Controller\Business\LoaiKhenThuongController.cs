﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/loai-khen-thuong")]
    [ApiExplorerSettings(GroupName = "30. Loại khen thưởng")]
    [Authorize]
    public class LoaiKhenThuongController : ApiControllerBase
    {
        public LoaiKhenThuongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách loại khen thưởng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiKhenThuongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiKhenThuongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách loại khen thưởng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiKhenThuongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiKhenThuongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiKhenThuongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết loại khen thưởng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiKhenThuongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiKhenThuongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới loại khen thưởng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiKhenThuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_KHEN_THUONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_KHEN_THUONG_CREATE;


                return await _mediator.Send(new CreateLoaiKhenThuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel loại khen thưởng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyLoaiKhenThuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_KHEN_THUONG_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_KHEN_THUONG_CREATE_MANY;


                return await _mediator.Send(new CreateManyLoaiKhenThuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa loại khen thưởng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLoaiKhenThuongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_KHEN_THUONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_KHEN_THUONG_UPDATE;
                return await _mediator.Send(new UpdateLoaiKhenThuongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa loại khen thưởng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_KHEN_THUONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_KHEN_THUONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_KHEN_THUONG_DELETE;

                return await _mediator.Send(new DeleteLoaiKhenThuongCommand(id, u.SystemLog));
            });
        }

    }
}
