﻿using Core.Business.Workflow;
using Core.Data;
using MediatR;
using OptimaJet.Workflow.Core.Parser;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetAllStateNameByWorkflowNameQuery : IRequest<GetAllStatesNameResponseModel>
    {
        public string WorkflowName { get; set; }

        /// <summary>
        /// Lấy combobox state name
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetAllStateNameByWorkflowNameQuery(string workflowName)
        {
            WorkflowName = workflowName;
        }

        public class Handler : IRequestHandler<GetAllStateNameByWorkflowNameQuery, GetAllStatesNameResponseModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<GetAllStatesNameResponseModel> Handle(GetAllStateNameByWorkflowNameQuery request, CancellationToken cancellationToken)
            {
                var workflowName = request.WorkflowName;

                // Get scheme
                var scheme = await WorkflowInit.PersistenceProviderContainer.Provider.GetSchemeAsync(workflowName);

                if (string.IsNullOrEmpty(scheme.ToString()))
                    throw new ArgumentException("workflow.scheme-not-found");

                // Phân tích XML scheme
                var parser = new XmlWorkflowParser();
                var parsedScheme = parser.Parse(WorkflowInit.Runtime, scheme);

                // Trích xuất danh sách state name
                var stateNames = parsedScheme.Activities.Select(x => x.Name).ToList();

                return new GetAllStatesNameResponseModel { StateNames = stateNames };
            }
        }
    }
}
