#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base

RUN  ["rm", "-rf", "/etc/localtime"]
RUN  ["ln", "-s", "/usr/share/zoneinfo/Asia/Ho_Chi_Minh", "/etc/localtime"]


RUN apt-get update && apt-get install -y apt-utils libgdiplus libc6-dev

RUN apt-get update; apt-get install -y fontconfig fonts-liberation
RUN fc-cache -f -v

USER app
WORKDIR /app

EXPOSE 80
ENV ASPNETCORE_URLS=http://+:80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["APIService/System.API/System.API.csproj", "APIService/System.API/"]
COPY ["CoreService/Core.Business/Core.Business.csproj", "CoreService/Core.Business/"]
COPY ["CoreService/Core.DataLog/Core.DataLog.csproj", "CoreService/Core.DataLog/"]
COPY ["CoreService/Core.Shared/Core.Shared.csproj", "CoreService/Core.Shared/"]
COPY ["CoreService/Core.Data/Core.Data.csproj", "CoreService/Core.Data/"]
COPY ["APIService/Core.API.Shared/Core.API.Shared.csproj", "APIService/Core.API.Shared/"]
RUN dotnet restore "./APIService/System.API/System.API.csproj"
COPY . .
WORKDIR "/src/APIService/System.API"
RUN dotnet build "./System.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./System.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

COPY ./file/Spire.License.dll .

ENTRYPOINT ["dotnet", "System.API.dll"]