﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Leader.API.Services;

namespace Leader.API
{
    public static class HostedServiceCollection
    {
        /// <summary>
        /// RegisterHostedServiceComponents
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterHostedServiceComponents(this IServiceCollection services, IConfiguration configuration)
        {
           
            services.AddHostedService<StartupService>();

            return services;
        }
    }
}