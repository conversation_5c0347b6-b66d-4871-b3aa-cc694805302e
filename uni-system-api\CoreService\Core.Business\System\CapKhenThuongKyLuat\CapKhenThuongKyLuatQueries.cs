﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxCapKhenThuongKyLuatQuery : IRequest<List<CapKhenThuongKyLuatSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y cấp khen thưởng kỷ luật cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxCapKhenThuongKyLuatQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxCapKhenThuongKyLuatQuery, List<CapKhenThuongKyLuatSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<CapKhenThuongKyLuatSelectItemModel>> Handle(GetComboboxCapKhenThuongKyLuatQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = CapKhenThuongKyLuatConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvCapKhenThuongKyLuats.OrderBy(x => x.TenCap)
                                select new CapKhenThuongKyLuatSelectItemModel()
                                {
                                    IdCap = dt.IdCap,
                                    MaCap = dt.MaCap,
                                    TenCap = dt.TenCap
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenCap.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterCapKhenThuongKyLuatQuery : IRequest<PaginationList<CapKhenThuongKyLuatBaseModel>>
    {
        public CapKhenThuongKyLuatFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách cấp khen thưởng kỷ luật có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterCapKhenThuongKyLuatQuery(CapKhenThuongKyLuatFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterCapKhenThuongKyLuatQuery, PaginationList<CapKhenThuongKyLuatBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<CapKhenThuongKyLuatBaseModel>> Handle(GetFilterCapKhenThuongKyLuatQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvCapKhenThuongKyLuats
                            select new CapKhenThuongKyLuatBaseModel
                            {
                                IdCap = dt.IdCap,
                                MaCap = dt.MaCap,
                                TenCap = dt.TenCap,


                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenCap.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<CapKhenThuongKyLuatBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetCapKhenThuongKyLuatByIdQuery : IRequest<CapKhenThuongKyLuatModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin cấp khen thưởng kỷ luật theo id
        /// </summary>
        /// <param name="id">Id cấp khen thưởng kỷ luật</param>
        public GetCapKhenThuongKyLuatByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetCapKhenThuongKyLuatByIdQuery, CapKhenThuongKyLuatModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<CapKhenThuongKyLuatModel> Handle(GetCapKhenThuongKyLuatByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = CapKhenThuongKyLuatConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvCapKhenThuongKyLuats.FirstOrDefaultAsync(x => x.IdCap == id);

                    return AutoMapperUtils.AutoMap<SvCapKhenThuongKyLuat, CapKhenThuongKyLuatModel>(entity);
                });
                return item;
            }
        }
    }
}
