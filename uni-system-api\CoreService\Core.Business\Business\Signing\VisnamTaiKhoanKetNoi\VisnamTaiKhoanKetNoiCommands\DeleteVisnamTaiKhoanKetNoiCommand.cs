﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteVisnamTaiKhoanKetNoiCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Xóa tài khoản kết nối Visnam theo Id truyền vào
        /// </summary>
        /// <param name="id">Id tài khoản kết nối Visnam cần xóa</param>
        public DeleteVisnamTaiKhoanKetNoiCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteVisnamTaiKhoanKetNoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteVisnamTaiKhoanKetNoiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {VisnamTaiKhoanKetNoiConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SgVisnamTaiKhoanKetNois.FirstOrDefaultAsync(x => x.Id == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SgVisnamTaiKhoanKetNois.Remove(dt);

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa tài khoản kết nối Visnam cho User ID: {dt.UserId}",
                    ObjectCode = VisnamTaiKhoanKetNoiConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(VisnamTaiKhoanKetNoiConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(VisnamTaiKhoanKetNoiConstant.BuildCacheKey());

                Log.Information($"Delete {VisnamTaiKhoanKetNoiConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
