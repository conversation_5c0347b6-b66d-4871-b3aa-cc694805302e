﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/quoc-tich")]
    [ApiExplorerSettings(GroupName = "15. Quốc tịch")]
    [Authorize]
    public class QuocTichController : ApiControllerBase
    {
        public QuocTichController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// L<PERSON>y danh sách quốc tịch cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<QuocTichSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxQuocTichQuery(count, ts));
            });
        }


        /// <summary>
        /// Lấy danh sách quốc tịch có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<QuocTichBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] QuocTichFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterQuocTichQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết quốc tịch
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<QuocTichModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetQuocTichByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới quốc tịch
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateQuocTichModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_QUOC_TICH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_QUOC_TICH_CREATE;


                return await _mediator.Send(new CreateQuocTichCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel quốc tịch
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyQuocTichModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_QUOC_TICH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_QUOC_TICH_CREATE_MANY;


                return await _mediator.Send(new CreateManyQuocTichCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa quốc tịch
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateQuocTichModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_QUOC_TICH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_QUOC_TICH_UPDATE;
                return await _mediator.Send(new UpdateQuocTichCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa quốc tịch
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUOC_TICH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_QUOC_TICH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_QUOC_TICH_DELETE;

                return await _mediator.Send(new DeleteQuocTichCommand(id, u.SystemLog));
            });
        }

    }


}
