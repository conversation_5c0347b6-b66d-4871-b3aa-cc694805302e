﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("htPhanHe")]
    public partial class HtPhanHe
    {
        [Key]
        [Column("ID_ph")]
        public int IdPh { get; set; }
        
        [<PERSON>umn("Phan_he")]
        public string <PERSON>an<PERSON>e { get; set; }
        
        [Column("Mieu_ta")]
        public string MieuTa { get; set; }
        
        [Column("Url")]
        public string Url { get; set; }
        
        [Column("Ky_hieu_phan_he")]
        public string <PERSON>yHieuPhanHe { get; set; }
        
        [Column("Ma_phan_he")]
        public string <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
        
        [Column("Hinh_anh_phan_he")]
        public string Hinh<PERSON><PERSON><PERSON>hanHe { get; set; }

        [Column("Active")]
        public bool Active { get; set; }
        
        [Column("STT")]
        public int STT { get; set; }

        [Column("Hiden")]
        public bool Hiden { get; set; }
    }
}
