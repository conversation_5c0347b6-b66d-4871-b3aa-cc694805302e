﻿using Core.Business;
using Core.Data;
using Core.Shared;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    /// <summary>
    /// L<PERSON>y danh sách thống kê số lượng lớp theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách thống kê số lượng lớp</returns>
    public class GetThongKeSoLuongByFilterQuery : IRequest<PaginationList<ThongKeSoLuongModel>>
    {
        public ThongKeDaoTaoQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách thống kê số lượng lớp theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetThongKeSoLuongByFilterQuery(ThongKeDaoTaoQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetThongKeSoLuongByFilterQuery, PaginationList<ThongKeSoLuongModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<ThongKeSoLuongModel>> Handle(GetThongKeSoLuongByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreSoLuongLopTinChiAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc, filter.Dot);

                var data = new List<ThongKeSoLuongModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new ThongKeSoLuongModel()
                    {
                        TenHe = row.Field<string>("Ten_he"),
                        TenKhoa = row.Field<string>("Ten_khoa"),
                        KhoaHoc = row.Field<int>("Khoa_hoc"),
                        SoLuong = row.Field<int?>("So_lop_tc"),
                    });
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<ThongKeSoLuongModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }

    /// <summary>
    /// Lấy danh sách thống kê danh sách lớp tín chỉ theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Thống kê danh sách lớp tín chỉ</returns>
    public class GetThongKeDanhSachByFilterQuery : IRequest<PaginationList<ThongKeDanhSachModel>>
    {
        public ThongKeDaoTaoQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy thống kê danh sách lớp tín chỉ theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetThongKeDanhSachByFilterQuery(ThongKeDaoTaoQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetThongKeDanhSachByFilterQuery, PaginationList<ThongKeDanhSachModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<ThongKeDanhSachModel>> Handle(GetThongKeDanhSachByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreDanhSachLopTinChiAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc, filter.Dot);

                var data = new List<ThongKeDanhSachModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new ThongKeDanhSachModel()
                    {
                        MaHocPhan = row.Field<string>("Ky_hieu"),
                        TenHocPhan = row.Field<string>("Ten_mon"),
                        SoTC = row.Field<float?>("So_tin_chi"),
                        TenLopTC = row.Field<string>("Ten_lop_tc"),
                        SoSVDangKy = row.Field<int?>("So_sv_dang_ky"),
                        HoTenGV = row.Field<string>("Ho_ten"),
                        TuNgay = row.Field<DateTime?>("Tu_ngay_sk"),
                        DenNgay = row.Field<DateTime?>("Den_ngay_sk"),
                        LichHoc = row.Field<string>("Lich_hoc"),
                        PhongHoc = row.Field<string>("Ten_phong")
                    });
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<ThongKeDanhSachModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }

    /// <summary>
    /// Lấy danh sách thống kê tổ chức thi theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách thống kê tổ chức thi</returns>
    public class GetThongKeToChucThiByFilterQuery : IRequest<PaginationList<ThongKeToChucThiModel>>
    {
        public ThongKeDaoTaoQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách thống kê tổ chức thi theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetThongKeToChucThiByFilterQuery(ThongKeDaoTaoQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetThongKeToChucThiByFilterQuery, PaginationList<ThongKeToChucThiModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<ThongKeToChucThiModel>> Handle(GetThongKeToChucThiByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreThongKeToChucThiAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc, filter.Dot);

                var data = new List<ThongKeToChucThiModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new ThongKeToChucThiModel()
                    {
                        MaHocPhan = row.Field<string>("Ky_hieu"),
                        TenHocPhan = row.Field<string>("Ten_mon"),
                        DotThi = row.Field<int>("Dot_thi"),
                        LanThi = row.Field<int>("Lan_thi"),
                        NgayThi = row.Field<string>("Ngay_thi"),
                        CaThi = row.Field<string>("Ca_thi"),
                        GioThi = row.Field<string>("Gio_thi"),
                        PhongThi = row.Field<string>("Ten_phong"),
                        SoSVPhong = row.Field<int?>("So_sv_phong"),
                        SoSVThi = row.Field<int?>("So_sv_thi"),
                        GiamThi1 = row.Field<string>("Ten_giam_thi1"),
                        GiamThi2 = row.Field<string>("Ten_giam_thi2"),
                        GiamThi3 = row.Field<string>("Ten_giam_thi3"),
                        GiamThi4 = row.Field<string>("Ten_giam_thi4")
                    });
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<ThongKeToChucThiModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }

    /// <summary>
    /// Lấy danh sách tổng hợp lớp tín chỉ theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách tổng hợp lớp tín chỉ</returns>
    public class GetTongHopLopTinChiByFilterQuery : IRequest<PaginationList<TongHopLopTinChiModel>>
    {
        public ThongKeDaoTaoQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách tổng hợp số lượng lớp theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetTongHopLopTinChiByFilterQuery(ThongKeDaoTaoQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetTongHopLopTinChiByFilterQuery, PaginationList<TongHopLopTinChiModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(LeaderReadDataContext dataContext, ILeaderCallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<TongHopLopTinChiModel>> Handle(GetTongHopLopTinChiByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreSoLuongLopTinChiAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc, filter.Dot);

                var dataStore = dataTable.AsEnumerable().Select(row => new ThongKeSoLuongModel
                {
                    TenHe = row.Field<string>("Ten_he"),
                    TenKhoa = row.Field<string>("Ten_khoa"),
                    KhoaHoc = row.Field<int>("Khoa_hoc"),
                    SoLuong = row.Field<int?>("So_lop_tc"),
                }).ToList();

                var dataKhoa = await _dataContext.SvKhoas.ToListAsync();
                var dataHe = await _dataContext.SvHes.OrderBy(x => x.TenHe).Select(dt => new TongHopLopTinChiBaseModel
                {
                    TenHe = dt.TenHe,
                    Count = 0
                }).ToListAsync();

                var dataStoreDict = dataStore.ToDictionary(
                    k => (k.TenHe, k.TenKhoa),
                    v => v.SoLuong);

                List<TongHopLopTinChiModel> data = dataKhoa.Select(khoa => new TongHopLopTinChiModel
                {
                    TenKhoa = khoa.TenKhoa,
                    ListTH = dataHe.Select(he => new TongHopLopTinChiBaseModel
                    {
                        TenHe = he.TenHe,
                        Count = dataStoreDict.TryGetValue((he.TenHe, khoa.TenKhoa), out var soLuong) ? soLuong : 0
                    }).ToList()
                }).ToList();


                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<TongHopLopTinChiModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }
}

