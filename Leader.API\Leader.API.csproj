﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>425b4f5f-9a2d-4859-8fc2-b0e291718a67</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <DockerfileContext>..\..</DockerfileContext> 
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="wwwroot\images\**" />
    <Content Remove="wwwroot\images\**" />
    <EmbeddedResource Remove="wwwroot\images\**" />
    <None Remove="wwwroot\images\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Leader.Business\Leader.Business.csproj" />
    <ProjectReference Include="..\uni-system-api\APIService\Core.API.Shared\Core.API.Shared.csproj" />
  </ItemGroup>

</Project>
