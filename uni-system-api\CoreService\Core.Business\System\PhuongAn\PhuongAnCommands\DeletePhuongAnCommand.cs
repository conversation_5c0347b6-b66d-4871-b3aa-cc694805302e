﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeletePhuongAnCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Xóa phương án theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Id phương án cần xóa</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public DeletePhuongAnCommand(int id, RequestUser requestUser)
        {
            Id = id;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<DeletePhuongAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeletePhuongAnCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var requestUser = request.RequestUser;
                Log.Information($"Delete {PhuongAnConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SvPhuongAns.FirstOrDefaultAsync(x => x.IdPhuongAn == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SvPhuongAns.Remove(dt);

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phương án mã: {dt.MaPhuongAn}",
                    ObjectCode = PhuongAnConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(PhuongAnConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(PhuongAnConstant.BuildCacheKey());

                Log.Information($"Delete {PhuongAnConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
