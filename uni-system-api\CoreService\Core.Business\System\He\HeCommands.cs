﻿using Core.Shared;
using MediatR;
using Microsoft.Extensions.Localization;
using Serilog;
using System.Threading.Tasks;
using System.Threading;
using System;
using Core.Data;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Core.Business
{
    public class HeCommands
    {

        public class CreateHeCommand : IRequest<Unit>
        {
            public CreateHeModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateHeCommand(CreateHeModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateHeCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateHeCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {HeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateHeModel, SvHe>(model);

                    var checkCode = await _dataContext.SvHes.AnyAsync(x => x.IdHe == entity.IdHe || x.TenHe == entity.TenHe || x.MaHe == entity.MaHe);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["He.Existed", entity.TenHe.ToString()]}");
                    }

                    await _dataContext.SvHes.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {HeConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới hệ: {entity.TenHe}",
                        ObjectCode = HeConstant.CachePrefix,
                        ObjectId = entity.IdHe.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(HeConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class CreateManyHeCommand : IRequest<Unit>
        {
            public CreateManyHeModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateManyHeCommand(CreateManyHeModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateManyHeCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateManyHeCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create many {HeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var listTenHeAdd = model.listHeModels.Select(x => x.TenHe ).ToList();
                    var listMaHeAdd = model.listHeModels.Select(x => x.MaHe).ToList();
                    var entity = AutoMapperUtils.AutoMap<CreateManyHeModel, SvHe>(model);

                    // Check data duplicate
                    if (listTenHeAdd.Count() != listTenHeAdd.Distinct().Count() || listMaHeAdd.Count() != listMaHeAdd.Distinct().Count())
                    {
                        throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                    }
                    // Check data exits DB
                    if (await _dataContext.SvHes.AnyAsync(x => listTenHeAdd.Contains( x.TenHe)) || await _dataContext.SvHes.AnyAsync(x => listMaHeAdd.Contains(x.MaHe)))
                    {
                        throw new ArgumentException($"{_localizer["He.Existed"]}");
                    }

                    var listEntity = model.listHeModels.Select(x => new SvHe()
                    {
                        IdHe = x.IdHe,
                        MaHe = x.MaHe,
                        TenHe = x.TenHe,
                        TenHeEn = x.TenHeEn,
                        QuyChe = x.QuyChe,
                        TenBacDaoTao = x.TenBacDaoTao,
                        TenBacDaoTaoEn = x.TenBacDaoTaoEn,
                        HinhThucDaoTao = x.HinhThucDaoTao,
                        HinhThucDaoTaoEn = x.HinhThucDaoTaoEn,
                    }).ToList();

                    await _dataContext.AddRangeAsync(listEntity);
                    await _dataContext.SaveChangesAsync();

                    var createdIds = listEntity.Select(e => e.IdHe).ToList();

                    Log.Information($"Create many {HeConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Import hệ từ file excel",
                        ObjectCode = HeConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(createdIds)
                    });

                    //Xóa cache
                    _cacheService.Remove(HeConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class UpdateHeCommand : IRequest<Unit>
        {
            public int Id { get; set; }
            public UpdateHeModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public UpdateHeCommand(int id, UpdateHeModel model, SystemLogModel systemLog)
            {
                Id = id;
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<UpdateHeCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(UpdateHeCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    int id = request.Id;
                    var systemLog = request.SystemLog;
                    Log.Information($"Update {HeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = await _dataContext.SvHes.FirstOrDefaultAsync(dt => dt.IdHe == id);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                   
                    var checkCode = await _dataContext.SvHes.AnyAsync(x => (x.TenHe ==model.TenHe || x.MaHe ==model.MaHe) && x.IdHe != model.IdHe);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["He.Existed",model.TenHe.ToString()]}");
                    }

                    Log.Information($"Before Update {HeConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);

                    _dataContext.SvHes.Update(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"After Update {HeConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    Log.Information($"Update {HeConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Cập nhật hệ: {entity.TenHe}",
                        ObjectCode = HeConstant.CachePrefix,
                        ObjectId = entity.IdHe.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(HeConstant.BuildCacheKey(entity.IdHe.ToString()));
                    _cacheService.Remove(HeConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class DeleteHeCommand : IRequest<Unit>
        {
            public int Id { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public DeleteHeCommand(int id, SystemLogModel systemLog)    
            {
                Id = id;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<DeleteHeCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(DeleteHeCommand request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    var systemLog = request.SystemLog;
                    Log.Information($"Delete {HeConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                    var entity = await _dataContext.SvHes.FirstOrDefaultAsync(x => x.IdHe == id);

                    _dataContext.SvHes.Remove(entity);

                    Log.Information($"Delete {HeConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa Hệ: {entity.TenHe}",
                        ObjectCode = HeConstant.CachePrefix,
                        ObjectId = entity.IdHe.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(HeConstant.BuildCacheKey());
                    _cacheService.Remove(HeConstant.BuildCacheKey(id.ToString()));
                    await _dataContext.SaveChangesAsync();

                    return Unit.Value;
                }
            }
        }
    }
}
