﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ChiTieuTuyenSinhSelectItemModel
    {
        public int IdChiTieuTuyenSinh { get; set; }
        public string MaChiTieuTuyenSinh { get; set; }
        public string ChiTieuTuyenSinh { get; set; }
    }

    public class ChiTieuTuyenSinhBaseModel
    {
        public int IdChiTieuTuyenSinh { get; set; }

        public int NamTuyenSinh { get; set; }

        public int IdHe { get; set; }

        public string TenHe { get; set; }

        public int IdNganh { get; set; }

        public string TenNganh { get; set; }

        public int ChiTieuTuyenSinh { get; set; }

        public Decimal DiemSanXetTuyen { get; set; }

        public int ChiTieuTuyenSinh1 { get; set; }
    }


    public class ChiTieuTuyenSinhModel : ChiTieuTuyenSinhBaseModel
    {

    }

    public class ChiTieuTuyenSinhFilterModel : BaseQueryFilterModel
    {
        public ChiTieuTuyenSinhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdChiTieuTuyenSinh";
        }
    }

    public class CreateChiTieuTuyenSinhModel
    {
        [Required(ErrorMessage = "ChiTieuTuyenSinh.NamTuyenSinh.NotRequire")]
        public int NamTuyenSinh { get; set; }

         [Required(ErrorMessage = "ChiTieuTuyenSinh.IdHe.NotRequire")]
        public int IdHe { get; set; }

         [Required(ErrorMessage = "ChiTieuTuyenSinh.IdNganh.NotRequire")]
        public int IdNganh { get; set; }

        [Required(ErrorMessage = "ChiTieuTuyenSinh.ChiTieuTuyenSinh.NotRequire")]
        public int ChiTieuTuyenSinh { get; set; } 
        
        [Required(ErrorMessage = "ChiTieuTuyenSinh.DiemSanXetTuyen.NotRequire")]
        public Decimal DiemSanXetTuyen { get; set; }

        [Required(ErrorMessage = "ChiTieuTuyenSinh.ChiTieuTuyenSinh1.NotRequire")]
        public int ChiTieuTuyenSinh1 { get; set; }

    }

    public class CreateManyChiTieuTuyenSinhModel
    {
        public List<CreateChiTieuTuyenSinhModel> listChiTieuTuyenSinhModels { get; set; }
    }

    public class UpdateChiTieuTuyenSinhModel : CreateChiTieuTuyenSinhModel
    {
        [Required(ErrorMessage = "ChiTieuTuyenSinh.IdChiTieuTuyenSinh.NotRequire")]
        public int IdChiTieuTuyenSinh { get; set; }
        public void UpdateEntity(SvChiTieuTuyenSinh input)
        {
            input.IdChiTieuTuyenSinh = IdChiTieuTuyenSinh;
            input.NamTuyenSinh = NamTuyenSinh;
            input.IdHe = IdHe;
            input.IdNganh = IdNganh;
            input.ChiTieuTuyenSinh = ChiTieuTuyenSinh;
            input.DiemSanXetTuyen = DiemSanXetTuyen;
            input.ChiTieuTuyenSinh1 = ChiTieuTuyenSinh1;

        }
    }
}
