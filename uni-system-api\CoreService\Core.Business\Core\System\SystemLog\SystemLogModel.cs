﻿using Core.DataLog;
using Core.Shared;
using System;
using System.Collections.Generic;

namespace Core.Business
{
    public class SystemLogQueryFilter
    {
        public string TextSearch { get; set; }
        public string TradeId { get; set; }
        public string Device { get; set; }
        public string ActionCode { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public SystemLogQueryFilter()
        {
            PageNumber = QueryFilter.DefaultPageNumber;
            PageSize = QueryFilter.DefaultPageSize;
        }
    }

    public class ActionCodeForComboboxModel
    {
        public string ActionCode { get; set; }
        public string ActionName { get; set; }
    }
}
