﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Cập nhật nhật ký gửi mail
    /// </summary>
    /// <param name="model">Model cập nhật nhật ký gửi mail</param>
    /// <returns>Id nhật ký gửi mail</returns>
    public class SendMailLogUpdateCommand : IRequest<Unit>
    {
        public SendMailLog Model { get; set; }

        public SendMailLogUpdateCommand(SendMailLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<SendMailLogUpdateCommand, Unit>
        {
            private readonly IMongoCollection<SendMailLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SendMailLog>(MongoCollections.SendMailLog);
                }
            }

            public async Task<Unit> Handle(SendMailLogUpdateCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    await _logs.ReplaceOneAsync(log => log.Id == model.Id, model).ConfigureAwait(false);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<SendMailLog, SendMailLogEntity>(model);
                    var entity = await _dataContext.SendMailLogs
                        .FirstOrDefaultAsync(x => x.Id == dt.Id, cancellationToken);
                    if (entity != null)
                    {
                        AutoMapperUtils.AutoMap(dt, entity);
                        _dataContext.SendMailLogs.Update(entity);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }
                return Unit.Value;
            }
        }
    }
}
