﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxXepHangHocLucQuery : IRequest<List<XepHangHocLucSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 10 cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepHangHocLucQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepHangHocLucQuery, List<XepHangHocLucSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepHangHocLucSelectItemModel>> Handle(GetComboboxXepHangHocLucQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepHangHocLucConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepHangHocLucs.OrderBy(x => x.XepHang)
                                select new XepHangHocLucSelectItemModel()
                                {
                                    IdXepHang = dt.IdXepHang,
                                    XepHang = dt.XepHang,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepHang.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepHangHocLucQuery : IRequest<PaginationList<XepHangHocLucBaseModel>>
    {
        public XepHangHocLucFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 10 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepHangHocLucQuery(XepHangHocLucFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepHangHocLucQuery, PaginationList<XepHangHocLucBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepHangHocLucBaseModel>> Handle(GetFilterXepHangHocLucQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepHangHocLucs
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepHangHocLucBaseModel
                            {
                                IdXepHang = dt.IdXepHang,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                XepHang = dt.XepHang,
                                XepHangEn = dt.XepHangEn,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepHang.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepHangHocLucBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepHangHocLucByIdQuery : IRequest<XepHangHocLucModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 10 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 10</param>
        public GetXepHangHocLucByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepHangHocLucByIdQuery, XepHangHocLucModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepHangHocLucModel> Handle(GetXepHangHocLucByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepHangHocLucConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepHangHocLucs.FirstOrDefaultAsync(x => x.IdXepHang == id);

                    return AutoMapperUtils.AutoMap<SvXepHangHocLuc, XepHangHocLucModel>(entity);
                });
                return item;
            }
        }
    }
}
