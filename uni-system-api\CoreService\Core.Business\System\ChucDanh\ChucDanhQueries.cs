﻿using Core.Business.System.ChucDanh;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxChucDanhQuery : IRequest<List<ChucDanhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tôn giáo cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxChucDanhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxChucDanhQuery, List<ChucDanhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChucDanhSelectItemModel>> Handle(GetComboboxChucDanhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChucDanhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbChucDanhs.OrderBy(x => x.ChucDanh)
                                select new ChucDanhSelectItemModel()
                                {
                                    IdChucDanh = dt.IdChucDanh,
                                    MaChucDanh = dt.MaChucDanh,
                                    ChucDanh = dt.ChucDanh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.ChucDanh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterChucDanhQuery : IRequest<PaginationList<ChucDanhBaseModel>>
    {
        public ChucDanhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tôn giáo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChucDanhQuery(ChucDanhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChucDanhQuery, PaginationList<ChucDanhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChucDanhBaseModel>> Handle(GetFilterChucDanhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbChucDanhs
                            select new ChucDanhBaseModel
                            {
                                IdChucDanh = dt.IdChucDanh,
                                MaChucDanh = dt.MaChucDanh,
                                ChucDanh = dt.ChucDanh

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.ChucDanh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ChucDanhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetChucDanhByIdQuery : IRequest<ChucDanhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Tôn giáo theo id
        /// </summary>
        /// <param name="id">Id tôn giáo</param>
        public GetChucDanhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetChucDanhByIdQuery, ChucDanhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ChucDanhModel> Handle(GetChucDanhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = ChucDanhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbChucDanhs.FirstOrDefaultAsync(x => x.IdChucDanh == id);

                    return AutoMapperUtils.AutoMap<TkbChucDanh, ChucDanhModel>(entity);
                });
                return item;
            }
        }
    }
}
