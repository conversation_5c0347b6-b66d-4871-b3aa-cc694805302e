﻿using Core.API.Shared;
using Core.Business;
using Leader.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Serilog;
using System.Linq;
using System.Threading.Tasks;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module quy mô sinh viên
    /// </summary>
    [ApiController]
    [Route("leader/v1/quy-mo-sinh-vien")]
    [ApiExplorerSettings(GroupName = "10. Quy mô sinh viên")]
    [Authorize]
    public class QuyMoSinhVienController : ApiControllerBase
    {
        public QuyMoSinhVienController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {
        }

        /// <summary>
        /// Lấy danh sách quy mô sinh viên theo điều kiện lọc
        /// </summary> 
        /// <param name="filter"><PERSON>i<PERSON><PERSON> kiện lọc</param>
        /// <returns>Danh sách quy mô sinh viên</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<QuyMoSinhVienBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "QUY_MO_SINH_VIEN_VIEW")]
        public async Task<IActionResult> Filter([FromBody] QuyMoSinhVienQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Lấy danh sách claims của người dùng từ http context
                string[] permission = HttpContext.User.Claims?.FirstOrDefault(c => c.Type == ClaimConstants.PERMISSTTIONS)?.Value?.Split(',');

                //Nếu người dùng không có quyền view all data thì cập nhật Id khoa về thành thông tin khoa hiện tại của người dùng
                if (!permission.Any(x => x == "VIEW_ALL_DATA"))
                {
                    var user = await _mediator.Send(new GetUserByIdQuery(u.UserId));
                    if (user != null)
                    {
                        filter.IDKhoa = user.IdKhoa;
                    }
                }
                return await _mediator.Send(new GetQuyMoSinhVienByFilterQuery(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách tổng hợp quy mô theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách tổng hợp quy mô</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("general")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TongHopQuyMoModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "TONG_HOP_QUY_MO_SINH_VIEN_VIEW")]
        public async Task<IActionResult> Summary([FromBody] QuyMoSinhVienQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetTongHopQuyMoByFilterQuery(filter, u.SystemLog));
            });
        }

    }
}
