using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.Tokens;
using Core.Business;
using Core.DataLog;
using Core.Shared;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text.Json.Serialization;
using UAParser;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace Core.API.Shared
{
    public static class Helper
    {
        public const string ErrorMessage_TokenExpired = "TokenExpired";
        public const string ErrorMessage_Unauthorized = "Unauthorized";
        public const string ErrorMessage_IncorrectIssuer = "IncorrectIssuer";
        public const string ErrorMessage_IncorrectInput = "IncorrectInput";

        public static string GetValueFromJWTTokenByKey(string token, string key)
        {
            try
            {
                var handerJwt = new JwtSecurityTokenHandler();
                if (token != null)
                {
                    // Get the token
                    token = token.Trim();
                    var tokenInfo = handerJwt.ReadJwtToken((string)token);
                    var consumerKey = tokenInfo.Claims.FirstOrDefault(x => x.Type == key)?.Value;
                    if (string.IsNullOrEmpty(consumerKey))
                    {
                        consumerKey = "";
                    }
                    return consumerKey;
                }
                else
                {
                    return string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Transform data to http response
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static ActionResult TransformData(Response data)
        {
            var result = new ObjectResult(data) { StatusCode = (int)data.Code };
            return result;
        }

        public static async Task<RequestUser> GetRequestInfo(HttpRequest request)
        {
            var claims = request.HttpContext.User.Claims;

            // Lấy thông tin UserId và OrganizationId từ Claims
            var userIdClaims = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            int userId = 0;
            int.TryParse(userIdClaims, out userId);

            string language = request.Headers["X-Language"];

            var requestUser = new RequestUser()
            {
                UserId = userId,
                UserName = claims.GetStringClaim(ClaimTypes.Name),
                Language = string.IsNullOrEmpty(language) ? LanguageConstant.VI : language,
                TraceId = Guid.NewGuid().ToString()
            };

            #region SystemLog
            // Lấy thông tin thiết bị truy cập từ Header
            string userAgent = request.HttpContext.Request.Headers["User-Agent"].ToString();
            string requestMethod = request.HttpContext.Request.Method;
            string requestPath = request.HttpContext.Request.Path;

            var parser = Parser.GetDefault();
            var clientInfo = parser.Parse(userAgent);

            // Lấy thông tin tọa độ từ Header
            Location locationObj = new Location();
            string locationBase64 = request.Headers["location"];
            if (!string.IsNullOrEmpty(locationBase64))
            {
                try
                {
                    string location = Utils.Base64Decode(locationBase64);
                    locationObj = JsonSerializer.Deserialize<Location>(location);
                }
                catch (Exception)
                {
                    // Không thể convert từ Base64 sang Object
                }
            }

            requestUser.SystemLog = new SystemLogModel()
            {
                TraceId = requestUser.TraceId,
                CorrelationId = request.Headers["X-Correlation-Id"],
                DeviceId = request.Headers["X-Device-Id"],
                ClientIP = GetIPAddress(request),
                UserAgent = userAgent,
                RequestMethod = requestMethod,
                RequestPath = requestPath,
                Os = clientInfo.OS.ToString(),
                Browser = clientInfo.UA.ToString(),
                ClientInfo = clientInfo.ToString(),
                Location = new Location()
                {
                    Latitude = locationObj?.Latitude,
                    Longitude = locationObj?.Longitude,
                },
                UserName = requestUser.UserName,
                CreatedDate = DateTime.Now,
                UserId = requestUser.UserId.ToString(),
                ListAction = new List<ActionDetail>()
            };
            #endregion

            return GetRequestInfo(claims, requestUser);
        }

        public static RequestUser GetRequestInfo(IEnumerable<Claim> claims, RequestUser request)
        {
            var result = new RequestUser()
            {
                TraceId = request.TraceId,
                GiaoVienId = request.GiaoVienId,
                Language = request.Language,
                MaCB = request.MaCB,
                SystemLog = request.SystemLog ?? new SystemLogModel()
                {
                    TraceId = request.TraceId,
                    CreatedDate = DateTime.Now,
                    ListAction = new List<ActionDetail>()
                },
                UserId = request != null ? request.UserId : claims.GetIntClaim(ClaimConstants.USER_ID)
            };

            return result;
        }

        public static string GetIPAddress(HttpRequest request)
        {
            // Lấy thông tin cấu hình từ config
            var config = request.HttpContext.RequestServices.GetService<IConfiguration>();
            string ipHeader = config["AppSettings:IPHeader"] ?? string.Empty;
            string clientIP = string.Empty;
            switch (ipHeader)
            {
                case "X-Forwarded-For":
                    clientIP = request.Headers["X-Forwarded-For"].ToString();
                    break;
                case "X-Real-Ip":
                    clientIP = request.Headers["X-Real-Ip"].ToString();
                    break;
                default:
                    clientIP = request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? string.Empty;
                    break;
            }

            return clientIP;
        }

        private static string GetStringClaim(this IEnumerable<Claim> claims, string key)
        {
            var obj = claims.FirstOrDefault(x => x.Type == key);
            if (obj != null)
                return obj.Value;
            return string.Empty;
        }

        private static int GetIntClaim(this IEnumerable<Claim> claims, string key)
        {
            var obj = claims.FirstOrDefault(x => x.Type == key);
            int rs = 0;
            if (obj != null && !string.IsNullOrEmpty(obj.Value))
                int.TryParse(obj.Value, out rs);
            return rs;
        }

        private static Guid? GetGuidClaim(this IEnumerable<Claim> claims, string key)
        {

            var obj = claims.FirstOrDefault(x => x.Type == key);
            if (obj != null && !string.IsNullOrEmpty(obj.Value) && Utils.IsGuid(obj.Value))
                return new Guid(obj.Value);
            return null;
        }

        private static DateTime? GetDateClaim(this IEnumerable<Claim> claims, string key)
        {
            var obj = claims.FirstOrDefault(x => x.Type == key);
            if (obj != null && !string.IsNullOrEmpty(obj.Value))
                return DateTime.Parse(obj.Value);
            return null;
        }

        private static List<string> GetListStringClaim(this IEnumerable<Claim> claims, string key)
        {
            var obj = claims.FirstOrDefault(x => x.Type == key);
            return obj != null && !string.IsNullOrEmpty(obj.Value) ? JsonSerializer.Deserialize<List<string>>(obj.Value) : null;
        }

        private static List<Guid> GetListGuidClaim(this IEnumerable<Claim> claims, string key)
        {
            var obj = claims.FirstOrDefault(x => x.Type == key);
            return obj != null && !string.IsNullOrEmpty(obj.Value) ? JsonSerializer.Deserialize<List<Guid>>(obj.Value) : null;
        }

        public static bool ValidateTokenByJWKS(string token, string jwksKeyBase64)
        {
            try
            {
                string jwksKeyString = Utils.Base64Decode(jwksKeyBase64);
                JWKSKeyModel keyModel = JsonSerializer.Deserialize<JWKSKeyModel>(jwksKeyString);

                return ValidateTokenByJWKS(token, keyModel);
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool ValidateTokenByJWKS(string token, JWKSKeyModel keyModel)
        {
            try
            {
                string[] tokenParts = token.Split('.');

                RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
                rsa.ImportParameters(
                  new RSAParameters()
                  {
                      Modulus = JwksFromBase64Url(keyModel.N),
                      Exponent = JwksFromBase64Url(keyModel.E)
                  });

                SHA256 sha256 = SHA256.Create();
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(tokenParts[0] + '.' + tokenParts[1]));

                RSAPKCS1SignatureDeformatter rsaDeformatter = new RSAPKCS1SignatureDeformatter(rsa);
                rsaDeformatter.SetHashAlgorithm("SHA256");
                if (rsaDeformatter.VerifySignature(hash, JwksFromBase64Url(tokenParts[2])))
                    return true;
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private static byte[] JwksFromBase64Url(string base64Url)
        {
            string padded = base64Url.Length % 4 == 0
                ? base64Url : base64Url + "====".Substring(base64Url.Length % 4);
            string base64 = padded.Replace("_", "/")
                                  .Replace("-", "+");
            return Convert.FromBase64String(base64);
        }
    }

    public class TimeSpanToStringConverter : JsonConverter<TimeSpan>
    {
        public override TimeSpan Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            return TimeSpan.Parse(value);
        }

        public override void Write(Utf8JsonWriter writer, TimeSpan value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }
}
