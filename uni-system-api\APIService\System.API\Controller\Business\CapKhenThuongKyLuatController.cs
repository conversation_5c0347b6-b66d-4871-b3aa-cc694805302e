﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/cap-khen-thuong-ky-luat")]
    [ApiExplorerSettings(GroupName = "29. Cấp khen thưởng kỷ luật")]
    [Authorize]
    public class CapKhenThuongKyLuatController : ApiControllerBase
    {
        public CapKhenThuongKyLuatController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách cấp khen thưởng kỷ luật cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<CapKhenThuongKyLuatSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxCapKhenThuongKyLuatQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách cấp khen thưởng kỷ luật có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<CapKhenThuongKyLuatBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_VIEW))]
        public async Task<IActionResult> Filter([FromBody] CapKhenThuongKyLuatFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterCapKhenThuongKyLuatQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết cấp khen thưởng kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<CapKhenThuongKyLuatModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetCapKhenThuongKyLuatByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới cấp khen thưởng kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateCapKhenThuongKyLuatModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE;


                return await _mediator.Send(new CreateCapKhenThuongKyLuatCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel cấp khen thưởng kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyCapKhenThuongKyLuatModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE_MANY;


                return await _mediator.Send(new CreateManyCapKhenThuongKyLuatCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa cấp khen thưởng kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateCapKhenThuongKyLuatModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_UPDATE;
                return await _mediator.Send(new UpdateCapKhenThuongKyLuatCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa cấp khen thưởng kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CAP_KHEN_THUONG_KY_LUAT_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CAP_KHEN_THUONG_KY_LUAT_DELETE;

                return await _mediator.Send(new DeleteCapKhenThuongKyLuatCommand(id, u.SystemLog));
            });
        }

    }
}
