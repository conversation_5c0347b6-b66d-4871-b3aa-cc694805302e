﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hoc-vi")]
    [ApiExplorerSettings(GroupName = "18. Học Vị")]
    [Authorize]
    public class HocViController : ApiControllerBase
    {
        public HocViController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách học vị cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HocViSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHocViQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách học vị có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HocViBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HocViFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHocViQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết học vị
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HocViModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocViByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới học vị
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHocViModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_VI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_VI_CREATE;


                return await _mediator.Send(new CreateHocViCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel học vị
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHocViModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_VI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_VI_CREATE_MANY;


                return await _mediator.Send(new CreateManyHocViCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa học vị
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHocViModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_VI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_VI_UPDATE;
                return await _mediator.Send(new UpdateHocViCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa học vị
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_VI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_VI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_VI_DELETE;

                return await _mediator.Send(new DeleteHocViCommand(id, u.SystemLog));
            });
        }

    }
}

