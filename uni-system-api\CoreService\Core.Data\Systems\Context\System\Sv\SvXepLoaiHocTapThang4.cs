﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepLoaiHocTap")]
    public class SvXepLoaiHocTap
    {
        
        public SvXepLoaiHocTap()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai")]
        public int IdXepLoai { get; set; }

        [Column("Xep_loai"), MaxLength(50)]
        public string XepLoai { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Xep_loai_en"), MaxLength(50)]
        public string XepLoaiEn { get; set; }

        [Column("Ma_xep_loai"), <PERSON><PERSON>eng<PERSON>(20)]
        public string MaXepLoai { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }
    }
}
