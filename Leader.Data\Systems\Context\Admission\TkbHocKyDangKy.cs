﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Leader.Data
{
    [Table("tkbHocKyDangKy")]
    public class TkbHocKyDangKy
    {
        public TkbHocKyDangKy()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("Ky_dang_ky")]
        public int IdKy { get; set; }

        [Column("Dot")]
        public int Dot { get; set; }

        [Column("Hoc_ky")]
        public int HocKy { get; set; }

        [Column("Nam_hoc"), MaxLength(10)]
        public string NamHoc { get; set; }

        [Column("Tu_ngay")]
        public DateTime TuNgay { get; set; }

        [Column("Den_ngay")]
        public DateTime? DenNgay { get; set; }

        [Column("Chon_dang_ky")]
        public bool ChonDangKy { get; set; }

        [Column("Khoa_TKB")]
        public bool KhoaTKB { get; set; }

    }
}
