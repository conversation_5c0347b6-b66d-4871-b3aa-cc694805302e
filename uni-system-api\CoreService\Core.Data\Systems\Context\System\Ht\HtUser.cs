﻿using System;
using System.Collections.Generic;

namespace Core.Data
{
    public partial class HtUser
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string PassWord { get; set; }
        public string FullName { get; set; }
        public int UserGroup { get; set; }
        public int Active { get; set; }
        public int IdPhong { get; set; }
        public int IdKhoa { get; set; }
        public int IdBomon { get; set; }
        public string MayTram { get; set; }
        public string UserNameLdap { get; set; }
        public string AdsPathLdap { get; set; }
        public string Email { get; set; }
        public string DienThoai { get; set; }
        public string Mac { get; set; }
        public bool Supervisor { get; set; }
        public string TenDangNhapHoaDon { get; set; }
        public string MatKhauHoaDon { get; set; }
        public string TenDangNhapServiceHoaDon { get; set; }
        public string MatKhauServiceHoaDon { get; set; }
        public string LinkPublishService { get; set; }
        public string LinkPortalService { get; set; }
        public string LinkBusinessService { get; set; }
        public string PatternVnpt { get; set; }
        public string SerialVnpt { get; set; }
        public string LinkHoaDon { get; set; }
        public int? IdPhongBan { get; set; }
        public string IdPhongBanAccessList { get; set; }
        public string MaCanBoUser { get; set; }
    }
}
