﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetFilterMucHuongBhytQuery : IRequest<PaginationList<MucHuongBhytBaseModel>>
    {
        public MucHuongBhytFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách mức hưởng bhyt có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterMucHuongBhytQuery(MucHuongBhytFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterMucHuongBhytQuery, PaginationList<MucHuongBhytBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<MucHuongBhytBaseModel>> Handle(GetFilterMucHuongBhytQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvMucHuongBhyts
                            select new MucHuongBhytBaseModel
                            {
                                IdMucHuongBhyt = dt.IdMucHuongBhyt,
                                KyHieu = dt.KyHieu,
                                DoiTuongMucHuong = dt.DoiTuongMucHuong,
                                MucHuong = dt.MucHuong

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.KyHieu.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<MucHuongBhytBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
