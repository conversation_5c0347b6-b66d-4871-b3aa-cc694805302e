﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepLoaiTotNghiepThang4Command : IRequest<Unit>
    {
        public CreateXepLoaiTotNghiepThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepLoaiTotNghiepThang4Command(CreateXepLoaiTotNghiepThang4Model model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepLoaiTotNghiepThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepLoaiTotNghiepThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepLoaiTotNghiepThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepLoaiTotNghiepThang4Model, SvXepHangTotNghiep>(model);

                var checkCode = await _dataContext.SvXepHangTotNghieps.AnyAsync(x => x.XepHang == entity.XepHang && x.IdHe == entity.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed", entity.XepHang.ToString()]}");
                }
                var checkCodeMaXepHang = await _dataContext.SvXepHangTotNghieps.AnyAsync(x => x.MaXepHang == entity.MaXepHang && x.IdHe == entity.IdHe);
                if (checkCodeMaXepHang)
                {
                    throw new ArgumentException($"{_localizer["MaXepHang.Existed", entity.MaXepHang.ToString()]}");
                }

                await _dataContext.SvXepHangTotNghieps.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepLoaiTotNghiepThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp loại tốt nghiệp thang 4: {entity.XepHang}",
                    ObjectCode = XepLoaiTotNghiepThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepHang .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepLoaiTotNghiepThang4Command : IRequest<Unit>
    {
        public CreateManyXepLoaiTotNghiepThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepLoaiTotNghiepThang4Command(CreateManyXepLoaiTotNghiepThang4Model model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepLoaiTotNghiepThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepLoaiTotNghiepThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepLoaiTotNghiepThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXepHangAdd = model.listXepLoaiTotNghiepThang4Models.Select(x => x.XepHang).ToList();
                var listMaXepHangAdd = model.listXepLoaiTotNghiepThang4Models.Select(x => x.MaXepHang).ToList();
                var listIdHeAdd = model.listXepLoaiTotNghiepThang4Models.Select(x => x.IdHe).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiTotNghiepThang4Model, SvXepHangTotNghiep>(model);

                // Check data duplicate
                if (listXepHangAdd.Count() != listXepHangAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepHangTotNghieps.AnyAsync(x => listXepHangAdd.Contains(x.XepHang))
                       && await _dataContext.SvXepHangTotNghieps.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed"]}");
                }
                if (await _dataContext.SvXepHangTotNghieps.AnyAsync(x => listMaXepHangAdd.Contains(x.MaXepHang))
                      && await _dataContext.SvXepHangTotNghieps.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                {
                    throw new ArgumentException($"{_localizer["MaXepHang.Existed"]}");
                }


                var listEntity = model.listXepLoaiTotNghiepThang4Models.Select(x => new SvXepHangTotNghiep()
                {
                    XepHang = x.XepHang,
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    TuDiemThang10 = x.TuDiemThang10,
                    DenDiemThang10 = x.DenDiemThang10,
                    MaXepHang = x.MaXepHang,
                    XepHangEn = x.XepHangEn,
                    IdHe = x.IdHe,
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepHang ).ToList();

                Log.Information($"Create many {XepLoaiTotNghiepThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp loại tốt nghiệp thang 4 từ file excel",
                    ObjectCode = XepLoaiTotNghiepThang4Constant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepLoaiTotNghiepThang4Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiTotNghiepThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiTotNghiepThang4Command(int id, UpdateXepLoaiTotNghiepThang4Model model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiTotNghiepThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiTotNghiepThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiTotNghiepThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepHangTotNghieps.FirstOrDefaultAsync(dt => dt.IdXepHang  == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               

                var checkCode = await _dataContext.SvXepHangTotNghieps.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.XepHang == model.XepHang && x.IdHe == model.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed", model.XepHang.ToString()]}");
                }

                var checkCodeMaXepHang = await _dataContext.SvXepHangTotNghieps.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.MaXepHang == model.MaXepHang && x.IdHe == model.IdHe);
                if (checkCodeMaXepHang)
                {
                    throw new ArgumentException($"{_localizer["MaXepHang.Existed", model.MaXepHang.ToString()]}");
                }


                Log.Information($"Before Update {XepLoaiTotNghiepThang4Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepHangTotNghieps.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiTotNghiepThang4Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiTotNghiepThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp loại tốt nghiệp thang 4: {entity.XepHang}",
                    ObjectCode = XepLoaiTotNghiepThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepHang .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey(entity.IdXepHang .ToString()));
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiTotNghiepThang4Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiTotNghiepThang4Command(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiTotNghiepThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiTotNghiepThang4Command request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiTotNghiepThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepHangTotNghieps.FirstOrDefaultAsync(x => x.IdXepHang  == id);

                _dataContext.SvXepHangTotNghieps.Remove(entity);

                Log.Information($"Delete {XepLoaiTotNghiepThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp loại tốt nghiệp thang 4: {entity.XepHang}",
                    ObjectCode = XepLoaiTotNghiepThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepHang .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey());
                _cacheService.Remove(XepLoaiTotNghiepThang4Constant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
