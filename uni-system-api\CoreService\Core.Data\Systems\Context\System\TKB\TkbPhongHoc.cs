﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nest;

namespace Core.Data
{
    [Table("tkbPhongHoc")]
    public class TkbPhongHoc
    {

        public TkbPhongHoc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phong")]
        public int IdPhong { get; set; }

        [Column("ID_co_so")]
        public int? IdCoSo { get; set; }

        [Column("ID_nha")]
        public int? IdNha { get; set; }

        [Column("ID_tang")]
        public int? IdTang { get; set; }

        [Column("So_phong"), MaxLength(50)]
        public string SoPhong { get; set; }

        [Column("Loai_phong"), MaxLength(200)]
        public string <PERSON><PERSON><PERSON>hong { get; set; }

        [Column("So_ban")]
        public int? SoBan { get; set; }

        [Column("So_sv_mot_ban")]
        public int? SoSvMotBan { get; set; }

        [Column("Suc_chua")]
        public int? SucChua { get; set; }

        [Column("Suc_chua_thi")]
        public int? SucChuaThi { get; set; }

        [Column("Am_thanh")]
        public bool? AmThanh { get; set; }

        [Column("May_tinh")]
        public bool? MayTinh { get; set; }

        [Column("Tivi")]
        public bool? TiVi { get; set; }

        [Column("May_chieu")]
        public bool? MayChieu { get; set; }

        [Column("So_sv")]
        public int? SoSv { get; set; }

        [Column("ID_loai_phong")]
        public int? IdLoaiPhong { get; set; }

        [Column("Thiet_bi"), MaxLength(1000)]
        public string ThietBi { get; set; }

        [Column("Khong_ToChucThi")]
        public int KhongToChucThi { get; set; }

        [Column("Ghi_chu"), MaxLength(100)]
        public string GhiChu { get; set; }

        [Column("Trung_phong")]
        public bool? TrungPhong { get; set; }

        [Column("ID_khoa")]
        public int? IdKhoa { get; set; }




    }
}
