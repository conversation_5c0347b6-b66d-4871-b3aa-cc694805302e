﻿using System;
using System.Data;

namespace Core.Business
{
    public interface ICallStoreHelper
    {
        DataTable CallStoreChuongTrinhDaoTaoChiTietLoadByIdDaoTaoAsync(int IdDaoTao);
        DataTable CallStoreWithStartAndEndDateAsync(string storeName, DateTime startDate, DateTime endDate);
        DataTable CallStoreLoginAsync(string userName, string password);
        DataTable CallStoresvLopGetByUserName(string userName);
        int MonHocInsert(string kyHieu, string tenMon, string tenTiengAnh, int idBm, int idHe, int idNhomHp);


        /// <summary>
        /// Lấy danh sách bộ môn
        /// </summary>
        /// <returns></returns>
        DataTable CallStoreDanhSachBoMonAsync(int IdMon, int IdCb);

        /// <summary>
        /// Lấy danh sách Giảng viên đã gán bộ môn
        /// </summary>
        /// /// <param name="IdBm"></param>
        /// <returns></returns>
        DataTable CallStoreDanhSachGiangVienTheoBoMonAsync(int IdBm);

        /// <summary>
        /// Lấy danh sách giảng viên chưa gán bộ môn
        /// </summary>
        /// <returns></returns>
        DataTable CallStoreDanhSachGiangVienChuaGanBoMonAsync(int IdBm);


        /// <summary>
        /// Lấy danh sách môn học đã gán bộ môn
        /// </summary>
        /// /// <param name="IdBm"></param>
        /// <returns></returns>
        DataTable CallStoreDanhSachMonHocTheoBoMonAsync(int IdBm);

        /// <summary>
        /// Thêm giang viên thuộc bộ môn
        /// </summary>
        /// /// <param name="IdBm"></param>
        /// /// <param name="IdCb"></param>
        /// <returns></returns>
        DataTable CallStoreCreateGiangVienTheoBoMonAsync(int IdBm, int IdCb);



        /// <summary>
        /// Xóa giảng viên thuộc bộ môn
        /// </summary>
        /// /// <param name="IdBm"></param>
        /// /// <param name="IdCb"></param>
        /// <returns></returns>
        DataTable CallStoreDeleteGiangVienTheoBoMonAsync(int IdBm, int IdCb);

        /// <summary>
        /// Lấy số lượng môn CTĐT có điểm
        /// </summary>
        /// /// <param name="idDt"></param>
        /// /// <param name="idMon"></param>
        /// <returns></returns>
        DataTable CallStoreCheckDiemCtdtAsync(int idDt, int idMon);
    }
}
