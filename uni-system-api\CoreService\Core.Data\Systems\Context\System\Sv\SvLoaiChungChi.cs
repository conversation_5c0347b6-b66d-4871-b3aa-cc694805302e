﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiChungChi")]
    public class SvLoaiChungChi
    {
        
        public SvLoaiChungChi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_chung_chi")]
        public int IdChungChi { get; set; }

        [Column("Ky_hieu"), MaxLength(20)]
        public string <PERSON>yH<PERSON> { get; set; }

        [Column("Loai_chung_chi"), MaxLength(200)]
        public string <PERSON>ai<PERSON>hung<PERSON><PERSON> { get; set; }

        [Column("ID_nhom_chung_chi")]
        public float IdNhomChungChi { get; set; }

        [Column("Cap_do_chung_chi")]
        public int? CapDoChungChi { get; set; }
    }
}
