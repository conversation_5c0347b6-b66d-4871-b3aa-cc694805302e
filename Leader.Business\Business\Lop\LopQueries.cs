﻿using Core.Business;
using Core.Data;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class GetComboboxKhoaHocQuery : IRequest<List<KhoaHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy khóa học cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxKhoaHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxKhoaHocQuery, List<KhoaHocSelectItemModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(LeaderReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<KhoaHocSelectItemModel>> Handle(GetComboboxKhoaHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;

                string cacheKey = LopConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var rs = await _dataContext.SvLops.GroupBy(test => test.KhoaHoc).Select(grp => grp.First()).ToListAsync();
                    var data = (from dt in rs
                                select new KhoaHocSelectItemModel()
                                {
                                    KhoaHoc = dt.KhoaHoc
                                });

                    return data.ToList();
                });

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
