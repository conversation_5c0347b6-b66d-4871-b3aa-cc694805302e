﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Core.Shared;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Core.DataLog
{
    [BsonIgnoreExtraElements]
    public class SystemLog
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("correlation_id")]
        public string CorrelationId { get; set; }

        [BsonElement("trace_id")]
        public string TraceId { get; set; }

        [BsonElement("device_id")]
        public string DeviceId { get; set; }

        [BsonElement("user_id")]
        public string UserId { get; set; }

        [BsonElement("user_name")]
        public string UserName { get; set; }

        [BsonElement("action_code")]
        public string ActionCode { get; set; }

        [BsonElement("action_name")]
        public string ActionName { get; set; }

        [BsonElement("client_ip")]
        public string ClientIP { get; set; }

        [BsonElement("created_date")]
        [BsonRepresentation(BsonType.DateTime)]
        public DateTime CreatedDate { get; set; }

        [BsonElement("object_code")]
        public string ObjectCode { get; set; }

        [BsonElement("object_id")]
        public string ObjectId { get; set; }

        [BsonElement("request_method")]
        public string RequestMethod { get; set; }

        [BsonElement("request_path")]
        public string RequestPath { get; set; }

        [BsonElement("user_agent")]
        public string UserAgent { get; set; }

        [BsonElement("os")]
        public string Os { get; set; }

        [BsonElement("browser")]
        public string Browser { get; set; }

        [BsonElement("client_info")]
        public string ClientInfo { get; set; }

        [BsonElement("location")]
        public Location Location { get; set; }

        [BsonElement("description")]
        public string Description { get; set; }

        [BsonElement("time_execution")]
        public long TimeExecution { get; set; }

        [BsonElement("meta_data")]
        [JsonIgnore]
        public string MetaData { get; set; }
    }
}
