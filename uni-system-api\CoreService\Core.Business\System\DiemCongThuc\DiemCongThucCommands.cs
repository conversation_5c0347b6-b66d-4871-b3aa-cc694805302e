﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDiemCongThucCommand : IRequest<Unit>
    {
        public CreateDiemCongThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDiemCongThucCommand(CreateDiemCongThucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDiemCongThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDiemCongThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DiemCongThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDiemCongThucModel, SvDiemCongThuc>(model);

                var checkCode = await _dataContext.SvDiemCongThucs.AnyAsync(x => x.TenCongThuc == entity.TenCongThuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemCongThuc.Existed", entity.TenCongThuc.ToString()]}");
                }

                await _dataContext.SvDiemCongThucs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DiemCongThucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới điểm công thức: {entity.TenCongThuc}",
                    ObjectCode = DiemCongThucConstant.CachePrefix,
                    ObjectId = entity.IdCongThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDiemCongThucCommand : IRequest<Unit>
    {
        public CreateManyDiemCongThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDiemCongThucCommand(CreateManyDiemCongThucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDiemCongThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDiemCongThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DiemCongThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDiemCongThucAdd = model.listDiemCongThucModels.Select(x => x.TenCongThuc).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDiemCongThucModel, SvDiemCongThuc>(model);
              
                // Check data exits DB
                if (await _dataContext.SvDiemCongThucs.AnyAsync(x => listDiemCongThucAdd.Contains(x.TenCongThuc)))
                {
                    throw new ArgumentException($"{_localizer["DiemCongThuc.Existed"]}");
                }

                var listEntity = model.listDiemCongThucModels.Select(x => new SvDiemCongThuc()
                {
                    IdCongThuc = x.IdCongThuc,
                    TenCongThuc = x.TenCongThuc,
                    CongThucTinhDiemTBCBP = x.CongThucTinhDiemTBCBP,
                    CongThucTinhDiemTBCHP = x.CongThucTinhDiemTBCHP
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdCongThuc).ToList();

                Log.Information($"Create many {DiemCongThucConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import công thức điểm từ file excel",
                    ObjectCode = DiemCongThucConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDiemCongThucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDiemCongThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDiemCongThucCommand(int id, UpdateDiemCongThucModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDiemCongThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDiemCongThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DiemCongThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDiemCongThucs.FirstOrDefaultAsync(dt => dt.IdCongThuc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvDiemCongThucs.AnyAsync(x => x.TenCongThuc == model.TenCongThuc  && x.IdCongThuc != model.IdCongThuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DiemCongThuc.Existed", model.TenCongThuc.ToString()]}");
                }

                Log.Information($"Before Update {DiemCongThucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDiemCongThucs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DiemCongThucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DiemCongThucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật công thức điểm: {entity.TenCongThuc}",
                    ObjectCode = DiemCongThucConstant.CachePrefix,
                    ObjectId = entity.IdCongThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey(entity.IdCongThuc.ToString()));
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDiemCongThucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDiemCongThucCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDiemCongThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDiemCongThucCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DiemCongThucConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDiemCongThucs.FirstOrDefaultAsync(x => x.IdCongThuc == id);

                _dataContext.SvDiemCongThucs.Remove(entity);

                Log.Information($"Delete {DiemCongThucConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa công thức điểm: {entity.TenCongThuc}",
                    ObjectCode = DiemCongThucConstant.CachePrefix,
                    ObjectId = entity.IdCongThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey());
                _cacheService.Remove(DiemCongThucConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
