﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetEmailTemplateByIdQuery : IRequest<EmailTemplateModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin mẫu email theo id
        /// </summary>
        /// <param name="id">Id mẫu email</param>
        public GetEmailTemplateByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetEmailTemplateByIdQuery, EmailTemplateModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<EmailTemplateModel> Handle(GetEmailTemplateByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = EmailTemplateConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.EmailTemplates.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<EmailTemplate, EmailTemplateModel>(entity);
                });
                return item;
            }
        }
    }
}
