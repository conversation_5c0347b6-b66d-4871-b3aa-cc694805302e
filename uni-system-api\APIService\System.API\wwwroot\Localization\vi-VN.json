{"redis.test.label": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i redis", "redis.test.current-time": "<PERSON><PERSON><PERSON><PERSON> gian hiện tại", "redis.test.cache-time": "<PERSON>h<PERSON><PERSON> gian l<PERSON>u cache", "data.not-found": "<PERSON><PERSON> liệu không tồn tại", "message.error-500": "<PERSON><PERSON> lỗi xảy ra", "message.error-500-minimum": "<PERSON><PERSON> ra, li<PERSON><PERSON> hệ quản trị hệ thống để biết thêm chi tiết", "system-application.code.existed": "Mã ứng dụng này đã tồn tại", "InsertData.Duplicate": "<PERSON><PERSON> liệu thêm mới bị trùng lặp", "ChucDanh.Existed": "<PERSON><PERSON><PERSON> danh này đã tồn tại", "ChucDanh.IdChucDanh.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdChucDanh", "ChucDanh.MaChucDanh.MaxLength(10)": "MaChucDanh không đư<PERSON>c quá 10 ký tự", "ChucDanh.MaChucDanh.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "ChucDanh.ChucDanh.MaxLength(10)": "ChucDan<PERSON> không đư<PERSON>c quá 100 ký tự", "ChucDanh.ChucDanh.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "GioiTinh.Existed": "<PERSON><PERSON>ớ<PERSON> tính này đã tồn tại", "GioiTinh.IdGioiTinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>yền IdGioiTinh", "GioiTinh.GioiTinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>yền GioiTinh", "GioiTinh.GioiTinh.MaxLength(3)": "GioiTinh không đư<PERSON><PERSON> quá 3 ký tự", "He.Existed": "<PERSON><PERSON> này đã tồn tại", "He.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "He.MaHe.MaxLength(50)": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "He.MaHe.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "He.TenHe.MaxLength(50)": "TenHe không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "He.TenHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenHe", "He.TenHeEn.MaxLength(50)": "TenHeEn không đư<PERSON><PERSON> quá 50 ký tự", "He.TenBacDaoTao.MaxLength(50)": "TenBacDaoTao không được quá 50 ký tự", "He.TenBacDaoTao.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenBacDaoTao", "He.TenBacDaoTaoEn.MaxLength(50)": "TenBacDaoTaoEn không được quá 50 ký tự", "He.TenBacDaoTaoEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenBacDaoTaoEn", "He.HinhThucDaoTao.MaxLength(50)": "HinhThucDaoTao không được quá 50 ký tự", "He.HinhThucDaoTao.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n HinhThucDaoTao", "He.HinhThucDaoTaoEn.MaxLength(50)": "HinhThucDaoTaoEn không được quá 50 ký tự", "He.HinhThucDaoTaoEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>n HinhThucDaoTaoEn", "He.SttTrinhDo.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n SttTrinhDo", "HocHam.Existed": "<PERSON><PERSON><PERSON> hàm này đã tồn tại", "HocHam.IdHocHam.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdHocHam", "HocHam.MaHocHam.MaxLength(10)": "MaHocHam không đư<PERSON><PERSON> quá 10 ký tự", "HocHam.MaHocHam.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MaHoc<PERSON>am", "HocHam.HocHam.MaxLength(100)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON>c quá 100 ký tự", "HocHam.HocHam.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HocVi.Existed": "<PERSON><PERSON><PERSON> vị này đã tồn tại", "HocVi.IdHocVi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHocVi", "HocVi.MaHocVi.MaxLength(10)": "MaHocVi không đư<PERSON> quá 10 ký tự", "HocVi.MaHocVi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>ền MaHocVi", "HocVi.HocVi.MaxLength(100)": "HocVi không được quá 100 ký tự", "HocVi.HocVi.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "QuocTich.Existed": "<PERSON><PERSON><PERSON><PERSON> tịch này đã tồn tại", "QuocTich.IdQuocTich.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdQuoc<PERSON>ich", "QuocTich.MaQuocTich.MaxLength(10)": "MaQuocTich không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "QuocTich.MaQuocTich.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "QuocTich.QuocTich.MaxLength(50)": "<PERSON><PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "QuocTich.QuocTich.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "TonGiao.Existed": "Tôn gi<PERSON>o này đã tồn tại", "TonGiao.IdTonGiao.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdTonGiao", "TonGiao.MaTonGiao.MaxLength(5)": "MaTonGiao không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "TonGiao.MaTonGiao.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>on<PERSON>", "TonGiao.TonGiao.MaxLength(50)": "TonG<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "TonGiao.TonGiao.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "TonGiao.TonGiaoEn.MaxLength(50)": "TonGiaoEn không đư<PERSON>c quá 50 ký tự", "TonGiao.TonGiaoEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TonGiaoEn", "DanToc.Existed": "<PERSON><PERSON> tộc này đã tồn tại", "DanToc.IdDanToc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdDanToc", "DanToc.MaDanToc.MaxLength(5)": "MaDanToc không đư<PERSON><PERSON> quá 5 ký tự", "DanToc.MaDanToc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Ma<PERSON>anToc", "DanToc.DanToc.MaxLength(50)": "DanToc không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "DanToc.DanToc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "DanToc.DanTocEn.MaxLength(50)": "DanTocEn không đư<PERSON><PERSON> quá 50 ký tự", "DanToc.DanTocEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n DanTocEn", "Khoa.Existed": "<PERSON><PERSON><PERSON> này đã tồn tại", "Khoa.IdKhoa.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdKhoa", "Khoa.MaKhoa.MaxLength(5)": "MaKhoa không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "Khoa.MaKhoa.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "Khoa.TenKhoa.MaxLength(50)": "TenKhoa không đư<PERSON><PERSON> quá 50 ký tự", "Khoa.TenKhoa.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "Khoa.TenKhoaEn.MaxLength(50)": "TenKhoaEn không đư<PERSON><PERSON> quá 50 ký tự", "Khoa.TenKhoaEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenKhoaEn", "Nganh.Existed": "ng<PERSON>nh này đã tồn tại", "Nganh.IdNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdNganh", "Nganh.MaNganh.MaxLength(20)": "MaNganh không đư<PERSON><PERSON> quá 20 ký tự", "Nganh.MaNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "Nganh.TenNganh.MaxLength(200)": "TenNganh không đư<PERSON>c quá 200 ký tự", "Nganh.TenNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "Nganh.TenNganhEn.MaxLength(200)": "TenNganhEn không được quá 200 ký tự", "Nganh.TenNganhEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenNganhEn", "Nganh.SuPham.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "Huyen.Existed": "Huyện này đã tồn tại", "Huyen.IdHuyen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "Huyen.IdHuyen.MaxLength(5)": "<PERSON><PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "Huyen.IdTinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdT<PERSON>h", "Huyen.IdTinh.MaxLength(5)": "Id<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "Huyen.TenHuyen.MaxLength(50)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON><PERSON> quá 50 ký tự", "Huyen.TenHuyen.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "Huyen.TenHuyenEn.MaxLength(50)": "TenHuyenEn không đư<PERSON>c quá 50 ký tự", "Huyen.IdHuyenCu.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n IdHuyen<PERSON>u", "Huyen.IdHuyenCu.MaxLength(5)": "IdHuyenCu không đư<PERSON><PERSON> quá 5 ký tự", "Huyen.IdHuyenCu1.NotRequire": "<PERSON><PERSON>t bu<PERSON><PERSON> ph<PERSON>i truyền IdHuyenCu1", "Huyen.IdHuyenCu1.MaxLength(5)": "IdHuyenCu1 không đư<PERSON>c quá 5 ký tự", "Huyen.TenHuyenCu.MaxLength(50)": "TenHuyenCu không đư<PERSON>c quá 50 ký tự", "Tinh.Existed": "Tỉnh này đã tồn tại", "Tinh.IdTinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdT<PERSON>h", "Tinh.IdTinh.MaxLength(5)": "Id<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "Tinh.TenTinh.MaxLength(50)": "TenT<PERSON>h không đư<PERSON><PERSON> quá 50 ký tự", "Tinh.TenTinh.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenTinh", "Tinh.TenTinhEn.MaxLength(50)": "TenTinhEn không đư<PERSON><PERSON> quá 50 ký tự", "Tinh.TenTinhEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenTinhEn", "ChuyenNganh.Existed": "<PERSON><PERSON><PERSON><PERSON> ngành này đã tồn tại", "ChuyenNganh.IdChuyenNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdChuyenNganh", "ChuyenNganh.MaChuyenNganh.MaxLength(20)": "MaChuyenNganh không được quá 20 ký tự", "ChuyenNganh.MaChuyenNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền MaChuyenNganh", "ChuyenNganh.ChuyenNganh.MaxLength(200)": "ChuyenN<PERSON>h không được quá 200 ký tự", "ChuyenNganh.ChuyenNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>n ChuyenNganh", "ChuyenNganh.ChuyenNganhEn.MaxLength(200)": "ChuyenNganhEn không được quá 200 ký tự", "ChuyenNganh.ChuyenNganhEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>ền ChuyenNganhEn", "ChuyenNganh.IdNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdNganh", "KhuVuc.Existed": "<PERSON><PERSON> vực này đã tồn tại", "KhuVuc.IdKhuVuc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>yền IdKhu<PERSON>uc", "KhuVuc.MaKhuVuc.MaxLength(10)": "MaKhu<PERSON><PERSON> không đư<PERSON><PERSON> quá 10 ký tự", "KhuVuc.MaKhuVuc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "KhuVuc.TenKhuVuc.MaxLength(50)": "TenKhuV<PERSON> không đư<PERSON><PERSON> quá 50 ký tự", "KhuVuc.TenKhuVuc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "NhomDoiTuong.Existed": "<PERSON><PERSON><PERSON><PERSON> đối tượng này đã tồn tại", "NhomDoiTuong.IdNhomDoiTuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền IdNhomDoiTuong", "NhomDoiTuong.MaNhom.MaxLength(5)": "Ma<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "NhomDoiTuong.MaNhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "NhomDoiTuong.TenNhom.MaxLength(100)": "TenNhom không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "NhomDoiTuong.TenNhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "NhomDoiTuong.TenNhomEn.MaxLength(50)": "NhomDoiTuongEn không được quá 50 ký tự", "DoiTuong.Existed": "<PERSON><PERSON><PERSON> tượng này đã tồn tại", "DoiTuong.IdDoiTuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdDoiTuong", "DoiTuong.MaDoiTuong.MaxLength(5)": "MaDoiTuong không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "DoiTuong.MaDoiTuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "DoiTuong.TenDoiTuong.MaxLength(50)": "TenDoiTuong không đư<PERSON><PERSON> quá 50 ký tự", "DoiTuong.TenDoiTuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenDoiTuong", "DoiTuong.PhanTramMienGiam.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n PhanTramMienGiam", "DoiTuongHocBong.Existed": "<PERSON><PERSON><PERSON> tư<PERSON><PERSON> học bổng này đã tồn tại", "DoiTuongHocBong.IdDoiTuongHocBong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>ền IdDoiTuongHocBong", "DoiTuongHocBong.MaDoiTuongHocBong.MaxLength(5)": "MaDoiTuongHocBong không đư<PERSON><PERSON> quá 5 ký tự", "DoiTuongHocBong.MaDoiTuongHocBong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MaDoiTuongHocBong", "DoiTuongHocBong.DoiTuongHocBong.MaxLength(50)": "DoiTuongHocBong không được quá 50 ký tự", "DoiTuongHocBong.DoiTuongHocBong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n DoiTuongHocBong", "DoiTuongHocBong.SoTienTroCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n SoTienTroCap", "DoiTuongHocBong.PhanTramTroCap.NotRequire": "Bắt bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền PhanTramTroCap", "CapKhenThuongKyLuat.Existed": "<PERSON><PERSON><PERSON> này đã tồn tại", "CapKhenThuongKyLuat.IdCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền IdCap", "CapKhenThuongKyLuat.MaCap.MaxLength(20)": "MaCap không đư<PERSON><PERSON> quá 20 ký tự", "CapKhenThuongKyLuat.TenCap.MaxLength(50)": "MaCap không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "CapKhenThuongKyLuat.TenCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenCap", "LoaiKhenThuong.Existed": "<PERSON><PERSON><PERSON> khen thưởng này đã tồn tại", "LoaiKhenThuong.IdLoaiKhenThuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdLoaiKhen<PERSON>huong", "LoaiKhenThuong.IdCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền IdCap", "LoaiKhenThuong.LoaiKhenThuong.MaxLength(100)": "LoaiKhen<PERSON><PERSON><PERSON> không được quá 100 ký tự", "LoaiKhenThuong.LoaiKhenThuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n <PERSON>", "LoaiKhenThuong.DiemThuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON><PERSON>", "HanhVi.Existed": "<PERSON>ành vi Kỷ luật này đã tồn tại", "HanhVi.IdHanhVi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHanhVi", "HanhVi.MaHanhVi.MaxLength(5)": "MaHanhVi không đư<PERSON><PERSON> quá 5 ký tự", "HanhVi.MaHanhVi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền MaHanhVi", "HanhVi.HanhVi.MaxLength(100)": "HanhVi không được quá 100 ký tự", "HanhVi.HanhVi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>", "XuLy.Existed": "<PERSON>ử lý Kỷ luật này đã tồn tại", "XuLy.IdXuLy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdXuLy", "XuLy.IdCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền IdCap", "XuLy.XuLy.MaxLength(50)": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 100 ký tự", "XuLy.XuLy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XuLy.SoThang.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n SoThang", "XuLy.DiemPhat.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XuLy.MucXuLy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MucXuLy", "HinhThucHoc.Existed": "<PERSON><PERSON><PERSON> thức học này đã tồn tại", "HinhThucHoc.IdHinhThucHoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdHinhThucHoc", "HinhThucHoc.MaHinhThucHoc.MaxLength(50)": "MaHinhThucHoc không được quá 50 ký tự", "HinhThucHoc.MaHinhThucHoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HinhThucHoc.TenHinhThucHoc.MaxLength(50)": "TenHinhThucHoc không được quá 50 ký tự", "HinhThucHoc.TenHinhThucHoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenHinhThucHoc", "HinhThucHoc.GhiChu.MaxLength(200)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON>c quá 200 ký tự", "ChucVu.Existed": "<PERSON><PERSON><PERSON> vụ này đã tồn tại", "ChucVu.IdChucVu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdChucVu", "ChucVu.MaChucVu.MaxLength(5)": "MaChucVu không đư<PERSON><PERSON> quá 5 ký tự", "ChucVu.MaChucVu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MaChucVu", "ChucVu.ChucVu.MaxLength(100)": "ChucVu không đư<PERSON>c quá 100 ký tự", "ChucVu.ChucVu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HocKyDangKy.Existed": "<PERSON><PERSON><PERSON> kỳ đăng ký này đã tồn tại", "HocKyDangKy.Dot.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HocKyDangKy.HocKy.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HocKyDangKy.NamHoc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HocKyDangKy.NamHoc.MaxLength(10)": "NamHoc không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "HocKyDangKy.TuNgay.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HocKyDangKy.DenNgay.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HocKyDangKy.ChonDangKy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>n<PERSON>", "HocKyDangKy.KhoaTkb.NotRequire": "Bắ<PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền KhoaTkb", "LoaiRenLuyen.Existed": "Loại rèn luyện này đã tồn tại", "LoaiRenLuyen.IdLoaiRenLuyen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdLoaiRenLuyen", "LoaiRenLuyen.IdCapRenLuyen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdCapRenLuyen", "LoaiRenLuyen.KyHieu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiRenLuyen.KyHieu.MaxLength(10)": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> quá 10 ký tự", "LoaiRenLuyen.TenLoai.MaxLength(200)": "TenLoai không đư<PERSON>c quá 200 ký tự", "LoaiRenLuyen.TenLoai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Ten<PERSON>i", "LoaiRenLuyen.Diem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiRenLuyen.Existed": "<PERSON><PERSON><PERSON> loại rèn luyện này đã tồn tại", "XepLoaiRenLuyen.IdXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền IdXepLoai", "XepLoaiRenLuyen.XepLoai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền XepLoai", "XepLoaiRenLuyen.XepLoai.MaxLength(50)": "XepLoai không đư<PERSON><PERSON> quá 50 ký tự", "XepLoaiRenLuyen.XepLoaiEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền XepLoaiEn", "XepLoaiRenLuyen.XepLoaiEn.MaxLength(50)": "XepLoaiEn không đư<PERSON>c quá 50 ký tự", "XepLoaiRenLuyen.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiRenLuyen.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiRenLuyen.Heso.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "LoaiGiayTo.Existed": "Loại giấy tờ này đã tồn tại", "LoaiGiayTo.IdGiayTo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdGiayTo", "LoaiGiayTo.MaGiayTo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MaGiay<PERSON>o", "LoaiGiayTo.MaGiayTo.MaxLength(5)": "MaGiayTo không đ<PERSON><PERSON><PERSON> quá 5 ký tự", "LoaiGiayTo.TenGiayTo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenGiayTo", "LoaiGiayTo.TenGiayTo.MaxLength(100)": "TenGiayTo không đư<PERSON>c quá 100 ký tự", "LoaiGiayTo.Stt.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>n Stt", "LoaiGiayTo.BatBuoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>c", "LoaiGiayTo.MacDinh.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiGiayTo.Nhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiGiayTo.GhiChu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiGiayTo.GhiChu.MaxLength(150)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON><PERSON> quá 150 ký tự", "LoaiGiayTo.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "LoaiGiayTo.IdPhong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdPhong", "LoaiGiayTo.TuyenSinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n TuyenSinh", "XepLoaiHocBong.Existed": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> học bổng này đã tồn tại", "XepLoaiHocBong.IdXepLoaiHb.NotRequire": "Bắ<PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdXepLoaiHb", "XepLoaiHocBong.TenXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền TenXepLoai", "XepLoaiHocBong.TenXepLoai.MaxLength(50)": "TenXepLoai không đư<PERSON><PERSON> quá 50 ký tự", "XepLoaiHocBong.TuDiemHt.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Tu<PERSON>iemHt", "XepLoaiHocBong.TuDiemRl.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Tu<PERSON>iem<PERSON>l", "XepLoaiHocBong.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "XepLoaiHocBong.TuDiemHt4.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> tru<PERSON>ền TuDiemHt4", "XepLoaiHocBong.MaXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền MaXepLoai", "XepLoaiHocBong.MaXepLoai.MaxLength(50)": "MaXepLoai không đư<PERSON><PERSON> quá 50 ký tự", "XepLoaiHocBong.SoTien.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n SoTien", "Phuong.Existed": "Phường này đã tồn tại", "Phuong.IdPhuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n <PERSON>d<PERSON>", "Phuong.TenPhuong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "Phuong.TenPhuong.MaxLength(100)": "TenPhuong không được quá 100 ký tự", "Xa.Existed": "<PERSON>ã này đã tồn tại", "Xa.IdXa.MaxLength(6)": "<PERSON><PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 6 ký tự", "Xa.IdXa.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON> t<PERSON>yền IdXa", "Xa.IdHuyen.MaxLength(4)": "<PERSON><PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 4 ký tự", "Xa.IdHuyen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "Xa.TenXa.MaxLength(30)": "TenXa không đư<PERSON><PERSON> quá 30 ký tự", "Xa.TenXa.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Ten<PERSON>a", "Xa.TenXaEn.MaxLength(50)": "TenXaEn không đư<PERSON> quá 50 ký tự", "Xa.TenXaEn.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>n TenXaEn", "DiemRenLuyenQuyDoi.Existed": "<PERSON><PERSON><PERSON><PERSON> rèn luyện quy đổi này đã tồn tại", "DiemRenLuyenQuyDoi.IdXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền IdXepLoai", "DiemRenLuyenQuyDoi.XepLoai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền XepLoai", "DiemRenLuyenQuyDoi.XepLoai.MaxLength(50)": "XepLoai không đư<PERSON><PERSON> quá 50 ký tự", "DiemRenLuyenQuyDoi.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "DiemRenLuyenQuyDoi.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "DiemRenLuyenQuyDoi.DiemCong10.NotRequire": "Bắt bu<PERSON><PERSON> ph<PERSON>i truyền DiemCong10", "DiemRenLuyenQuyDoi.DiemCong4.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> truyền DiemCong4", "LoaiQuyetDinh.Existed": "<PERSON><PERSON><PERSON> quyết định này đã tồn tại", "LoaiQuyetDinh.IdLoaiQd.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>yền IdLoaiQd", "LoaiQuyetDinh.MaQd.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiQuyetDinh.MaQd.MaxLength(10)": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "LoaiQuyetDinh.TenLoaiQd.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenLoaiQd", "LoaiQuyetDinh.TenLoaiQd.MaxLength(100)": "TenLoaiQd không được quá 100 ký tự", "LoaiQuyetDinh.ChuyenLop.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n ChuyenLop", "LoaiQuyetDinh.ThoiHoc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n Thoi<PERSON>", "LoaiQuyetDinh.NgungHoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "LoaiQuyetDinh.HocTiep.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiQuyetDinh.ChuyenTruongDi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền ChuyenTruongDi", "LoaiQuyetDinh.ChuyentruongDen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n Chu<PERSON>nt<PERSON>ong<PERSON>en", "LoaiQuyetDinh.ThoiHocQuyChe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n ThoiHocQuyChe", "LoaiQuyetDinh.XoaTenkhoiLop.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n XoaTenkhoiLop", "ToaNha.Existed": "T<PERSON>a nhà này đã tồn tại", "ToaNha.IdNha.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền IdNha", "ToaNha.MaNha.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "ToaNha.MaNha.MaxLength(10)": "MaNha không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "ToaNha.TenNha.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "ToaNha.TenNha.MaxLength(50)": "TenNha không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "ToaNha.IDCoSo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>y<PERSON>n IDCoSo", "CoSoDaoTao.Existed": "Cơ sở đào tạo này đã tồn tại", "CoSoDaoTao.IdCoSo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>yền IdCoSo", "CoSoDaoTao.MaCoSo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n MaCoSo", "CoSoDaoTao.MaCoSo.MaxLength(20)": "MaCoSo không đư<PERSON><PERSON> quá 20 ký tự", "CoSoDaoTao.TenCoSo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenCoSo", "CoSoDaoTao.TenCoSo.MaxLength(200)": "TenCoSo không đư<PERSON>c quá 200 ký tự", "CoSoDaoTao.DayNgoaiTruong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền DayNgoaiTruong", "CoSoDaoTao.GdCongViec.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n GdCongViec", "BoMon.Existed": "<PERSON>ộ môn này đã tồn tại", "BoMon.IdBoMon.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdBoMon", "BoMon.MaBoMon.MaxLength(10)": "MaBoMon không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "BoMon.MaBoMon.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "BoMon.BoMon.MaxLength(100)": "BoMon không đ<PERSON><PERSON><PERSON> quá 100 ký tự", "BoMon.BoMon.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "BoMon.SoNhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n So<PERSON>hom", "DoiTuongHocPhi.Existed": "<PERSON><PERSON><PERSON> tượ<PERSON> học phí này đã tồn tại", "DoiTuongHocPhi.IdDoiTuongHocPhi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>ền IdDoiTuongHocPhi", "DoiTuongHocPhi.DoiTuongHocPhi.MaxLength(50)": "DoiTuongHocPhi không được quá 50 ký tự", "DoiTuongHocPhi.DoiTuongHocPhi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n DoiTuongHocPhi", "LoaiThuChi.Existed": "<PERSON><PERSON><PERSON> thu chi này đã tồn tại", "LoaiThuChi.IdThuChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdThuChi", "LoaiThuChi.MaThuChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Ma<PERSON>hu<PERSON>", "LoaiThuChi.MaThuChi.MaxLength(20)": "Ma<PERSON>hu<PERSON><PERSON> không được quá 20 ký tự", "LoaiThuChi.TenThuChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenThuChi", "LoaiThuChi.TenThuChi.MaxLength(100)": "TenThu<PERSON><PERSON> không được quá 100 ký tự", "LoaiThuChi.ThuChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThuChi.SoTien.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n SoTien", "LoaiThuChi.HocLai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThuChi.ThiLai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Thi<PERSON>ai", "LoaiThuChi.HocPhi.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThuChi.KinhPhiDt.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>nh<PERSON>hi<PERSON>t", "LoaiThuChi.KhoanThuKtx.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n KhoanThuKtx", "LoaiThuChi.KhoanThuTienPhong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThuChi.KhoanTienCuoc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThuChi.BaoHiem.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HinhThucThi.Existed": "<PERSON><PERSON><PERSON> thức thi này đã tồn tại", "HinhThucThi.IdHinhThucThi.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdHinhThucThi", "HinhThucThi.MaHinhThucThi.MaxLength(50)": "MaHinhThu<PERSON><PERSON><PERSON> không được quá 50 ký tự", "HinhThucThi.MaHinhThucThi.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HinhThucThi.HinhThucThi.MaxLength(50)": "HinhThu<PERSON><PERSON><PERSON> không được quá 50 ký tự", "HinhThucThi.HinhThucThi.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "HinhThucThi.GhiChu.MaxLength(200)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON>c quá 200 ký tự", "HinhThucThi.GhiChu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "HinhThucThi.KhongKiemTraTrungLich.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>hongKiemTraTrungLich", "NoiThucTap.Existed": "<PERSON><PERSON><PERSON> thực tập này đã tồn tại", "NoiThucTap.IdNoiThucTap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>ền IdNoiThucTap", "NoiThucTap.MaNoiThucTap.MaxLength(20)": "MaNoiThucTap không được quá 20 ký tự", "NoiThucTap.MaNoiThucTap.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>ền MaNoiThucTap", "NoiThucTap.NoiThucTap.MaxLength(200)": "NoiThucTap không được quá 200 ký tự", "NoiThucTap.NoiThucTap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>ền NoiThucTap", "NoiThucTap.DiaChithucTap.MaxLength(50)": "DiaChithucTap không được quá 50 ký tự", "NoiThucTap.DiaChithucTap.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "ThanhPhanMonTheohe.Existed": "<PERSON><PERSON><PERSON><PERSON> phần môn theo hệ này đã tồn tại", "ThanhPhanMonTheohe.IdThanhPhan.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdThanhPhan", "ThanhPhanMonTheohe.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "ThanhPhanMonTheohe.Stt.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON> t<PERSON>n Stt", "ThanhPhanMonTheohe.TyLe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i truyền TyLe", "ThanhPhanMonTheohe.TyLeNhom.NotRequire": "Bắt bu<PERSON><PERSON> ph<PERSON>i truyền TyLeNhom", "ThanhPhanMonTheohe.NhomThanhPhan.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n NhomThanhPhan", "ThanhPhanMonTheohe.ChonMacDinh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n <PERSON>", "DiemQuyDoi.Existed": "<PERSON><PERSON><PERSON><PERSON> quy đổi theo hệ này đã tồn tại", "DiemQuyDoi.IdXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền IdXepLoai", "DiemQuyDoi.TuHocKy.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "DiemQuyDoi.TuNamHoc.MaxLength(10)": "TuNamHoc không đư<PERSON><PERSON> quá 10 ký tự", "DiemQuyDoi.TuNamHoc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>n <PERSON>", "DiemQuyDoi.DenHocKy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n DenHocKy", "DiemQuyDoi.DenNamHoc.MaxLength(10)": "DenNamHoc không đư<PERSON><PERSON> quá 10 ký tự", "DiemQuyDoi.DenNamHoc.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Den<PERSON>amHoc", "DiemQuyDoi.XepLoai.MaxLength(50)": "XepLoai không đư<PERSON><PERSON> quá 50 ký tự", "DiemQuyDoi.XepLoai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền XepLoai", "DiemQuyDoi.DiemChu.MaxLength(2)": "<PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 2 ký tự", "DiemQuyDoi.DiemChu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "DiemQuyDoi.DiemSo.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "DiemQuyDoi.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "DiemQuyDoi.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "DiemQuyDoi.TichLuy.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON>", "DiemQuyDoi.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "ChuongTrinhDaoTaoKienThuc.Existed": "<PERSON><PERSON>ơng trình đào tạo kiến thức này đã tồn tại", "ChuongTrinhDaoTaoKienThuc.IdKienthuc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON>i t<PERSON>n Id<PERSON>", "ChuongTrinhDaoTaoKienThuc.MonChuyenNganh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền MonChuyenNganh", "ChuongTrinhDaoTaoKienThuc.TenKienThuc.MaxLength(100)": "TenKienT<PERSON><PERSON> không đư<PERSON>c quá 100 ký tự", "ChuongTrinhDaoTaoKienThuc.TenKienThuc.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n Ten<PERSON>Thuc", "ChuongTrinhDaoTaoKienThuc.TenKienThucEn.MaxLength(200)": "TenKienThucEn không được quá 200 ký tự", "PhongHoc.Existed": "<PERSON><PERSON><PERSON> học này đã tồn tại", "PhongHoc.IdPhong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n IdPhong", "PhongHoc.SoPhong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "PhongHoc.SoPhong.MaxLength(50)": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "PhongHoc.LoaiPhong.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "PhongHoc.LoaiPhong.MaxLength(200)": "<PERSON>ai<PERSON><PERSON> không đư<PERSON>c quá 200 ký tự", "PhongHoc.ThietBi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền ThietBi", "PhongHoc.ThietBi.MaxLength(1000)": "ThietBi không được quá 1000 ký tự", "PhongHoc.GhiChu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "PhongHoc.GhiChu.MaxLength(100)": "<PERSON><PERSON><PERSON><PERSON> không đư<PERSON>c quá 100 ký tự", "PhongHoc.KhongToChucThi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "KyHieu.Existed": "<PERSON><PERSON> hiệu đã tồn tại", "LoaiThanhPhanDiem.KyHieu.MaxLength(20)": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> quá 20 ký tự", "LoaiThanhPhanDiem.KyHieu.NotRequire.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiThanhPhanDiem.TenThanhPhan.MaxLength(50)": "TenThanhPhan không được quá 50 ký tự", "LoaiThanhPhanDiem.TenThanhPhan.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenThanhPhan", "LoaiThanhPhanDiem.TyLe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i truyền TyLe", "LoaiThanhPhanDiem.KyHieuNhom.MaxLength(10)": "KyHieuNhom không đ<PERSON><PERSON><PERSON> quá 10 ký tự", "LoaiThanhPhanDiem.KyHieuNhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n KyHieuNhom", "LoaiThanhPhanDiem.ThucHanh.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoai.Existed": "<PERSON><PERSON><PERSON> loại đã tồn tại trong hệ", "MaXepLoai.Existed": "<PERSON>ã xếp loại đã tồn tại trong hệ", "XepLoaiHocTapThangDiem10.XepLoai.MaxLength(50)": "<PERSON><PERSON><PERSON> lo<PERSON>i không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "XepLoaiHocTapThangDiem10.XepLoai.NotRequire.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON><PERSON>n <PERSON><PERSON>", "XepLoaiHocTapThangDiem10.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i tru<PERSON>ền từ điểm", "XepLoaiHocTapThangDiem10.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền đến điểm", "XepLoaiHocTapThangDiem10.MaXepLoai.MaxLength(20)": "Mã xếp loại không đư<PERSON>c quá 20 ký tự", "XepLoaiHocTapThangDiem10.XepLoaiEn.MaxLength(50)": "XepLoaiEn không được quá 20 ký tự", "XepLoaiHocTapThangDiem10.IdHe.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n hệ", "NamThu.Existed": "<PERSON><PERSON><PERSON> thứ đã tồn tại", "XepHangNamDaoTao.NamThu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON><PERSON>n <PERSON> thứ", "XepHangNamDaoTao.TuTinChi.NotRequire": "<PERSON><PERSON><PERSON> buộ<PERSON> phải truyền từ tín chỉ", "XepHangNamDaoTao.DenTinChi.NotRequire": "<PERSON><PERSON><PERSON> buộ<PERSON> phải truyền đến tín chỉ", "XepHangNamDaoTao.NamThuEn.MaxLength(50)": "NamThuEn không đư<PERSON><PERSON> quá 50 ký tự", "XepHang.Existed": "<PERSON><PERSON><PERSON> hạng đã tồn tại trong hệ", "XepHangHocLuc.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepHangHocLuc.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepHangHocLuc.XepHang.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n XepHang", "XepHangHocLuc.XepHang.MaxLength(20)": "XepHang không đư<PERSON>c quá 20 ký tự", "XepHangHocLuc.XepHangEn.MaxLength(20)": "XepHangEn không được quá 20 ký tự", "XepHangHocLuc.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "XepLoaiHocTapThang4.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiHocTapThang4.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiHocTapThang4.XepLoai.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền XepLoai", "XepLoaiHocTapThang4.XepLoai.MaxLength(50)": "XepLoai không đư<PERSON><PERSON> quá 50 ký tự", "XepLoaiHocTapThang4.XepLoaiEn.MaxLength(20)": "XepLoaiEn không được quá 20 ký tự", "XepLoaiHocTapThang4.MaXepLoai.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền MaXepLoai", "XepLoaiHocTapThang4.XepLoai.MaxLength(20)": "MaXepLoai không đư<PERSON>c quá 20 ký tự", "XepLoaiHocTapThang4.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "MaXepHang.Existed": "<PERSON><PERSON> xếp hạng đã tồn tại trong hệ", "XepLoaiTotNghiepThang4.XepHang.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n XepHang", "XepLoaiTotNghiepThang4.XepHang.MaxLength(20)": "XepHang không đư<PERSON>c quá 20 ký tự", "XepLoaiTotNghiepThang4.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiTotNghiepThang4.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiTotNghiepThang4.TuDiemThang10.NotRequire": "<PERSON>ắ<PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> tru<PERSON>ền TuDiemThang10", "XepLoaiTotNghiepThang4.DenDiemThang10.NotRequire": "Bắt bu<PERSON><PERSON> ph<PERSON>i truyền DenDiemThang10", "XepLoaiTotNghiepThang4.MaXepHang.MaxLength(20)": "MaXepHang không được quá 20 ký tự", "XepLoaiTotNghiepThang4.MaXepHang.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i t<PERSON>yền MaXepHang", "XepLoaiTotNghiepThang4.XepHangEn.MaxLength(20)": "XepHangEn không được quá 20 ký tự", "XepLoaiTotNghiepThang4.IdHe.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền IdHe", "XepHangTotNghiepThangDiem10.XepHang.MaxLength(50)": "<PERSON><PERSON><PERSON> hạng không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "XepHangTotNghiepThangDiem10.XepHang.NotRequire.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n <PERSON><PERSON><PERSON> hạng", "XepHangTotNghiepThangDiem10.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i tru<PERSON>ền từ điểm", "XepHangTotNghiepThangDiem10.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i truyền đến điểm", "XepHangTotNghiepThangDiem10.MaXepHang.MaxLength(20)": "<PERSON>ã xếp hạng không đư<PERSON><PERSON> quá 20 ký tự", "XepHangTotNghiepThangDiem10.XepHangEn.MaxLength(50)": "XepHangEn không được quá 20 ký tự", "XepHangTotNghiepThangDiem10.IdHe.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n hệ", "LoaiChungChi.KyHieu.MaxLength(20)": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> quá 20 ký tự", "LoaiChungChi.KyHieu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "LoaiChungChi.LoaiChungChi.MaxLength(200)": "LoaiChungChi không được quá 20 ký tự", "LoaiChungChi.LoaiChungChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>ền <PERSON>ai<PERSON>g<PERSON>hi", "LoaiChungChi.IdNhomChungChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>yền IdNhomChungChi", "LoaiChungChi.CapDoChungChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n CapDoChungChi", "XepHang-ChungChi.Existed": "<PERSON><PERSON><PERSON> hạng đã tồn tại", "XepLoaiChungChi.DenDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiChungChi.TuDiem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "XepLoaiChungChi.XepHang.MaxLength(20)": "XepHang không đư<PERSON>c quá 20 ký tự", "XepLoaiChungChi.XepHang.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON>i t<PERSON>n XepHang", "XepLoaiChungChi.XepHangEn.MaxLength(50)": "XepHang không đư<PERSON><PERSON> quá 50 ký tự", "KyHieuNhom.Existed": "<PERSON><PERSON> hiệu nhóm đã tồn tại", "NhomChungChi.KyHieuNhom.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i tru<PERSON>ền ký hiệu nhóm", "NhomChungChi.KyHieuNhom.MaxLength(20)": "<PERSON>ý hiệu nhóm không được quá 20 ký tự", "NhomChungChi.NhomChungChi.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> phải tru<PERSON>ền nhóm chứng chỉ", "NhomChungChi.NhomChungChi.MaxLength(200)": "<PERSON><PERSON><PERSON><PERSON> chứng chỉ không được quá 200 ký tự", "CapRenLuyen.Existed": "<PERSON><PERSON><PERSON> rèn luyện đã tồn tại", "CapRenLuyen.IdCapRenLuyen.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>ền IdCapRenLuyen", "CapRenLuyen.KyHieu.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "CapRenLuyen.KyHieu.MaxLength(10)": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> quá 10 ký tự", "CapRenLuyen.TenCap.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>n TenCap", "CapRenLuyen.TenCap.MaxLength(50)": "TenCap không đư<PERSON><PERSON> quá 200 ký tự", "CapRenLuyen.Diem.NotRequire": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> t<PERSON>", "ChuongTrinhDaoTao.Existed": "<PERSON>ương trình đào tạo này đã tồn tại", "ChuongTrinhDaoTaoChiTiet.Existed": "<PERSON>ôn này đã tồn tại trong chương trình đào tạo", "GiaoAn.Existed": "G<PERSON><PERSON>o án này đã tồn tại", "GiaoAn.NotExisted": "<PERSON><PERSON><PERSON><PERSON> án này không tồn tại", "MucHuongBhyt.Existed": "<PERSON><PERSON><PERSON> hưởng bhyt này đã tồn tại", "Vung.Existed": " <PERSON><PERSON><PERSON> này đã tồn tại", "BacDaoTao.Existed": "<PERSON><PERSON><PERSON> đào tạo này đã tồn tại", "BacDaoTao.IdBacDaoTao.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>yền IdBacDaoTao", "BacDaoTao.MaBacDaoTao.MaxLength(10)": "MaBacDaoTao không được quá 10 ký tự", "BacDaoTao.MaBacDaoTao.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n MaBacDaoTao", "BacDaoTao.BacDaoTao.MaxLength(100)": "BacDaoTao không được quá 100 ký tự", "BacDaoTao.BacDaoTao.NotRequire": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>n BacDao<PERSON>ao", "map-work-flow-name-function.phan-he.not-exists": "<PERSON><PERSON> hệ không tồn tại", "map-work-flow-name-function.tham-so-he-thong.not-exists": "<PERSON><PERSON> số không tồn tại", "chung-thu-so.serial-number.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i nhập số serial", "chung-thu-so.serial-number.max-length": "Số serial không đư<PERSON><PERSON> quá 100 ký tự", "chung-thu-so.serial-number.existed": "Số serial này đã tồn tại", "chung-thu-so.subject-name.required": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON>i nhập tên chủ thể", "chung-thu-so.subject-name.max-length": "Tên chủ thể không đư<PERSON>c quá 500 ký tự", "chung-thu-so.issuer.required": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> nhập đơn vị cấp", "chung-thu-so.issuer.max-length": "Đơn vị cấp không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "chung-thu-so.certificate-base64.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i nhập dữ liệu chứng thư số", "chung-thu-so.list.required": "<PERSON><PERSON> s<PERSON>ch chứng thư số không đư<PERSON><PERSON> để trống", "chung-thu-so.list.min-length": "<PERSON><PERSON> s<PERSON>ch chứng thư số phải có ít nhất 1 phần tử", "mau-chu-ky.code.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> phải nhập mã mẫu chữ ký", "mau-chu-ky.code.max-length": "Mã mẫu chữ ký không được quá 50 ký tự", "mau-chu-ky.code.existed": "Mã mẫu chữ ký này đã tồn tại cho người dùng này", "mau-chu-ky.name.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i nhập tên mẫu chữ ký", "mau-chu-ky.name.max-length": "Tên mẫu chữ ký không được quá 100 ký tự", "visnam-tai-khoan-ket-noi.user-id.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> phải chọn người dùng", "visnam-tai-khoan-ket-noi.user-id.existed": "Người dùng này đã có tài khoản kết nối <PERSON>nam", "visnam-tai-khoan-ket-noi.key.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON>i nhập Key kết nối", "visnam-tai-khoan-ket-noi.key.max-length": "Key kết n<PERSON>i không đ<PERSON><PERSON><PERSON> quá 500 ký tự", "visnam-tai-khoan-ket-noi.secret.required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> ph<PERSON><PERSON> nhập Secret kết nối", "visnam-tai-khoan-ket-noi.secret.max-length": "Secret kết n<PERSON>i không đ<PERSON><PERSON><PERSON> quá 500 ký tự"}