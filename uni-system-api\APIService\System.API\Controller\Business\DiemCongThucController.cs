﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/diem-cong-thuc")]
    [ApiExplorerSettings(GroupName = "12. Điểm công thức")]
    [Authorize]
    public class DiemCongThucController : ApiControllerBase
    {
        public DiemCongThucController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Điểm công thức có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<DiemCongThucBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] DiemCongThucFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterDiemCongThucQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Điểm công thức
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<DiemCongThucModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetDiemCongThucByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Điểm công thức
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateDiemCongThucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_CONG_THUC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_CONG_THUC_CREATE;


                return await _mediator.Send(new CreateDiemCongThucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Điểm công thức
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyDiemCongThucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_CONG_THUC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_CONG_THUC_CREATE_MANY;


                return await _mediator.Send(new CreateManyDiemCongThucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Điểm công thức
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDiemCongThucModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_CONG_THUC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_CONG_THUC_UPDATE;
                return await _mediator.Send(new UpdateDiemCongThucCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Điểm công thức
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_CONG_THUC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_CONG_THUC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_CONG_THUC_DELETE;

                return await _mediator.Send(new DeleteDiemCongThucCommand(id, u.SystemLog));
            });
        }

    }
}
