﻿using Core.Business;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    /// <summary>
    /// L<PERSON>y danh sách thống kê nhập học theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh thống kê nhập học</returns>
    public class GetThongKeNhapHocByFilterQuery : IRequest<PaginationList<ThongKeNhapHocModel>>
    {
        public ThongKeNhapHocQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách thống kê nhập học theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetThongKeNhapHocByFilterQuery(ThongKeNhapHocQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetThongKeNhapHocByFilterQuery, PaginationList<ThongKeNhapHocModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<ThongKeNhapHocModel>> Handle(GetThongKeNhapHocByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreThongKeNhapHocAsync(filter.IDHe, filter.NamHoc, filter.IDNganh);

                var data = new List<ThongKeNhapHocModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new ThongKeNhapHocModel()
                    {
                        IdNganh = row.Field<int>("ID_nganh"),
                        TenNganh = row.Field<string>("Ten_nganh"),
                        ChiTieuTuyenSinh = row.Field<int?>("Chi_tieu_tuyen_sinh"),
                        TrungTuyen = row.Field<int?>("Trung_tuyen"),
                        XacNhanNhapHoc = row.Field<int?>("Xac_nhan_nhap_hoc"),
                        DangKy = row.Field<int?>("Dang_ky"),
                        DaThuPhieuDiem = row.Field<int?>("Da_thu_phieu_diem"),
                        DaTraGiayBao = row.Field<int?>("Da_tra_giay_bao"),
                        DaThuHoSo= row.Field<int?>("Da_thu_ho_so"),
                        DaThuHocPhi = row.Field<int?>("Da_thu_hoc_phi"),
                        DaHoanThanh= row.Field<int?>("Da_hoan_thanh"),
                        DaRutHoSo = row.Field<int?>("Da_rut_ho_so"),
                        TongTien = row.Field<long?>("TongTien")
                    });
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<ThongKeNhapHocModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }
}
