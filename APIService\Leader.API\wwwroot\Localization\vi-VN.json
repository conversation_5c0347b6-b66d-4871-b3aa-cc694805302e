{"Localization.DefaultLanguage": "<PERSON><PERSON><PERSON> ngữ mặc định", "SoLenLop.Existed": "<PERSON><PERSON> lên lớp ng<PERSON> {0} - ca {1} đã tồn tại", "data.not-found": "<PERSON><PERSON> liệu không tồn tại", "data.NoDataToUpdate": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu đ<PERSON><PERSON><PERSON> cập nhật", "LinhVucDeTai.code.existed": "Mã lĩnh vực đề tài đã tồn tại", "LinhVucDeTai.code.required": "<PERSON><PERSON> lĩnh vực đề tài không được để trống", "LinhVucDeTai.name.required": "<PERSON><PERSON><PERSON> lĩnh vực đề tài không đượ<PERSON> để trống", "LoaiHopDong.name.existed": "<PERSON><PERSON>n loại hợp đồng đã tồn tại", "LoaiHopDong.name.required": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng không đư<PERSON><PERSON> để trống", "LoaiHoiThao.name.existed": "Tên loại hội thảo đã tồn tại", "LoaiHoiThao.name.required": "<PERSON><PERSON><PERSON> lo<PERSON>i hội thảo không đư<PERSON><PERSON> để trống", "LoaiDeXuat.name.existed": "Tên loại đề xuất đã tồn tại", "LoaiDeXuat.name.required": "Tên loại đề xuất không đư<PERSON><PERSON> để trống", "LinhVucNghienCuu.code.existed": "<PERSON><PERSON> lĩnh vực nghiên cứu đã tồn tại", "LinhVucNghienCuu.code.required": "<PERSON><PERSON> lĩnh vự<PERSON> nghiên cứu không đư<PERSON><PERSON> để trống", "LinhVucNghienCuu.name.required": "<PERSON><PERSON><PERSON> lĩnh vực nghiên cứu không đư<PERSON><PERSON> để trống", "LoaiSanPham.name.existed": "Tên loại sản phẩm đã tồn tại", "LoaiSanPham.name.required": "<PERSON>ên lo<PERSON>i sản phẩm không đư<PERSON><PERSON> để trống", "NguonKinhPhi.name.existed": "Tên loại sản phẩm đã tồn tại", "NguonKinhPhi.name.required": "<PERSON>ên lo<PERSON>i sản phẩm không đư<PERSON><PERSON> để trống", "DeXuatTrangThai.code.existed": "Mã trạng thái đề xuất đã tồn tại", "DeXuatTrangThai.code.required": "<PERSON>ã trạng thái đề xuất không đư<PERSON><PERSON> để trống", "DeXuatTrangThai.name.required": "Tên trạng thái đề xuất không đư<PERSON><PERSON> để trống", "HoiThaoTrangThai.code.existed": "<PERSON>ã hội thảo trạng thái đã tồn tại", "HoiThaoTrangThai.code.required": "<PERSON><PERSON> hội thảo trạng thái không đư<PERSON><PERSON> để trống", "HoiThaoTrangThai.name.required": "<PERSON><PERSON><PERSON> hội thảo trạng thái không đư<PERSON><PERSON> để trống", "DeTaiTrangThai.code.existed": "Mã đề tài trạng thái đã tồn tại", "DeTaiTrangThai.code.required": "Mã đề tài trạng thái không đượ<PERSON> để trống", "DeTaiTrangThai.name.required": "Tên đề tài trạng thái không đư<PERSON><PERSON> để trống", "ThangDiem.name.existed": "Tên thang điểm đã tồn tại", "ThangDiem.name.required": "Tên thang điểm không được để trống", "ThangDiem.nam.required": "<PERSON><PERSON><PERSON> thang điểm không được để trống", "ThangDiem.so.required": "<PERSON><PERSON> thang điểm không đư<PERSON>c để trống", "TrinhDoDaoTao.name.existed": "Tên trình độ đào tạo đã tồn tại", "TrinhDoDaoTao.name.required": "Tên trình độ đào tạo không đư<PERSON><PERSON> để trống", "XepLoai.name.existed": "Tên trình độ đào tạo đã tồn tại", "XepLoai.name.required": "Tên trình độ đào tạo không đư<PERSON><PERSON> để trống", "XepLoai.tuDiem.required": "Từ điểm của trình độ đào tạo không đượ<PERSON> để trống", "XepLoai.denDiem.required": "<PERSON><PERSON>n điểm của trình độ đào tạo không được để trống", "CanBoVaiTro.code.existed": "Mã cán bộ vai trò đã tồn tại", "CanBoVaiTro.code.required": "<PERSON><PERSON> cán bộ vai trò không đư<PERSON><PERSON> để trống", "CanBoVaiTro.name.required": "<PERSON><PERSON><PERSON> cán bộ vai trò không đư<PERSON><PERSON> để trống", "CanBoVaiTro.chuNhiemDeTai.required": "<PERSON><PERSON> nhiệm đề tài không được để trống", "CanBoVaiTro.idLoaiVaiTro.required": "Id lo<PERSON>i vai trò không được để trống", "CapHoiDong.code.existed": "<PERSON><PERSON> cấp hội đồng đã tồn tại", "CapHoiDong.code.required": "<PERSON><PERSON> cấp hội đồng không đư<PERSON><PERSON> để trống", "CapHoiDong.name.required": "<PERSON><PERSON><PERSON> cấp hội đồng không đư<PERSON><PERSON> để trống", "ChucVuHoiDong.code.existed": "<PERSON><PERSON> chức vụ hội đồng đã tồn tại", "ChucVuHoiDong.code.required": "<PERSON><PERSON> chức vụ hội đồng không đư<PERSON><PERSON> để trống", "ChucVuHoiDong.name.required": "<PERSON><PERSON><PERSON> chứ<PERSON> vụ hội đồng không đư<PERSON><PERSON> để trống", "CoQuan.code.existed": "Mã cơ quan đã tồn tại", "CoQuan.code.required": "<PERSON><PERSON> cơ quan không đư<PERSON><PERSON> để trống", "CoQuan.name.required": "<PERSON><PERSON><PERSON> cơ quan không đ<PERSON><PERSON><PERSON> để trống", "LoaiBaiBao.code.existed": "Mã loại bài báo đã tồn tại", "LoaiBaiBao.code.required": "<PERSON>ã loại bài báo không đư<PERSON><PERSON> để trống", "LoaiBaiBao.name.required": "<PERSON>ê<PERSON> lo<PERSON>i bài báo không đư<PERSON><PERSON> để trống", "LoaiDeTai.code.existed": "Mã loại đề tài đã tồn tại", "LoaiDeTai.code.required": "Mã loại đề tài không được để trống", "LoaiDeTai.name.required": "Tên loại đề tài không được để trống", "LoaiHoiDong.code.existed": "Mã loại hội đồng đã tồn tại", "LoaiHoiDong.code.required": "<PERSON>ã loại hội đồng không đư<PERSON>c để trống", "LoaiHoiDong.name.required": "<PERSON>ê<PERSON> lo<PERSON>i hội đồng không đư<PERSON><PERSON> để trống", "LoaiSach.code.existed": "Mã loại sách đã tồn tại", "LoaiSach.code.required": "<PERSON>ã loại sách không đ<PERSON><PERSON><PERSON> để trống", "LoaiSach.name.required": "<PERSON><PERSON><PERSON> lo<PERSON>i sách không đư<PERSON>c để trống", "DonVi.code.existed": "Mã đơn vị đã tồn tại", "DonVi.code.required": "<PERSON>ã đơn vị không đư<PERSON><PERSON> để trống", "DonVi.name.required": "<PERSON>ê<PERSON> đơn vị không đ<PERSON><PERSON><PERSON> để trống", "DonVi.donViCon.existed": "<PERSON>ui lòng xóa các đơn vị con trước khi xóa!", "DonVi.phongBan.existed": "Vui lòng xóa các phòng ban trực thuộc trước khi xóa!", "PhongBan.code.existed": "Mã phòng ban đã tồn tại", "PhongBan.code.required": "<PERSON><PERSON> phòng ban không được để trống", "PhongBan.name.required": "Tên phòng ban không được để trống", "PhongBan.phongBanCon.existed": "Vui lòng xóa các phòng ban con trước khi xóa!", "PhongBan.canBo.existed": "Không thể xóa phòng ban khi vẫn còn cán bộ trực thuộc!", "MauLyLich.code.existed": "<PERSON>ã mẫu lý lịch đã tồn tại", "MauLyLich.code.required": "<PERSON><PERSON> mẫu lý lịch không được để trống", "MauLyLich.name.required": "Tên mẫu lý lịch không được để trống", "MauLyLich.not-active": "Mẫu lý lịch không được sử dụng. <PERSON>ui lòng liên hệ quản trị viên!", "LyLich.code.existed": "Mã cán bộ đã tồn tại", "LyLich.code.required": "<PERSON><PERSON> cán bộ không được để trống", "LyLich.name.required": "<PERSON><PERSON> tên cán bộ không được để trống", "LyLich.PhongBan.not-found": "<PERSON><PERSON><PERSON> không thuộc phòng ban nào, vui lòng kiểm tra lại!", "QuaTrinhDaoTao.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "QuaTrinhDaoTao.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "NgoaiNgu.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "NgoaiNgu.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "KhoaDaoTao.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "KhoaDaoTao.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "QuaTrinhCongTac.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "QuaTrinhCongTac.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "LoaiVaiTro.code.existed": "Mã loại vai trò đã tồn tại", "LoaiVaiTro.code.required": "Mã loại vai trò không được để trống", "LoaiVaiTro.name.required": "Tên lo<PERSON>i vai trò không đư<PERSON><PERSON> để trống", "BaiBao.name.existed": "<PERSON>ên bài báo đã tồn tại", "BaiBao.name.required": "<PERSON><PERSON><PERSON> bà<PERSON> báo không được để trống", "bai-bao.trang-thai-khong-hop-le": "Tr<PERSON>ng thái dữ liệu bài báo không hợp lệ", "bai-bao.vui-long-thao-tac-lai": "<PERSON><PERSON> lòng thao tác lại", "bai-bao.quy-trinh-xu-ly-khong-hop-le": "<PERSON><PERSON> tác quy trình không hợp lệ", "BaiBaoFileDinhKem.IdBaiBao.required": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> không đư<PERSON>c để trống", "BaiBaoFileDinhKem.IdFileAttachment.required": "Địa chỉ file không được để trống", "BaiBaoFileDinhKem.IdBaiBao.not-found": "<PERSON><PERSON>ông tìm thấy thông tin bài báo", "BaiBaoThamGia.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "BaiBaoThamGia.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia bài báo", "BaiBaoThamGia.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "BaiBaoThamGia.IdBaiBao.required": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> không đư<PERSON>c để trống", "BaiBaoThamGia.IdVaiTro.required": "<PERSON><PERSON> trò không đư<PERSON><PERSON> để trống", "BaiBaoThamGia.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "HoiThao.name.existed": "<PERSON><PERSON><PERSON> hội thảo đã tồn tại", "HoiThao.name.required": "<PERSON><PERSON><PERSON> hội thảo không đư<PERSON><PERSON> để trống", "HoiThaoFileDinhKem.IdHoiThao.required": "<PERSON><PERSON><PERSON> thảo không đư<PERSON><PERSON> để trống", "HoiThaoFileDinhKem.IdFileAttachment.required": "Địa chỉ file không được để trống", "HoiThaoFileDinhKem.IdHoiThao.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin hội thảo", "HoiThaoThamGia.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "HoiThaoThamGia.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia hội thảo", "HoiThaoThamGia.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "HoiThaoThamGia.IdHoiThao.required": "<PERSON><PERSON><PERSON> thảo không đư<PERSON><PERSON> để trống", "HoiThaoThamGia.IdVaiTro.required": "<PERSON><PERSON> trò không đư<PERSON><PERSON> để trống", "HoiThaoThamGia.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "Sach.name.existed": "<PERSON><PERSON><PERSON> s<PERSON>ch đã tồn tại", "Sach.name.required": "<PERSON><PERSON><PERSON> s<PERSON>ch không đ<PERSON><PERSON><PERSON> để trống", "SachFileDinhKem.IdSach.required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "SachFileDinhKem.IdFileAttachment.required": "Địa chỉ file không được để trống", "SachFileDinhKem.IdSach.not-found": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin sách", "SachThamGia.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "SachThamGia.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia s<PERSON>ch", "SachThamGia.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "SachThamGia.IdSach.required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "SachThamGia.IdVaiTro.required": "<PERSON><PERSON> trò không đư<PERSON><PERSON> để trống", "SachThamGia.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "BangSoHuuTriTue.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "BangSoHuuTriTue.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "CapDeTai.code.existed": "<PERSON>ã cấp đề tài đã tồn tại", "CapDeTai.code.required": "<PERSON><PERSON> cấp đề tài không được để trống", "CapDeTai.name.required": "<PERSON><PERSON><PERSON> cấp đề tài không đư<PERSON><PERSON> để trống", "DeTai.code.existed": "Mã đề tài đã tồn tại", "DeTai.code.required": "Mã đề tài không được để trống", "DeTai.name.existed": "Tên đề tài đã tồn tại", "DeTai.name.required": "Tên đề tài không được để trống", "DeTai.GiaiDoanChucNang.not-found": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c định đư<PERSON> giai đo<PERSON>, vui lòng kiểm tra lại!", "DeTaiThamGia.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "DeTaiThamGia.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia đề tài", "DeTaiThamGia.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "DeTaiThamGia.IdDeTai.required": "<PERSON><PERSON> tài không đ<PERSON><PERSON><PERSON> để trống", "DeTaiThamGia.IdVaiTro.required": "<PERSON><PERSON> trò không đư<PERSON><PERSON> để trống", "DeTaiDotThongBao.code.existed": "Mã đề tài đợt thông báo đã tồn tại", "DeTaiDotThongBao.code.required": "<PERSON>ã đề tài đợt thông báo không đư<PERSON><PERSON> để trống", "DeTaiDotThongBaoDotThongBao.name.existed": "Tên đề tài đợt thông báo đã tồn tại", "DeTaiDotThongBaoDotThongBao.name.required": "Tên đề tài đợt thông báo không đượ<PERSON> để trống", "DeTaiDotThongBao.in-use": "Đã có đề tài đăng ký trong đợt không thể xóa!", "DeTaiLinhVucNghienCuu.IdLinhVucNghienCuu.required": "<PERSON><PERSON><PERSON> vự<PERSON> nghiên cứu không được để trống!", "DeTaiLinhVucNghienCuu.IdDeTai.required": "<PERSON><PERSON> tài không đư<PERSON><PERSON> để trống!", "DeTaiLinhVucNghienCuu.IdDeTai.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đề tài", "DeTaiLinhVucNghienCuu.IdLinhVucNghienCuu.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin lĩnh vực nghiên cứu", "DeTaiPhongBanPhoiHop.IdPhongBan.required": "<PERSON>òng ban không được để trống!", "DeTaiPhongBanPhoiHop.IdDeTai.required": "<PERSON><PERSON> tài không đư<PERSON><PERSON> để trống!", "DeTaiPhongBanPhoiHop.IdDeTai.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đề tài", "DeTaiPhongBanPhoiHop.IdPhongBan.not-found": "<PERSON>hông tìm thấy thông tin phòng ban", "DeTaiFileDinhKem.IdFileAttachment.required": "File đ<PERSON>h kèm không được để trống!", "DeTaiFileDinhKem.IdDeTai.required": "<PERSON><PERSON> tài không đư<PERSON><PERSON> để trống!", "DeTaiFileDinhKem.IdDeTai.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đề tài", "DeTaiHoiDong.name.existed": "<PERSON><PERSON>n hội đồng đã tồn tại", "DeTaiHoiDong.name.required": "<PERSON><PERSON><PERSON> hội đồng không được để trống", "DeTaiHoiDong.approved-de-xuat": "Hội đồng đã duyệt đề xuất không thể xóa!", "DeTaiHoiDong.approved-tham-dinh": "Hội đồng đã duyệt thẩm định không thể xóa!", "DeTaiHoiDong.approved-nghiem-thu": "Hội đồng đã duyệt nghiệm thu không thể xóa!", "DeTaiHoiDongFileDinhKem.IdFileAttachment.required": "File đ<PERSON>h kèm không được để trống!", "DeTaiHoiDongFileDinhKem.IdHoiDong.required": "Hội đồng không được để trống!", "DeTaiHoiDongFileDinhKem.IdHoiDong.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin hội đồng", "DeTaiHoiDongThanhVien.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "DeTaiHoiDongThanhVien.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia hội đồng", "DeTaiHoiDongThanhVien.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "DeTaiHoiDongThanhVien.IdHoiDong.required": "<PERSON><PERSON><PERSON> đồng không được để trống", "DeTaiHoiDongThanhVien.IdChucVuHoiDong.required": "<PERSON><PERSON><PERSON> vụ hội đồng không đư<PERSON><PERSON> để trống", "DeTaiHoiDongThanhVien.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "DeTaiHoiDongThanhVienFileDinhKem.IdFileAttachment.required": "File đ<PERSON>h kèm không được để trống!", "DeTaiHoiDongThanhVienFileDinhKem.IdHoiDongThanhVien.required": "<PERSON>h<PERSON><PERSON> viên hội đồng không được để trống!", "DeTaiHoiDongThanhVienFileDinhKem.IdHoiDongThanhVien.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin thành viên hội đồng", "DeTaiHoiDongThanhVienFileDinhKem.IdDeTai.required": "<PERSON><PERSON> tài không đư<PERSON><PERSON> để trống!", "DeTaiHoiDongThanhVienFileDinhKem.IdDeTai.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đề tài", "DeTaiHoiDongThanhVienFileDinhKem.IdDeTai.existed": "<PERSON><PERSON><PERSON><PERSON> viên hội đồng đã có file đính kèm không thể gửi thêm!", "GiaiThuong.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "GiaiThuong.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "HuongDanNghienCuuSinh.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "HuongDanNghienCuuSinh.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "SanPham.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "SanPham.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "ToChucThamGia.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "ToChucThamGia.IdCanBo.required": "Id c<PERSON> bộ không đư<PERSON>c để trống", "KhGioiTinh.code.existed": "Mã giới tính đã tồn tại", "KhGioiTinh.code.required": "<PERSON>ã giới tính không được để trống", "KhGioiTinh.name.required": "Tên gi<PERSON>i t<PERSON>h không được để trống", "KhDanToc.code.existed": "Mã dân tộc đã tồn tại", "KhDanToc.code.required": "<PERSON>ã dân tộc không được để trống", "KhDanToc.name.required": "<PERSON>ên dân tộc không đư<PERSON>c để trống", "KhHocVi.code.existed": "<PERSON><PERSON> học vị đã tồn tại", "KhHocVi.code.required": "<PERSON><PERSON> học vị không đư<PERSON>c để trống", "KhHocVi.name.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> vị không đ<PERSON><PERSON><PERSON> để trống", "KhHocHam.code.existed": "<PERSON><PERSON> học hàm đã tồn tại", "KhHocHam.code.required": "<PERSON><PERSON> học hàm không đư<PERSON><PERSON> để trống", "KhHocHam.name.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> hàm không đư<PERSON>c để trống", "KhChucDanh.code.existed": "<PERSON><PERSON> chức danh đã tồn tại", "KhChucDanh.code.required": "<PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống", "KhChucDanh.name.required": "<PERSON><PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống", "KhChucVu.code.existed": "<PERSON><PERSON> chức vụ đã tồn tại", "KhChucVu.code.required": "<PERSON><PERSON> chức vụ không đư<PERSON>c để trống", "KhChucVu.name.required": "<PERSON><PERSON><PERSON> chứ<PERSON> vụ không đ<PERSON><PERSON><PERSON> để trống", "KhChuyenNganh.code.existed": "<PERSON><PERSON> chuyên ngành đã tồn tại", "KhChuyenNganh.code.required": "<PERSON><PERSON> chuyên ngành không được để trống", "KhChuyenNganh.name.required": "<PERSON>ê<PERSON> chuyên ngành không được để trống", "KhQuocGia.code.existed": "Mã quốc gia đã tồn tại", "KhQuocGia.code.required": "<PERSON><PERSON> quốc gia không được để trống", "KhQuocGia.name.required": "<PERSON><PERSON><PERSON> quốc gia không được để trống", "KhBacDaoTao.code.existed": "<PERSON><PERSON> bậc đào tạo đã tồn tại", "KhBacDaoTao.code.required": "<PERSON><PERSON> bậc đào tạo không được để trống", "KhBacDaoTao.name.required": "<PERSON><PERSON><PERSON> bậc đào tạo không đư<PERSON><PERSON> để trống", "HocLieu.name.existed": "<PERSON><PERSON><PERSON> học liệu đã tồn tại", "HocLieu.name.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> liệu không được để trống", "hoc-lieu.trang-thai-khong-hop-le": "<PERSON>r<PERSON><PERSON> thái dữ liệu học liệu không hợp lệ", "hoc-lieu.vui-long-thao-tac-lai": "<PERSON><PERSON> lòng thao tác lại", "hoc-lieu.quy-trinh-xu-ly-khong-hop-le": "<PERSON><PERSON> tác quy trình không hợp lệ", "HocLieu.approved": "<PERSON><PERSON><PERSON> liệu đã đư<PERSON><PERSON> duyệt không thể xóa!", "HocLieuDeXuat.code.existed": "<PERSON><PERSON> học liệu đề xuất đã tồn tại", "HocLieuDeXuat.code.required": "<PERSON><PERSON> học liệu đề xuất không đư<PERSON><PERSON> để trống", "HocLieuDeXuat.name.existed": "<PERSON><PERSON><PERSON> học liệu đề xuất đã tồn tại", "HocLieuDeXuat.name.required": "<PERSON><PERSON><PERSON> họ<PERSON> liệu đề xuất không đư<PERSON><PERSON> để trống", "HocLieuDeXuat.approved": "<PERSON><PERSON><PERSON> liệu đề xuất đã được duyệt không thể xóa!", "HocLieuDotDangKy.code.existed": "<PERSON><PERSON> học liệu đợt đăng ký đã tồn tại", "HocLieuDotDangKy.code.required": "<PERSON><PERSON> học liệu đợt đăng ký không được để trống", "HocLieuDotDangKy.name.required": "<PERSON><PERSON><PERSON> họ<PERSON> liệu đợt đăng ký không được để trống", "HocLieuDotDangKy.in-use": "Đã có đề xuất đăng ký không thể xóa đợt!", "HocLieuHoiDong.code.existed": "<PERSON><PERSON> học liệu hội đồng đã tồn tại", "HocLieuHoiDong.code.required": "<PERSON><PERSON> học liệu hội đồng không đư<PERSON><PERSON> để trống", "HocLieuHoiDong.name.required": "<PERSON><PERSON><PERSON> họ<PERSON> liệu hội đồng không đư<PERSON><PERSON> để trống", "HocLieuHoiDong.approved-tham-dinh": "Hội đồng đã duyệt thẩm định không thể xóa!", "HocLieuHoiDong.approved-nghiem-thu": "Hội đồng đã duyệt nghiệm thu không thể xóa!", "workflow.scheme-not-found": "<PERSON><PERSON><PERSON>ng tìm thấy scheme", "HocLieuFileDinhKem.IdHocLieu.required": "<PERSON><PERSON><PERSON> li<PERSON> không đư<PERSON><PERSON> để trống", "HocLieuFileDinhKem.IdFileAttachment.required": "Địa chỉ file không được để trống", "HocLieuFileDinhKem.IdHocLieu.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin học liệu", "HocLieuHoiDongThanhVien.model.duplicate": "Tr<PERSON>ng cán bộ, vui lòng kiểm tra lại!", "HocLieuHoiDongThanhVien.IdCanBo.existed": "<PERSON><PERSON> bộ đã tham gia hội đồng", "HocLieuHoiDongThanhVien.IdCanBo.required": "<PERSON><PERSON> bộ không đư<PERSON><PERSON> để trống", "HocLieuHoiDongThanhVien.IdHoiDong.required": "<PERSON><PERSON><PERSON> đồng không được để trống", "HocLieuHoiDongThanhVien.IdChucVuHoiDong.required": "<PERSON><PERSON><PERSON> vụ hội đồng không đư<PERSON><PERSON> để trống", "HocLieuHoiDongThanhVien.IdCanBo.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin cán bộ", "HocLieuThamDinh.existed": "<PERSON><PERSON><PERSON> liệu đã có hội đồng thẩm định", "HocLieuThamDinhFileDinhKem.IdHocLieuThamDinh.required": "<PERSON><PERSON><PERSON> li<PERSON>u thẩm định không đư<PERSON><PERSON> để trống", "HocLieuThamDinhFileDinhKem.IdHoiDongThanhVien.required": "<PERSON>ộ<PERSON> đồng thành viên không được để trống", "HocLieuThamDinhFileDinhKem.IdFileAttachment.required": "Địa chỉ file không được để trống", "HocLieuThamDinhFileDinhKem.IdHocLieuThamDinh.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin học liệu thẩm định", "HocLieuThamDinhFileDinhKem.IdHoiDongThanhVien.not-found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin hội đồng thành viên", "HocLieuNghiemThu.existed": "<PERSON><PERSON><PERSON> liệu đã có hội đồng nghiệm thu", "LoaiTaiLieu.code.existed": "Mã loại tài liệu đã tồn tại", "LoaiTaiLieu.code.required": "<PERSON>ã loại tài liệu không đư<PERSON><PERSON> để trống", "LoaiTaiLieu.name.required": "Tên loại tài liệu không đư<PERSON><PERSON> để trống", "TaiLieu.code.existed": "Mã tài liệu đã tồn tại", "TaiLieu.code.required": "<PERSON>ã tài liệu không được để trống", "TaiLieu.name.required": "Tên tài liệu không được để trống", "TaiLieuPhongBan.model.duplicate": "Trùng phòng ban, vui lòng kiểm tra lại!", "TaiLieuPhongBan.IdTaiLieu.not-found": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin tài liệu", "TaiLieuPhongBan.IdTaiLieu.required": "<PERSON><PERSON><PERSON> liệu cần chia sẻ không được để trống", "TaiLieuPhongBan.IdPhongBan.required": "Phòng ban được chia sẻ không được để trống", "TaiLieuPhongBan.IdPhongBan.existed": "Phòng ban đã được chia sẻ tài liệu"}