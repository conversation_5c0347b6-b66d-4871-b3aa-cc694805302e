﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxKhoaQuery : IRequest<List<KhoaSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy khoa cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxKhoaQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxKhoaQuery, List<KhoaSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<KhoaSelectItemModel>> Handle(GetComboboxKhoaQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = KhoaConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvKhoas
                                select new KhoaSelectItemModel()
                                {
                                    IdKhoa = dt.IdKhoa,
                                    MaKhoa = dt.MaKhoa,
                                    TenKhoa = dt.TenKhoa
                                })
                                .Distinct() // Loại bỏ các bản ghi trùng lặp
                                .OrderBy(x => x.TenKhoa); // Sắp xếp theo tên khoa sau khi đã loại bỏ trùng lặp

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenKhoa.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterKhoaQuery : IRequest<PaginationList<KhoaBaseModel>>
    {
        public KhoaFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách khoa có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterKhoaQuery(KhoaFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterKhoaQuery, PaginationList<KhoaBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<KhoaBaseModel>> Handle(GetFilterKhoaQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvKhoas
                            select new KhoaBaseModel
                            {
                                IdKhoa = dt.IdKhoa,
                                MaKhoa = dt.MaKhoa,
                                TenKhoa = dt.TenKhoa,
                                TenKhoaEn = dt.TenKhoaEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenKhoa.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<KhoaBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetKhoaByIdQuery : IRequest<KhoaModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin khoa theo id
        /// </summary>
        /// <param name="id">Id khoa</param>
        public GetKhoaByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetKhoaByIdQuery, KhoaModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<KhoaModel> Handle(GetKhoaByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = KhoaConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvKhoas.FirstOrDefaultAsync(x => x.IdKhoa == id);

                    return AutoMapperUtils.AutoMap<SvKhoa, KhoaModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetListRawKhoaQuery : IRequest<List<KhoaSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy khoa cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetListRawKhoaQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetListRawKhoaQuery, List<KhoaSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<KhoaSelectItemModel>> Handle(GetListRawKhoaQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                List<HeSelectItemModel> listHe = await _mediator.Send(new GetComboboxHeQuery());

                List<KhoaSelectItemModel> listKhoa = await _mediator.Send(new GetComboboxKhoaQuery());

                List<LopSelectItemModel> listLop = await _mediator.Send(new GetComboboxLopQuery());

                var list = (from lop in listLop
                            join he in listHe on lop.IdHe equals he.IdHe
                            join khoa in listKhoa on lop.IdKhoa equals khoa.IdKhoa
                            select new KhoaSelectItemModel()
                            {
                                IdHe = he.IdHe,
                                IdKhoa = khoa.IdKhoa,
                                MaKhoa = khoa.MaKhoa,
                                TenKhoa = khoa.TenKhoa
                            })
                                .OrderBy(x => x.TenKhoa)
                                .DistinctBy(x => new { x.IdHe, x.IdKhoa })
                                .ToList();

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenKhoa.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
