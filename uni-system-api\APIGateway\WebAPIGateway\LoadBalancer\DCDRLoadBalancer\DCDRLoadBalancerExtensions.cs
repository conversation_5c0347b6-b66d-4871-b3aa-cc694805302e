﻿using Ocelot.Configuration;
using Ocelot.LoadBalancer.LoadBalancers;
using Ocelot.Requester;
using Ocelot.ServiceDiscovery.Providers;

namespace Ocelot.DependencyInjection
{
    public static class DCDRLoadBalancerExtensions
    {
        public static IOcelotBuilder AddDCDRLoadBalancer(this IOcelotBuilder builder)
        {
            DCDRLoadBalancerDelegatingHandler delegatingHandler = null;
            builder.Services.AddTransient<DCDRLoadBalancerDelegatingHandler>();
            builder.Services.AddTransient(s =>
            {
                delegatingHandler = s.GetService<DCDRLoadBalancerDelegatingHandler>();
                // Make this handler global.
                return new GlobalDelegatingHandler(delegatingHandler);
            });

            Func<IServiceProvider, DownstreamRoute, IServiceDiscoveryProvider, DCDRLoadBalancer> loadBalancerFactoryFunc
                = (serviceProvider, route, serviceDiscoveryProvider) =>
                {
                    return new DCDRLoadBalancer(serviceProvider, route, serviceDiscoveryProvider.GetAsync);
                };

            builder.AddCustomLoadBalancer(loadBalancerFactoryFunc);
            return builder;
        }
    }
}
