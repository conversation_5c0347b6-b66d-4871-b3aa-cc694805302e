﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdatePhuongThucDongCommand : IRequest<Unit>
    {
        public UpdatePhuongThucDongModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Cập nhật phương thức đóng
        /// </summary>
        /// <param name="model">Thông tin phương thức đóng cần cập nhật</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public UpdatePhuongThucDongCommand(UpdatePhuongThucDongModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<UpdatePhuongThucDongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdatePhuongThucDongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Update {PhuongThucDongConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SvPhuongThucDongs.FirstOrDefaultAsync(x => x.IdPhuongThucDong == model.IdPhuongThucDong);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {PhuongThucDongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvPhuongThucDongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {PhuongThucDongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(PhuongThucDongConstant.BuildCacheKey(entity.IdPhuongThucDong.ToString()));
                _cacheService.Remove(PhuongThucDongConstant.BuildCacheKey());

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập phương thức đóng : {entity.PhuongThucDong}",
                    ObjectCode = PhuongThucDongConstant.CachePrefix,
                    ObjectId = entity.IdPhuongThucDong.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
