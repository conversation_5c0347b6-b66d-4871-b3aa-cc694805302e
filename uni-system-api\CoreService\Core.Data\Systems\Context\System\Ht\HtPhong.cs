﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("htPhong")]
    public class HtPhong
    {

        public HtPhong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phong")]
        public int IdPhong { get; set; }

        [<PERSON>umn("Ma_phong"), MaxLength(5)]
        public string MaPhong { get; set; }

        [<PERSON><PERSON><PERSON>("phong"), MaxLength(100)]
        public string <PERSON>ong { get; set; }



    }
}
