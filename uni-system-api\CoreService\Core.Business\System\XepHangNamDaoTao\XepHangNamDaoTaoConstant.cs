﻿using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepHangNamDaoTaoConstant
    {
        public const string CachePrefix = CacheConstants.XEP_HANG_NAM_DAO_TAO;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
