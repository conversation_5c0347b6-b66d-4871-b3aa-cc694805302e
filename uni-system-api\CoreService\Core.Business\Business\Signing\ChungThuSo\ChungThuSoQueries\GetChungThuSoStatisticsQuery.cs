﻿using Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetChungThuSoStatisticsQuery : IRequest<ChungThuSoStatisticsModel>
    {
        /// <summary>
        /// L<PERSON>y thống kê chứng thư số
        /// </summary>
        public GetChungThuSoStatisticsQuery()
        {
        }

        public class Handler : IRequestHandler<GetChungThuSoStatisticsQuery, ChungThuSoStatisticsModel>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<ChungThuSoStatisticsModel> Handle(GetChungThuSoStatisticsQuery request, CancellationToken cancellationToken)
            {
                var now = DateTime.Now;
                
                var statistics = new ChungThuSoStatisticsModel();

                // Tổng số chứng thư số
                statistics.TotalCount = await _dataContext.SgChungThuSos.CountAsync();

                // Số chứng thư số đang hoạt động
                statistics.ActiveCount = await _dataContext.SgChungThuSos
                    .Where(x => x.IsActive)
                    .CountAsync();

                // Số chứng thư số hết hạn
                statistics.ExpiredCount = await _dataContext.SgChungThuSos
                    .Where(x => x.NotAfter < now)
                    .CountAsync();

                // Số chứng thư số sắp hết hạn (trong 30 ngày)
                var thirtyDaysFromNow = now.AddDays(30);
                statistics.ExpiringCount = await _dataContext.SgChungThuSos
                    .Where(x => x.NotAfter >= now && x.NotAfter <= thirtyDaysFromNow)
                    .CountAsync();

                // Thống kê theo nguồn (Source)
                statistics.BySource = await _dataContext.SgChungThuSos
                    .GroupBy(x => x.Source)
                    .Select(g => new ChungThuSoSourceStatistics
                    {
                        Source = g.Key,
                        Count = g.Count(),
                        ActiveCount = g.Count(x => x.IsActive),
                        ExpiredCount = g.Count(x => x.NotAfter < now)
                    })
                    .ToListAsync();

                // Thống kê theo tháng tạo (6 tháng gần nhất)
                var sixMonthsAgo = now.AddMonths(-6);
                statistics.ByMonth = await _dataContext.SgChungThuSos
                    .Where(x => x.CreatedDate >= sixMonthsAgo)
                    .GroupBy(x => new { x.CreatedDate.Value.Year, x.CreatedDate.Value.Month })
                    .Select(g => new ChungThuSoMonthStatistics
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        Count = g.Count()
                    })
                    .OrderBy(x => x.Year)
                    .ThenBy(x => x.Month)
                    .ToListAsync();

                return statistics;
            }
        }
    }

    /// <summary>
    /// Model thống kê chứng thư số
    /// </summary>
    public class ChungThuSoStatisticsModel
    {
        /// <summary>
        /// Tổng số chứng thư số
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Số chứng thư số đang hoạt động
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// Số chứng thư số hết hạn
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// Số chứng thư số sắp hết hạn (trong 30 ngày)
        /// </summary>
        public int ExpiringCount { get; set; }

        /// <summary>
        /// Thống kê theo nguồn
        /// </summary>
        public List<ChungThuSoSourceStatistics> BySource { get; set; } = new List<ChungThuSoSourceStatistics>();

        /// <summary>
        /// Thống kê theo tháng
        /// </summary>
        public List<ChungThuSoMonthStatistics> ByMonth { get; set; } = new List<ChungThuSoMonthStatistics>();
    }

    /// <summary>
    /// Thống kê theo nguồn
    /// </summary>
    public class ChungThuSoSourceStatistics
    {
        public short Source { get; set; }
        public int Count { get; set; }
        public int ActiveCount { get; set; }
        public int ExpiredCount { get; set; }
    }

    /// <summary>
    /// Thống kê theo tháng
    /// </summary>
    public class ChungThuSoMonthStatistics
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public int Count { get; set; }
        public string MonthYear => $"{Month:00}/{Year}";
    }
}
