﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetThamSoQuyCheQuery : IRequest<List<ThamSoQuyCheModel>>
    {
        public int QuyChe { get; set; }

        /// <summary>
        /// Lấy danh sách tham số quy chế
        /// </summary>
        /// <param name="quyChe">Quy chế</param>
        public GetThamSoQuyCheQuery(int quyChe)
        {
            QuyChe = quyChe;
        }

        public class Handler : IRequestHandler<GetThamSoQuyCheQuery, List<ThamSoQuyCheModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ThamSoQuyCheModel>> Handle(GetThamSoQuyCheQuery request, CancellationToken cancellationToken)
            {
                var quyChe = request.QuyChe;
                string cacheKey = ThamSoHeThongConstant.BuildCacheKey(quyChe.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvThamSoQuyChes.Where(x => x.QuyChe == quyChe).ToListAsync();
                    return AutoMapperUtils.AutoMap<SvThamSoQuyChe, ThamSoQuyCheModel>(entity);
                });
                return item;
            }
        }
    }
}
