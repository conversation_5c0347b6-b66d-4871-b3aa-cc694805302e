﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svTonGiao")]
    public class SvTonGiao
    {
        
        public SvTonGiao()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_ton_giao")]
        public int IdTonGiao { get; set; }

        [Column("Ma_ton_giao"), MaxLength(5)]
        public string MaTonGiao { get; set; }

        [Column("Ton_giao"), MaxLength(50)]
        public string TonGiao{ get; set; }

        [Column("Ton_giao_en"), MaxLength(50)]
        public string TonGiaoEn { get; set; }

        
    }
}
