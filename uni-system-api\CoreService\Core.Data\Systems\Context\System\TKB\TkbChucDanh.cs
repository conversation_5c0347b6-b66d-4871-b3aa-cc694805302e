﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbChucDanh")]
    public class TkbChucDanh
    {

        public TkbChucDanh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_chuc_danh")]
        public int IdChucDanh { get; set; }

        [Column("Ma_chuc_danh"), MaxLength(10)]
        public string MaChucDanh { get; set; }

        [Column("Chuc_danh"), MaxLength(100)]
        public string ChucDanh { get; set; }

    }
}
