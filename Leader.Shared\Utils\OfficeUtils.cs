﻿using Core.Shared;
using Spire.Doc;
using System.Collections.Generic;
using System.IO;

namespace Leader.Shared
{
    public static class OfficeUtils
    {
        /// <summary>
        /// Tạo file docx từ file docx và metadata
        /// </summary>
        /// <param name="content">Stream file</param>
        /// <param name="list">Danh sách meta data</param>
        public static void GenDocxFromDocxAndMetaData(ref MemoryStream content, List<KeyValueModel> list)
        {
            Document document = new Document();
            document.LoadFromStream(content, Spire.Doc.FileFormat.Auto);
            list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
            MemoryStream convertData = new MemoryStream(0);
            document.SaveToStream(convertData, FileFormat.Docx);
            content = convertData;
        }

        /// <summary>
        /// Tạo file docx từ file docx và metadata
        /// </summary>
        /// <param name="pathFileSource"><PERSON><PERSON><PERSON>ng dẫn lưu file</param>
        /// <param name="list"><PERSON>h sách meta data</param>
        /// <returns>Stream of file</returns>
        public static MemoryStream GenDocxFromDocxAndMetaData(string pathFileSource, List<KeyValueModel> list)
        {
            Document document = new Document();
            document.LoadFromFile(pathFileSource);
            list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
            MemoryStream convertData = new MemoryStream(0);
            document.SaveToStream(convertData, FileFormat.Docx);
            return convertData;
        }

        /// <summary>
        /// Tạo file docx từ file docx và metadata
        /// </summary>
        /// <param name="pathFileSource">Đường dẫn lưu file nguồn</param>
        /// <param name="pathFileSave">Đường dẫn lưu file đích</param>
        /// <param name="list">Danh sách meta data</param>
        /// <returns></returns>
        public static void GenDocxFromDocxAndMetaDataAndSaveToFile(string pathFileSource, string pathFileSave, List<KeyValueModel> list)
        {
            Document document = new Document();
            document.LoadFromFile(pathFileSource);
            list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
            MemoryStream convertData = new MemoryStream(0);
            document.SaveToFile(pathFileSave, FileFormat.Docx);
        }
    }
}
