﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiQuyetDinhCommand : IRequest<Unit>
    {
        public CreateLoaiQuyetDinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiQuyetDinhCommand(CreateLoaiQuyetDinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiQuyetDinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiQuyetDinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiQuyetDinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiQuyetDinhModel, SvLoaiQuyetDinh>(model);

                var checkCode = await _dataContext.SvLoaiQuyetDinhs.AnyAsync(x => x.MaQd == entity.MaQd);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiQuyetDinh.Existed", entity.TenLoaiQd.ToString()]}");
                }

                await _dataContext.SvLoaiQuyetDinhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiQuyetDinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại quyết định: {entity.TenLoaiQd}",
                    ObjectCode = LoaiQuyetDinhConstant.CachePrefix,
                    ObjectId = entity.IdLoaiQd.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyLoaiQuyetDinhCommand : IRequest<Unit>
    {
        public CreateManyLoaiQuyetDinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyLoaiQuyetDinhCommand(CreateManyLoaiQuyetDinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyLoaiQuyetDinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyLoaiQuyetDinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {LoaiQuyetDinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listMaLoaiQuyetDinhAdd = model.listLoaiQuyetDinhModels.Select(x => x.MaQd).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyLoaiQuyetDinhModel, SvLoaiQuyetDinh>(model);

                // Check data duplicate
                if ( listMaLoaiQuyetDinhAdd.Count() != listMaLoaiQuyetDinhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if ( await _dataContext.SvLoaiQuyetDinhs.AnyAsync(x => listMaLoaiQuyetDinhAdd.Contains(x.MaQd)))
                {
                    throw new ArgumentException($"{_localizer["LoaiQuyetDinh.Existed"]}");
                }

                var listEntity = model.listLoaiQuyetDinhModels.Select(x => new SvLoaiQuyetDinh()
                {
                    IdLoaiQd = x.IdLoaiQd,
                    MaQd = x.MaQd,
                    TenLoaiQd = x.TenLoaiQd,
                    ChuyenLop = x.ChuyenLop,
                    ThoiHoc = x.ThoiHoc,
                    NgungHoc = x.NgungHoc,
                    HocTiep = x.HocTiep,
                    ChuyenTruongDi = x.ChuyenTruongDi,
                    ChuyentruongDen = x.ChuyentruongDen,
                    ThoiHocQuyChe = x.ThoiHocQuyChe,
                    XoaTenkhoiLop = x.XoaTenkhoiLop

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdLoaiQd).ToList();

                Log.Information($"Create many {LoaiQuyetDinhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import loại quyết định từ file excel",
                    ObjectCode = LoaiQuyetDinhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateLoaiQuyetDinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiQuyetDinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiQuyetDinhCommand(int id, UpdateLoaiQuyetDinhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiQuyetDinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiQuyetDinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiQuyetDinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvLoaiQuyetDinhs.FirstOrDefaultAsync(dt => dt.IdLoaiQd == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.SvLoaiQuyetDinhs.AnyAsync(x =>  x.MaQd == model.MaQd && x.IdLoaiQd != model.IdLoaiQd);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiQuyetDinh.Existed", model.TenLoaiQd.ToString()]}");
                }

                Log.Information($"Before Update {LoaiQuyetDinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvLoaiQuyetDinhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiQuyetDinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiQuyetDinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại quyết định: {entity.TenLoaiQd}",
                    ObjectCode = LoaiQuyetDinhConstant.CachePrefix,
                    ObjectId = entity.IdLoaiQd.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey(entity.IdLoaiQd.ToString()));
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiQuyetDinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiQuyetDinhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiQuyetDinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiQuyetDinhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiQuyetDinhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvLoaiQuyetDinhs.FirstOrDefaultAsync(x => x.IdLoaiQd == id);

                _dataContext.SvLoaiQuyetDinhs.Remove(entity);

                Log.Information($"Delete {LoaiQuyetDinhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại quyết định: {entity.TenLoaiQd}",
                    ObjectCode = LoaiQuyetDinhConstant.CachePrefix,
                    ObjectId = entity.IdLoaiQd.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey());
                _cacheService.Remove(LoaiQuyetDinhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
