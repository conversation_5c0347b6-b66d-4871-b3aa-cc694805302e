using Core.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Leader.Shared
{

    #region 
    /*
      public static List<PermissionSeedModel> PERMISSION_LEADER = new List<PermissionSeedModel>() {
            new PermissionSeedModel("<PERSON><PERSON>ơng thức tuyển sinh", "PHUONG_THUC_TUYEN_SINH_VIEW", "Xem thông tin phương thức tuyển sinh"),
            new PermissionSeedModel("Phương thức tuyển sinh", "PHUONG_THUC_TUYEN_SINH_ADD", "Thêm mới phương thức tuyển sinh"),
            new PermissionSeedModel("<PERSON><PERSON>ơng thức tuyển sinh", "PHUONG_THUC_TUYEN_SINH_EDIT", "Cập nhật phương thức tuyển sinh"),
            new PermissionSeedModel("Phương thức tuyển sinh", "PHUONG_THUC_TUYEN_SINH_DELETE", "<PERSON><PERSON><PERSON> phương thức tuyển sinh"),

            new PermissionSeedModel("Thống kê tài chính", "THONG_KE_TAI_CHINH_VIEW", "Xem thống kê tài chính"),

            new PermissionSeedModel("Thống kê đào tạo", "THONG_KE_SO_LUONG_LOP_VIEW", "Xem thống kê số lượng lớp tín chỉ"),
            new PermissionSeedModel("Thống kê đào tạo", "THONG_KE_DANH_SACH_LOP_VIEW", "Xem thống kê danh sách lớp tín chỉ"),
            new PermissionSeedModel("Thống kê đào tạo", "THONG_KE_TO_CHUC_THI_VIEW", "Xem thống kê tổ chức thi"),

            new PermissionSeedModel("Quy mô sinh viên", "QUY_MO_SINH_VIEN_VIEW", "Xem quy mô sinh viên"),

            new PermissionSeedModel("Phân quyền dữ liệu", "VIEW_ALL_DATA", "Xem dữ liệu toàn khoa"),

            new PermissionSeedModel("Tổng hợp quy mô sinh viên", "TONG_HOP_QUY_MO_SINH_VIEN_VIEW", "Xem tổng hợp quy mô sinh viên"),
            new PermissionSeedModel("Tổng hợp lớp tín chỉ", "TONG_HOP_LOP_TIN_CHI_VIEW", "Xem tổng hợp lớp tín chỉ"),

            new PermissionSeedModel("Thống kê nhập học", "THONG_KE_NHAP_HOC_VIEW", "Xem thống kê nhập học"),
        };
    */
    #endregion

    public enum PermissionLeaderEnum
    {
        #region Bậc đào tạo khoa học
        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Xem thông tin bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_VIEW,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Thêm mới bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_CREATE,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Cập nhật bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_UPDATE,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Xóa bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_DELETE,
        #endregion
    }
}