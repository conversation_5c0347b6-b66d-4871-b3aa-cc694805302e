<Process Name="<PERSON><PERSON> trình Học liệu điện tử - Quy trình tổng" CanBeInlined="false" Tags="" LogEnabled="false">
  <Designer />
  <Actors>
    <Actor Name="G<PERSON>i đề xuất - học liệu" Rule="CheckRole" Value="Gửi đề xuất - học liệu" />
    <Actor Name="Duyệt đề xuất - học liệu" Rule="CheckRole" Value="Duyệt đề xuất - học liệu" />
    <Actor Name="Duyệt thẩm định - học liệu" Rule="CheckRole" Value="Duyệt thẩm định - học liệu" />
    <Actor Name="Yêu cầu nghiệm thu - học liệu" Rule="CheckRole" Value="Yêu cầu nghiệm thu - học liệu" />
    <Actor Name="Duyệt nghiệm thu - học liệu" Rule="CheckRole" Value="Duyệt nghiệm thu - học liệu" />
    <Actor Name="Yêu cầu thẩm định - học liệu" Rule="CheckRole" Value="Yêu cầu thẩm định - học liệu" />
  </Actors>
  <Parameters>
    <Parameter Name="RequireComment" Type="String" Purpose="Temporary" />
    <Parameter Name="RequireFeedback" Type="String" Purpose="Temporary" />
  </Parameters>
  <Commands>
    <Command Name="Gửi đề xuất" />
    <Command Name="Duyệt đề xuất" />
    <Command Name="Từ chối" />
    <Command Name="Yêu cầu chỉnh sửa">
      <InputParameters>
        <ParameterRef Name="RequireComment" IsRequired="false" DefaultValue="" NameRef="RequireComment" />
      </InputParameters>
    </Command>
    <Command Name="Yêu cầu thẩm định" />
    <Command Name="Duyệt thẩm định">
      <InputParameters>
        <ParameterRef Name="RequireFeedback" IsRequired="false" DefaultValue="" NameRef="RequireFeedback" />
      </InputParameters>
    </Command>
    <Command Name="Yêu cầu nghiệm thu" />
    <Command Name="Duyệt nghiệm thu">
      <InputParameters>
        <ParameterRef Name="RequireFeedback" IsRequired="false" DefaultValue="" NameRef="RequireFeedback" />
      </InputParameters>
    </Command>
  </Commands>
  <Activities>
    <Activity Name="Gửi đề xuất" State="Mới" IsInitial="true" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Designer X="400" Y="250" Hidden="false" />
    </Activity>
    <Activity Name="Xét duyệt" State="Chờ xét duyệt" IsInitial="false" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"ChoXetDuyet\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="680.0000000000001" Y="250" Color="#FFC107" Hidden="false" />
    </Activity>
    <Activity Name="Sản xuất học liệu" State="Đã duyệt đề xuất" IsInitial="false" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"DaDuyetDeXuat\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="960" Y="250" Color="#FFC107" Hidden="false" />
    </Activity>
    <Activity Name="Nghiệm thu" State="Chờ nghiệm thu" IsInitial="false" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"ChoNghiemThu\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="1610" Y="390" Color="#FFC107" Hidden="false" />
    </Activity>
    <Activity Name="Hoàn thành sản xuất" State="Đã nghiệm thu" IsInitial="false" IsFinal="true" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"DaNghiemThu\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="1610" Y="550" Hidden="false" />
    </Activity>
    <Activity Name="Thẩm định " State="Chờ thẩm định" IsInitial="false" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"ChoThamDinh\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="1270" Y="260" Color="#FFC107" Hidden="false" />
    </Activity>
    <Activity Name="Từ chối" State="Từ chối" IsInitial="false" IsFinal="true" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Designer X="690" Y="380" Color="#F44336" Hidden="false" />
    </Activity>
    <Activity Name="Đã thẩm định" State="Đã thẩm định" IsInitial="false" IsFinal="false" IsForSetState="true" IsAutoSchemeUpdate="true">
      <Implementation>
        <ActionRef Order="1" NameRef="SetParameter">
          <ActionParameter><![CDATA[{"ParameterName":"TrangThaiXuLy","Value":"\"DaThamDinh\"","ForRootProcess":false}]]></ActionParameter>
        </ActionRef>
      </Implementation>
      <Designer X="1610" Y="250" Color="#FFC107" Hidden="false" />
    </Activity>
  </Activities>
  <Transitions>
    <Transition Name="Gửi đề xuất_Xét duyệt_1" To="Xét duyệt" From="Gửi đề xuất" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Gửi đề xuất - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Gửi đề xuất" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="Xét duyệt_Activity_1_1" To="Sản xuất học liệu" From="Xét duyệt" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt đề xuất - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Duyệt đề xuất" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="Activity_1_Activity_1_1_1_1_1" To="Thẩm định " From="Sản xuất học liệu" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Yêu cầu thẩm định - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Yêu cầu thẩm định" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="Xét duyệt_Gửi đề xuất_1" To="Gửi đề xuất" From="Xét duyệt" Classifier="Reverse" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt đề xuất - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Yêu cầu chỉnh sửa" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer X="633" Y="196" Hidden="false" Color="#2980B9" />
    </Transition>
    <Transition Name="Xét duyệt_Từ chối_1" To="Từ chối" From="Xét duyệt" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt đề xuất - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Từ chối" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" Color="#F44336" />
    </Transition>
    <Transition Name="thẩm định _Sản xuất học liệu_1" To="Sản xuất học liệu" From="Thẩm định " Classifier="Reverse" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt thẩm định - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Yêu cầu chỉnh sửa" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer X="1200" Y="189" Hidden="false" Color="#2980B9" />
    </Transition>
    <Transition Name="thẩm định _Từ chối_1" To="Từ chối" From="Thẩm định " Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt thẩm định - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Từ chối" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer X="1304.111111111111" Y="378.8888888888889" Hidden="false" Color="#F44336" />
    </Transition>
    <Transition Name="Tổng hợp nghiệm thu_hoàn thành sản xuất_1" To="Hoàn thành sản xuất" From="Nghiệm thu" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt nghiệm thu - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Duyệt nghiệm thu" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="Tổng hợp nghiệm thu_Từ chối_1" To="Từ chối" From="Nghiệm thu" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt nghiệm thu - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Từ chối" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" Color="#F44336" />
    </Transition>
    <Transition Name="thẩm định _Đã thẩm định_1" To="Đã thẩm định" From="Thẩm định " Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt thẩm định - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Duyệt thẩm định" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="Đã thẩm định_Tổng hợp nghiệm thu_1" To="Nghiệm thu" From="Đã thẩm định" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Yêu cầu nghiệm thu - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Yêu cầu nghiệm thu" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer Hidden="false" />
    </Transition>
    <Transition Name="nghiệm thu_Đã thẩm định_1" To="Đã thẩm định" From="Nghiệm thu" Classifier="Direct" AllowConcatenationType="And" RestrictConcatenationType="And" ConditionsConcatenationType="And" DisableParentStateControl="false">
      <Restrictions>
        <Restriction Type="Allow" NameRef="Duyệt nghiệm thu - học liệu" />
      </Restrictions>
      <Triggers>
        <Trigger Type="Command" NameRef="Yêu cầu chỉnh sửa" />
      </Triggers>
      <Conditions>
        <Condition Type="Always" />
      </Conditions>
      <Designer X="1820.000457676377" Y="341.99987631734905" Hidden="false" Color="#2980B9" />
    </Transition>
  </Transitions>
</Process>