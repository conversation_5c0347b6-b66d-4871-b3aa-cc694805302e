﻿using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// L<PERSON>y nhật ký hệ thống theo Id
    /// </summary>
    /// <param name="id">Id nhật ký hệ thống</param>
    /// <returns>Thông tin nhật ký hệ thống</returns>
    public class SystemLogGetByIdQuery : IRequest<SystemLog>
    {
        public string Id { get; set; }

        public SystemLogGetByIdQuery(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<SystemLogGetByIdQuery, SystemLog>
        {
            private readonly IMongoCollection<SystemLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemReadDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
                }
            }

            public async Task<SystemLog> Handle(SystemLogGetByIdQuery request, CancellationToken cancellationToken)
            {
                SystemLog entity = null;
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    entity = await _logs.Find(log => log.Id == request.Id).FirstOrDefaultAsync();
                }
                else
                {
                    int idLog = 0;
                    int.TryParse(request.Id, out idLog);
                    var dt = await _dataContext.SystemLogs.AsNoTracking().FirstOrDefaultAsync(x => x.Id == idLog);

                    entity = AutoMapperUtils.AutoMap<SystemLogEntity, SystemLog>(dt);
                }

                return entity;
            }
        }
    }
}
