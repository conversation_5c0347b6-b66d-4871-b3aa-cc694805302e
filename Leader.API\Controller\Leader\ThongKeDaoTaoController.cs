﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Leader.Business;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Nest;
using System.Linq;
using System.Threading.Tasks;

namespace Leader.API.Controller
{
    /// <summary>
    /// Module Thống kê số lượng lớp tín chỉ
    /// </summary>
    [ApiController]
    [Route("leader/v1/thong-ke-dao-tao")]
    [ApiExplorerSettings(GroupName = "08. Thống kê đào tạo")]
    [Authorize]
    public class ThongKeSoLuongController : ApiControllerBase
    {
        public ThongKeSoLuongController(IMediator mediator, IStringLocalizer<Resources> localizer) : base(mediator, localizer)
        {
        }

        /// <summary>
        /// Lấy danh sách thống kê số lượng lớp tín chỉ theo điều kiện lọc
        /// </summary> 
        /// <param name="filter"><PERSON><PERSON>ều kiện lọc</param>
        /// <returns><PERSON>h sách thống kê số lượng lớp tín chỉ</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("count-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeSoLuongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "THONG_KE_SO_LUONG_LOP_VIEW")]
        public async Task<IActionResult> CountFilter([FromBody] ThongKeDaoTaoQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Lấy danh sách claims của người dùng từ http context
                string[] permission = HttpContext.User.Claims?.FirstOrDefault(c => c.Type == ClaimConstants.PERMISSTTIONS)?.Value?.Split(',');

                //Nếu người dùng không có quyền view all data thì cập nhật Id khoa về thành thông tin khoa hiện tại của người dùng
                if (!permission.Any(x => x == "VIEW_ALL_DATA"))
                {
                    var user = await _mediator.Send(new GetUserByIdQuery(u.UserId));
                    if (user != null)
                    {
                        filter.IDKhoa = user.IdKhoa;
                    }
                }

                return await _mediator.Send(new GetThongKeSoLuongByFilterQuery(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách thống kê danh sách lớp tín chỉ theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách thống kê danh sách lớp tín chỉ</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("list-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeDanhSachBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "THONG_KE_DANH_SACH_LOP_VIEW")]
        public async Task<IActionResult> ListFilter([FromBody] ThongKeDaoTaoQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Lấy danh sách claims của người dùng từ http context
                string[] permission = HttpContext.User.Claims?.FirstOrDefault(c => c.Type == ClaimConstants.PERMISSTTIONS)?.Value?.Split(',');

                //Nếu người dùng không có quyền view all data thì cập nhật Id khoa về thành thông tin khoa hiện tại của người dùng
                if (!permission.Any(x => x == "VIEW_ALL_DATA"))
                {
                    var user = await _mediator.Send(new GetUserByIdQuery(u.UserId));
                    if (user != null)
                    {
                        filter.IDKhoa = user.IdKhoa;
                    }
                }

                return await _mediator.Send(new GetThongKeDanhSachByFilterQuery(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách thống kê tổ chức thi theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách thống kê tổ chức thi</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("test-organization-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeToChucThiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "THONG_KE_TO_CHUC_THI_VIEW")]
        public async Task<IActionResult> TestOrganizationFilter([FromBody] ThongKeDaoTaoQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Lấy danh sách claims của người dùng từ http context
                string[] permission = HttpContext.User.Claims?.FirstOrDefault(c => c.Type == ClaimConstants.PERMISSTTIONS)?.Value?.Split(',');

                //Nếu người dùng không có quyền view all data thì cập nhật Id khoa về thành thông tin khoa hiện tại của người dùng
                if (!permission.Any(x => x == "VIEW_ALL_DATA"))
                {
                    var user = await _mediator.Send(new GetUserByIdQuery(u.UserId));
                    if (user != null)
                    {
                        filter.IDKhoa = user.IdKhoa;
                    }
                }

                return await _mediator.Send(new GetThongKeToChucThiByFilterQuery(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách tổng hợp lớp tín chỉ theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách tổng hợp lớp tín chỉ</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("general")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeSoLuongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, "TONG_HOP_LOP_TIN_CHI_VIEW")]
        public async Task<IActionResult> Summary([FromBody] ThongKeDaoTaoQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetTongHopLopTinChiByFilterQuery(filter, u.SystemLog));
            });
        }
    }
}
