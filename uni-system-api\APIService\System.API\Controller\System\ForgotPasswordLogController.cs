﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.DataLog;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API
{
    /// <summary>
    /// Module nhật ký quên mạt khẩu
    /// </summary>
    [ApiController]
    [Route("system/v1/forgot-password-log")]
    [ApiExplorerSettings(GroupName = "101. Forgot Password Log (Nhật ký quên mật khẩu)")]
    [Authorize]
    public class ForgotPasswordLogController : ApiControllerBase
    {
        public ForgotPasswordLogController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách nhật ký quên mật khẩu theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Đi<PERSON>u kiện lọc</param>
        /// <returns>Danh sách nhật ký quên mật khẩu</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.FORGOT_PASSWORD_LOG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<List<ForgotPasswordLog>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] ForgotPasswordLogQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new ForgotPasswordLogFilterQuery(filter));
            });
        }
    }
}
