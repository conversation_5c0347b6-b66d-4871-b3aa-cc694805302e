﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="lib\**" />
    <EmbeddedResource Remove="lib\**" />
    <None Remove="lib\**" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Leader.Data\Leader.Data.csproj" />
    <ProjectReference Include="..\Leader.Shared\Leader.Shared.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Business\Core.Business.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.DataLog\Core.DataLog.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Data\Core.Data.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
</Project>