﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>425b4f5f-9a2d-4859-8fc2-b0e291718a67</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\images\**" />
    <Content Remove="wwwroot\images\**" />
    <EmbeddedResource Remove="wwwroot\images\**" />
    <None Remove="wwwroot\images\**" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Leader.Business\Leader.Business.csproj" />
    <ProjectReference Include="..\..\uni-system-api\APIService\Core.API.Shared\Core.API.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
  </ItemGroup>
</Project>