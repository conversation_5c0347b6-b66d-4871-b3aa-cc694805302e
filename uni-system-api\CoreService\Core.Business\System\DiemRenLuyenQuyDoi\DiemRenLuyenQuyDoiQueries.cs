﻿using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxDiemRenLuyenQuyDoiQuery : IRequest<List<DiemRenLuyenQuyDoiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy điểm rèn luyện cơ bản cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDiemRenLuyenQuyDoiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDiemRenLuyenQuyDoiQuery, List<DiemRenLuyenQuyDoiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DiemRenLuyenQuyDoiSelectItemModel>> Handle(GetComboboxDiemRenLuyenQuyDoiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DiemRenLuyenQuyDoiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDiemRenLuyenQuyDois.OrderBy(x => x.XepLoai)
                                select new DiemRenLuyenQuyDoiSelectItemModel()
                                {
                                    IdXepLoai = dt.IdXepLoai,
                                    XepLoai = dt.XepLoai
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDiemRenLuyenQuyDoiQuery : IRequest<PaginationList<DiemRenLuyenQuyDoiBaseModel>>
    {
        public DiemRenLuyenQuyDoiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách điểm rèn luyện cơ bản có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDiemRenLuyenQuyDoiQuery(DiemRenLuyenQuyDoiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDiemRenLuyenQuyDoiQuery, PaginationList<DiemRenLuyenQuyDoiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DiemRenLuyenQuyDoiBaseModel>> Handle(GetFilterDiemRenLuyenQuyDoiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDiemRenLuyenQuyDois
                            select new DiemRenLuyenQuyDoiBaseModel
                            {
                                IdXepLoai = dt.IdXepLoai,
                                XepLoai = dt.XepLoai,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                DiemCong10 = dt.DiemCong10,
                                DiemCong4 = dt.DiemCong4
                            

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<DiemRenLuyenQuyDoiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDiemRenLuyenQuyDoiByIdQuery : IRequest<DiemRenLuyenQuyDoiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin điểm rèn luyện cơ bản theo id
        /// </summary>
        /// <param name="id">Id điểm rèn luyện cơ bản</param>
        public GetDiemRenLuyenQuyDoiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDiemRenLuyenQuyDoiByIdQuery, DiemRenLuyenQuyDoiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DiemRenLuyenQuyDoiModel> Handle(GetDiemRenLuyenQuyDoiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DiemRenLuyenQuyDoiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDiemRenLuyenQuyDois.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                    return AutoMapperUtils.AutoMap<SvDiemRenLuyenQuyDoi, DiemRenLuyenQuyDoiModel>(entity);
                });
                return item;
            }
        }
    }
}
