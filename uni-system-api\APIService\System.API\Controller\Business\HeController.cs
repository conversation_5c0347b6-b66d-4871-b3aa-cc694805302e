﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Core.Business.HeCommands;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/he")]
    [ApiExplorerSettings(GroupName = "11. Hệ")]
    [Authorize]
    public class HeController : ApiControllerBase
    {
        public HeController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// L<PERSON>y danh sách hệ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HeSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHeQuery(count, ts));
            });
        }


        /// <summary>
        /// Lấy danh sách Hệ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HeBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HeFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHeQuery(filter)));
        }

        /// <summary>
        /// Thêm mới hệ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHeModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HE_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HE_CREATE;


                return await _mediator.Send(new CreateHeCommand(model, u.SystemLog));
            });
        }
        /// <summary>
        /// Import excel hệ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHeModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HE_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HE_CREATE_MANY;


                return await _mediator.Send(new CreateManyHeCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa hệ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHeModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HE_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HE_UPDATE;


                /*model.CreateUserName = u.UserName;*/
                return await _mediator.Send(new UpdateHeCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Hệ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HE_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HE_DELETE;

                return await _mediator.Send(new DeleteHeCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết hệ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HeModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HE_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHeByIdQuery(id)));
        }
    }



}
