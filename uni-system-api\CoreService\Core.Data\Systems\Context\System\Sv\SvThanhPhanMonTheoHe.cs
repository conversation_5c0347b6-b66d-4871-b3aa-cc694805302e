﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svThanhPhanMonTheoHe")]
    public class SvThanhPhanMonTheoHe
    {

        public SvThanhPhanMonTheoHe()
        {

        }

        [Key]
        [Column("ID_thanh_phan")]
        public int IdThanhPhan { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("STT")]
        public int Stt { get; set; }

        [Column("Ty_le")]
        public int TyLe { get; set; }

        [Column("Ty_le_nhom")]
        public int TyLeNhom { get; set; }

        [Column("Nhom_thanh_phan")]
        public int NhomThanhPhan { get; set; }

        [Column("Chon_mac_dinh")]
        public bool ChonMacDinh { get; set; }




    }
}
