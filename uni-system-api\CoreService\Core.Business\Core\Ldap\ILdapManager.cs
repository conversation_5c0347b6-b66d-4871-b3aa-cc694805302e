﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.Core
{
    /// <summary>
    /// Ldap related contracts
    /// </summary>
    public interface ILdapManager
    {
        /// <summary>
        /// Check if user in Ldap 
        /// </summary>
        /// <param name="userName">Ldap user name</param>
        /// <param name="password">Ldap passsword</param>
        bool ValidateAuthentication(string userName, string password);
    }
}
