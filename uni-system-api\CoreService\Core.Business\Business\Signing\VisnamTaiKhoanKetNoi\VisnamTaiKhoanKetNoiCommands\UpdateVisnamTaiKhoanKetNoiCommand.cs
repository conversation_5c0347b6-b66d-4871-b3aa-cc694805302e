﻿using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateVisnamTaiKhoanKetNoiCommand : IRequest<Unit>
    {
        public UpdateVisnamTaiKhoanKetNoiModel Model { get; set; }

        /// <summary>
        /// Cập nhật tài khoản kết nối Visnam
        /// </summary>
        /// <param name="model">Thông tin tài khoản kết nối Visnam cần cập nhật</param>
        public UpdateVisnamTaiKhoanKetNoiCommand(UpdateVisnamTaiKhoanKetNoiModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateVisnamTaiKhoanKetNoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateVisnamTaiKhoanKetNoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {VisnamTaiKhoanKetNoiConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SgVisnamTaiKhoanKetNois.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {VisnamTaiKhoanKetNoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);
                entity.Secret = AesEncryption.Encrypt(entity.Secret, AesEncryption.KeyDefault);
                
                entity.ModifiedUserId = _contextAccessor.UserId;
                _dataContext.SgVisnamTaiKhoanKetNois.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {VisnamTaiKhoanKetNoiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(VisnamTaiKhoanKetNoiConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(VisnamTaiKhoanKetNoiConstant.BuildCacheKey());

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tài khoản kết nối Visnam cho User ID: {entity.UserId}",
                    ObjectCode = VisnamTaiKhoanKetNoiConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
