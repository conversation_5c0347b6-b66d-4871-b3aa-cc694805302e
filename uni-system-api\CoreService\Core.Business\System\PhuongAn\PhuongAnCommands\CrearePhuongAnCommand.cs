﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreatePhuongAnCommand : IRequest<Unit>
    {
        public CreatePhuongAnModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Thêm mới phương án
        /// </summary>
        /// <param name="model">Thông tin phương án cần thêm mới</param>
        /// <param name="requestUser">Thông tin người request</param>
        public CreatePhuongAnCommand(CreatePhuongAnModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<CreatePhuongAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreatePhuongAnCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Create {PhuongAnConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreatePhuongAnModel, SvPhuongAn>(model);

                var checkMaPhuongAn = await _dataContext.SvPhuongAns.AnyAsync(x => x.MaPhuongAn == entity.MaPhuongAn);
                if (checkMaPhuongAn)
                {
                    throw new ArgumentException($"{_localizer["system-application.MaPhuongAn.existed"]}");
                }

                await _dataContext.SvPhuongAns.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {PhuongAnConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phương án mã: {entity.MaPhuongAn}",
                    ObjectCode = PhuongAnConstant.CachePrefix,
                    ObjectId = entity.IdPhuongAn.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhuongAnConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
