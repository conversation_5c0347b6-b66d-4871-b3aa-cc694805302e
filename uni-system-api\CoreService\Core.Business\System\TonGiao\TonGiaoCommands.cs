﻿using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


        public class CreateTonGiaoCommand : IRequest<Unit>
        {
            public CreateTonGiaoModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateTonGiaoCommand(CreateTonGiaoModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateTonGiaoCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateTonGiaoCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {TonGiaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateTonGiaoModel, SvTonGiao>(model);

                    var checkCode = await _dataContext.SvTonGiaos.AnyAsync(x => x.IdTonGiao == entity.IdTonGiao || x.TonGiao == entity.TonGiao || x.MaTonGiao == entity.MaTonGiao);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TonGiao.Existed", entity.TonGiao.ToString()]}");
                    }

                    await _dataContext.SvTonGiaos.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {TonGiaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới tôn giáo: {entity.TonGiao}",
                        ObjectCode = TonGiaoConstant.CachePrefix,
                        ObjectId = entity.IdTonGiao.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(TonGiaoConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class CreateManyTonGiaoCommand : IRequest<Unit>
        {
            public CreateManyTonGiaoModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateManyTonGiaoCommand(CreateManyTonGiaoModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateManyTonGiaoCommand, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateManyTonGiaoCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create many {TonGiaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var listTonGiaoAdd = model.listTonGiaoModels.Select(x => x.TonGiao).ToList();
                    var listMaTonGiaoAdd = model.listTonGiaoModels.Select(x => x.MaTonGiao).ToList();
                    var entity = AutoMapperUtils.AutoMap<CreateManyTonGiaoModel, SvTonGiao>(model);

                    // Check data duplicate
                    if (listTonGiaoAdd.Count() != listTonGiaoAdd.Distinct().Count() || listMaTonGiaoAdd.Count() != listMaTonGiaoAdd.Distinct().Count())
                    {
                        throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                    }
                    // Check data exits DB
                    if (await _dataContext.SvTonGiaos.AnyAsync(x => listTonGiaoAdd.Contains(x.TonGiao)) || await _dataContext.SvTonGiaos.AnyAsync(x => listMaTonGiaoAdd.Contains(x.MaTonGiao)))
                    {
                        throw new ArgumentException($"{_localizer["TonGiao.Existed"]}");
                    }

                    var listEntity = model.listTonGiaoModels.Select(x => new SvTonGiao()
                    {
                        IdTonGiao = x.IdTonGiao,
                        MaTonGiao = x.MaTonGiao,
                        TonGiao = x.TonGiao,
                        TonGiaoEn = x.TonGiaoEn,
                     
                    }).ToList();

                    await _dataContext.AddRangeAsync(listEntity);
                    await _dataContext.SaveChangesAsync();

                    var createdIds = listEntity.Select(e => e.IdTonGiao).ToList();

                    Log.Information($"Create many {TonGiaoConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Import Tôn Giáo từ file excel",
                        ObjectCode = TonGiaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(createdIds)
                    });

                    //Xóa cache
                    _cacheService.Remove(TonGiaoConstant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class UpdateTonGiaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateTonGiaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateTonGiaoCommand(int id, UpdateTonGiaoModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateTonGiaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateTonGiaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {TonGiaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvTonGiaos.FirstOrDefaultAsync(dt => dt.IdTonGiao == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvTonGiaos.AnyAsync(x => (x.TonGiao == model.TonGiao || x.MaTonGiao == model.MaTonGiao) && x.IdTonGiao != model.IdTonGiao);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["TonGiao.Existed", model.TonGiao.ToString()]}");
                }

                Log.Information($"Before Update {TonGiaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvTonGiaos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TonGiaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {TonGiaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tôn giáo: {entity.TonGiao}",
                    ObjectCode = TonGiaoConstant.CachePrefix,
                    ObjectId = entity.IdTonGiao.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TonGiaoConstant.BuildCacheKey(entity.IdTonGiao.ToString()));
                _cacheService.Remove(TonGiaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteTonGiaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteTonGiaoCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteTonGiaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteTonGiaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {TonGiaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvTonGiaos.FirstOrDefaultAsync(x => x.IdTonGiao == id);

                _dataContext.SvTonGiaos.Remove(entity);

                Log.Information($"Delete {TonGiaoConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Tôn giáo: {entity.TonGiao}",
                    ObjectCode = TonGiaoConstant.CachePrefix,
                    ObjectId = entity.IdTonGiao.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TonGiaoConstant.BuildCacheKey());
                _cacheService.Remove(TonGiaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
