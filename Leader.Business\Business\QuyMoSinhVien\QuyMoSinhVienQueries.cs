﻿using Core.Business;
using Core.Data;
using Core.Shared;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    /// <summary>
    /// Lấy danh sách quy mô sinh viên theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh quy mô sinh viên</returns>
    public class GetQuyMoSinhVienByFilterQuery : IRequest<PaginationList<QuyMoSinhVienModel>>
    {
        public QuyMoSinhVienQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách quy mô sinh viên theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetQuyMoSinhVienByFilterQuery(QuyMoSinhVienQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetQuyMoSinhVienByFilterQuery, PaginationList<QuyMoSinhVienModel>>
        {
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(ILeaderCallStoreHelper callStoreHelper)
            {
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<QuyMoSinhVienModel>> Handle(GetQuyMoSinhVienByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreQuyMoSinhVienAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc);

                var data = new List<QuyMoSinhVienModel>();
                foreach (DataRow row in dataTable.Rows)
                {
                    data.Add(new QuyMoSinhVienModel()
                    {
                        TenHe = row.Field<string>("Ten_he"),
                        TenKhoa = row.Field<string>("Ten_khoa"),
                        KhoaHoc = row.Field<int>("Khoa_hoc"),
                        TenNganh = row.Field<string>("Ten_nganh"),
                        TongSoSV = row.Field<int?>("Tong_so_sv"),
                        TongSoSVNam = row.Field<int>("Tong_so_sv_nam"),
                        TongSoSVNu = row.Field<int>("Tong_so_sv_nu"),
                        TiLeSVNam = Math.Round((Convert.ToDouble(row.Field<int>("Tong_so_sv_nam")) / Convert.ToDouble(row.Field<int>("Tong_so_sv"))) * 100, 2),
                        TiLeSVNu = Math.Round((Convert.ToDouble(row.Field<int>("Tong_so_sv_nu")) / Convert.ToDouble(row.Field<int>("Tong_so_sv"))) * 100, 2)
                    });
                }

                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<QuyMoSinhVienModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }

    /// <summary>
    /// Lấy danh sách tổng hợp quy mô sinh viên theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách tổng hợp quy mô sinh viên</returns>
    public class GetTongHopQuyMoByFilterQuery : IRequest<PaginationList<TongHopQuyMoModel>>
    {
        public QuyMoSinhVienQueryFilter Filter { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy danh sách tổng hợp quy mô sin viên theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetTongHopQuyMoByFilterQuery(QuyMoSinhVienQueryFilter filter, SystemLogModel systemLog)
        {
            Filter = filter;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetTongHopQuyMoByFilterQuery, PaginationList<TongHopQuyMoModel>>
        {
            private readonly LeaderReadDataContext _dataContext;
            private readonly ILeaderCallStoreHelper _callStoreHelper;

            public Handler(LeaderReadDataContext dataContext, ILeaderCallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<PaginationList<TongHopQuyMoModel>> Handle(GetTongHopQuyMoByFilterQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var filter = request.Filter;
                // TODO: Cần chuyển đổi nghiệp vụ từ call store sang code để đáp ứng phân trang
                var dataTable = _callStoreHelper.CallStoreQuyMoSinhVienAsync(filter.IDHe, filter.IDKhoa, filter.IDNganh, filter.HocKy, filter.NamHoc, filter.KhoaHoc);

                var dataStore = dataTable.AsEnumerable().Select(row => new QuyMoSinhVienModel
                {
                    TenHe = row.Field<string>("Ten_he"),
                    TenKhoa = row.Field<string>("Ten_khoa"),
                    TongSoSV = row.Field<int?>("Tong_so_sv")
                }).GroupBy(x => new { x.TenHe, x.TenKhoa }).Select(group => new QuyMoSinhVienModel
                {
                    TenHe = group.Key.TenHe,
                    TenKhoa = group.Key.TenKhoa,
                    TongSoSV = group.Sum(z => z.TongSoSV)
                }).ToList();

                var dataKhoa = await _dataContext.SvKhoas.ToListAsync();
                var dataHe = await _dataContext.SvHes.OrderBy(x => x.TenHe).Select(dt => new TongHopQuyMoBaseModel
                {
                    TenHe = dt.TenHe,
                    Count = 0
                }).ToListAsync();

                var dataStoreDict = dataStore.ToDictionary(
                    k => (k.TenHe, k.TenKhoa),
                    v => v.TongSoSV);

                List<TongHopQuyMoModel> data = dataKhoa.Select(khoa => new TongHopQuyMoModel
                {
                    TenKhoa = khoa.TenKhoa,
                    ListTH = dataHe.Select(he => new TongHopQuyMoBaseModel
                    {
                        TenHe = he.TenHe,
                        Count = dataStoreDict.TryGetValue((he.TenHe, khoa.TenKhoa), out var TongSoSV) ? TongSoSV : 0
                    }).ToList()
                }).ToList();


                var totalCount = data.Count;

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize).ToList();
                int dataCount = data.Count;

                return new PaginationList<TongHopQuyMoModel>
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = data
                };
            }
        }
    }
}

