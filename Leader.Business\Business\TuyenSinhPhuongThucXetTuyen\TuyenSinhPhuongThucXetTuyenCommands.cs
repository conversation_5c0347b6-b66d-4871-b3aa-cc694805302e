﻿using Core.Business;
using Core.Data;
using Core.Shared;
using Leader.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Leader.Business
{
    public class CreateTuyenSinhPhuongThucXetTuyenCommand : IRequest<Unit>
    {
        public CreateTuyenSinhPhuongThucXetTuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới phương thức xét tuyển
        /// </summary>
        /// <param name="model">Thông tin phương thức xét tuyển cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateTuyenSinhPhuongThucXetTuyenCommand(CreateTuyenSinhPhuongThucXetTuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateTuyenSinhPhuongThucXetTuyenCommand, Unit>
        {
            private readonly LeaderDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(LeaderDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateTuyenSinhPhuongThucXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"{systemLog.TraceId} - Create {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateTuyenSinhPhuongThucXetTuyenModel, TuyenSinhPhuongThucXetTuyen>(model);

                var checkCode = await _dataContext.TuyenSinhPhuongThucXetTuyens.AnyAsync(x => x.MaPhuongThucXetTuyen == entity.MaPhuongThucXetTuyen);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["TuyenSinhPhuongThucXetTuyen.MaPhuongThucXetTuyen.Existed", entity.MaPhuongThucXetTuyen]}");
                }

                await _dataContext.TuyenSinhPhuongThucXetTuyens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"{systemLog.TraceId} - Create {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phương thức xét tuyển: {entity.MaPhuongThucXetTuyen}",
                    ObjectCode = TuyenSinhPhuongThucXetTuyenConstant.CachePrefix,
                    ObjectId = entity.IdPhuongThucXetTuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }

    public class UpdateTuyenSinhPhuongThucXetTuyenCommand : IRequest<Unit>
    {
        public UpdateTuyenSinhPhuongThucXetTuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật phương thức xét tuyển
        /// </summary>
        /// <param name="model">Thông tin phương thức xét tuyển cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateTuyenSinhPhuongThucXetTuyenCommand(UpdateTuyenSinhPhuongThucXetTuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateTuyenSinhPhuongThucXetTuyenCommand, Unit>
        {
            private readonly LeaderDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(LeaderDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateTuyenSinhPhuongThucXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"{systemLog.TraceId} - Update {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.TuyenSinhPhuongThucXetTuyens.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"{systemLog.TraceId} - Before Update {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TuyenSinhPhuongThucXetTuyens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"{systemLog.TraceId} - After Update {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey(entity.IdPhuongThucXetTuyen.ToString()));
                _cacheService.Remove(TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey());

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập phương thức xét tuyển: {entity.MaPhuongThucXetTuyen}",
                    ObjectCode = TuyenSinhPhuongThucXetTuyenConstant.CachePrefix,
                    ObjectId = entity.IdPhuongThucXetTuyen.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteTuyenSinhPhuongThucXetTuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa phương thức xét tuyển 
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteTuyenSinhPhuongThucXetTuyenCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteTuyenSinhPhuongThucXetTuyenCommand, Unit>
        {
            private readonly LeaderDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(LeaderDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteTuyenSinhPhuongThucXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"{systemLog.TraceId} - Delete {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                // Check dữ liệu đã dùng

                var entity = await _dataContext.TuyenSinhPhuongThucXetTuyens.FindAsync(id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.TuyenSinhPhuongThucXetTuyens.Remove(entity);
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phương thức xét tuyển: {entity.MaPhuongThucXetTuyen}",
                    ObjectCode = TuyenSinhPhuongThucXetTuyenConstant.CachePrefix,
                    ObjectId = entity.IdPhuongThucXetTuyen.ToString()
                });

                Log.Information($"{systemLog.TraceId} - Delete {TuyenSinhPhuongThucXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                //Xóa cache
                _cacheService.Remove(TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey(entity.IdPhuongThucXetTuyen.ToString()));
                await _dataContext.SaveChangesAsync();

                _cacheService.Remove(TuyenSinhPhuongThucXetTuyenConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
