﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;
using Core.Shared;

namespace Core.Data
{
    [Table("log_action")]
    public class SystemLogEntity
    {
        [Key]
        [Column("id", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("correlation_id")]
        public string CorrelationId { get; set; }

        [Column("trace_id")]
        public string TraceId { get; set; }

        [Column("device_id")]
        public string DeviceId { get; set; }

        [Column("user_id")]
        public string UserId { get; set; }

        [Column("user_name")]
        public string UserName { get; set; }

        [Column("action_code")]
        public string ActionCode { get; set; }

        [Column("action_name")]
        public string ActionName { get; set; }

        [Column("client_ip")]
        public string ClientIP { get; set; }

        [Column("created_date")]
        public DateTime CreatedDate { get; set; }

        [Column("object_code")]
        public string ObjectCode { get; set; }

        [Column("object_id")]
        public string ObjectId { get; set; }

        [Column("request_method")]
        public string RequestMethod { get; set; }

        [Column("request_path")]
        public string RequestPath { get; set; }

        [Column("user_agent")]
        public string UserAgent { get; set; }

        [Column("os")]
        public string Os { get; set; }

        [Column("browser")]
        public string Browser { get; set; }

        [Column("client_info")]
        public string ClientInfo { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("time_execution")]
        public long TimeExecution { get; set; }

        [NotMapped]
        public Location Location { get; set; }

        [Column("location_json")]
        public string LocationJson
        {
            get
            {
                return Location == null ? null : JsonSerializer.Serialize(Location);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    Location = null;
                else
                    Location = JsonSerializer.Deserialize<Location>(value);
            }
        }

        [Column("meta_data_json")]
        public string MetaDataJson { get; set; }
    }
}
