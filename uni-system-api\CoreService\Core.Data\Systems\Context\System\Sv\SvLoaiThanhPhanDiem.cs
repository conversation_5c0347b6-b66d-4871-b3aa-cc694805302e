﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svThanhPhanMon")]
    public class SvThanhPhanMon
    {
        
        public SvThanhPhanMon()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_thanh_phan")]
        public int IdThanhPhan { get; set; }

        [Column("STT")]
        public int Stt { get; set; }

        [Column("Ky_hieu"), MaxLength(20)]
        public string KyHieu { get; set; }

        [Column("Ten_thanh_phan"), MaxLength(50)]
        public string TenThanhPhan { get; set; }

        [Column("Ty_le")]
        public int TyLe { get; set; }

        [Column("Chon_mac_dinh")]
        public int? ChonMacDinh { get; set; }

        [Column("Chuyen_can")]
        public int? ChuyenCan { get; set; }

        [Column("Nhom_thanh_phan")]
        public int? NhomThanhPhan { get; set; }

        [Column("Ty_le_nhom")]
        public int? TyLeNhom { get; set; }

        [Column("Ky_hieu_nhom"), MaxLength(10)]
        public string KyHieuNhom { get; set; }

        [Column("Thuc_hanh")]
        public bool ThucHanh { get; set; }


    }
}
