﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Core.Data
{
    [Table("svNhomDoiTuong")]
    public class SvNhomDoiTuong
    {

        public SvNhomDoiTuong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nhom_doi_tuong")]
        public int IdNhomDoiTuong { get; set; }

        [Column("Ma_nhom"), MaxLength(5)]
        public string MaNhom { get; set; }

        [Column("Ten_nhom"), MaxLength(100)]
        public string TenNhom { get; set; }

        [<PERSON>umn("Ten_nhom_en"), MaxLength(50)]
        public string Ten<PERSON>homEn { get; set; }


    }
}
