﻿using Core.Shared;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace Core.Business
{
    public class CallStoreHelper : ICallStoreHelper
    {
        private readonly IConfiguration _config;

        public CallStoreHelper(IConfiguration config)
        {
            _config = config;
        }

        private string DatabaseConnectionString()
        {
            return String.Format(_config["Database:System:ConnectionString:MSSQLDatabase"],
                AesEncryption.Decrypt(_config, _config["Database:UserId"]),
                AesEncryption.Decrypt(_config, _config["Database:Password"]));
        }

        public DataTable CallStoreWithStartAndEndDateAsync(string storeName, DateTime startDate, DateTime endDate)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {
                cmd = new SqlCommand(storeName, connection);
                cmd.Parameters.Add(new SqlParameter("@StartDate", startDate.ToString("yyyy-MM-dd")));
                cmd.Parameters.Add(new SqlParameter("@EndDate", endDate.ToString("yyyy-MM-dd")));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreLoginAsync(string userName, string password)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {
                cmd = new SqlCommand("sp_htUsers_Login_Canbo", connection);
                cmd.Parameters.Add(new SqlParameter("@UserName", userName));
                cmd.Parameters.Add(new SqlParameter("@PassWord", password));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }


        public int MonHocInsert(string kyHieu, string tenMon, string tenTiengAnh, int idBm, int idHe, int idNhomHp)
        {
            SqlCommand cmd = new SqlCommand();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {
                cmd = new SqlCommand("sp_svMonHoc_Insert", connection);
                cmd.Parameters.Add(new SqlParameter("@Ky_hieu", kyHieu));
                cmd.Parameters.Add(new SqlParameter("@Ten_mon", tenMon));
                cmd.Parameters.Add(new SqlParameter("@Ten_tieng_anh", tenTiengAnh));
                cmd.Parameters.Add(new SqlParameter("@ID_bm", idBm));
                cmd.Parameters.Add(new SqlParameter("@ID_he", idHe));
                cmd.Parameters.Add(new SqlParameter("@ID_nhom_hp", idNhomHp));
                cmd.CommandType = CommandType.StoredProcedure;

                connection.Open();
                object result = cmd.ExecuteScalar();

                // Convert the result to integer (if null, return 0)
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                cmd.Dispose();
                connection.Close();
            }
        }

        #region 

        public DataTable CallStoreDanhSachBoMonAsync(int IdMon, int IdCb)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("sp_tkbBoMon_Load_List_Theo_Bo_mon_Giao_vien", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_Mon_hoc", IdMon));
                cmd.Parameters.Add(new SqlParameter("@ID_Giao_vien", IdCb));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }
        #endregion


        #region svLop - Lớp học
        public DataTable CallStoresvLopGetByUserName(string userName)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {
                cmd = new SqlCommand("sp_svLop_Load_List_User", connection);
                cmd.Parameters.Add(new SqlParameter("@UserName", userName));    
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }
        #endregion


        public DataTable CallStoreDanhSachGiangVienTheoBoMonAsync(int IdBm)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("sp_tkbBoMonGiangVien_Load_GiangVien", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_bm", IdBm));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreDanhSachGiangVienChuaGanBoMonAsync(int IdBm)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("sp_tkbBoMonGiangVien_Load_ChuaGanBoMon", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_bm", IdBm));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreDanhSachMonHocTheoBoMonAsync(int IdBm)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("sp_svMonHoc_Load_BoMon_List", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_bm", IdBm));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreCreateGiangVienTheoBoMonAsync(int IdBm, int IdCb)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("[sp_tkbBoMonGiangVien_Insert]", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_bm", IdBm));
                cmd.Parameters.Add(new SqlParameter("@ID_cb", IdCb));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreChuongTrinhDaoTaoChiTietLoadByIdDaoTaoAsync(int IdDaoTao)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("sp_svChuongTrinhDaoTaoChiTiet_Load_List", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_dt", IdDaoTao));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        } 
        public DataTable CallStoreDeleteGiangVienTheoBoMonAsync(int IdBm, int IdCb)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("[sp_tkbBoMonGiangVien_Delete]", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_bm", IdBm));
                cmd.Parameters.Add(new SqlParameter("@ID_cb", IdCb));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }

        public DataTable CallStoreCheckDiemCtdtAsync(int idDt, int idMon)

        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {

                cmd = new SqlCommand("[sp_svChuongTrinhDaoTao_CheckExist_Diem]", connection);
                cmd.Parameters.Add(new SqlParameter("@ID_dt", idDt));
                cmd.Parameters.Add(new SqlParameter("@ID_mon", idMon));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error("CallStore Error: {Error}", ex.ToString());
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }
    }
}