﻿using MediatR;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using Serilog;
using StackExchange.Redis;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Data.SqlClient;
using RabbitMQ.Client;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using Core.Shared;
using System.Configuration;

namespace Core.Business.Service.CheckConnectionService
{
    public class ConnectionCheckQuery : IRequest<ConnectionCheckModel>
    {
        public class Handler : IRequestHandler<ConnectionCheckQuery, ConnectionCheckModel>
        {
            private readonly IConfiguration _config;
            private readonly ILogger<ConnectionCheckQuery> _logger;

            public Handler(IConfiguration config, ILogger<ConnectionCheckQuery> logger)
            {
                _config = config;
                _logger = logger;
            }

            public async Task<ConnectionCheckModel> Handle(ConnectionCheckQuery request, CancellationToken cancellationToken)
            {
                ConnectionCheckModel rs = new ConnectionCheckModel();

                Log.Information($"Test Connection");

                #region Swagger
                rs.EnableSwagger = _config["AppSettings:EnableSwagger"] == "true";
                #endregion

                #region Vault
                rs.EnabledVault = _config["Vault:Enabled"] == "true";
                #endregion

                #region Redis
                rs.Redis = false;
                if (!string.IsNullOrEmpty(_config["redis:configuration"]))
                {
                    try
                    {
                        // Tạo kết nối đến Redis
                        var redis = ConnectionMultiplexer.Connect(_config["redis:configuration"]);
                        var db = redis.GetDatabase();

                        // Kiểm tra kết nối bằng cách thêm và lấy một giá trị
                        string key = "test_key";
                        string value = "Hello, Redis!";

                        // Thêm giá trị vào Redis
                        db.StringSet(key, value);

                        // Lấy giá trị từ Redis
                        string retrievedValue = db.StringGet(key);

                        Console.WriteLine($"Retrieved value from Redis: {retrievedValue}");
                        rs.Redis = true;
                        // Đóng kết nối
                        redis.Close();
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Redis - {ex.Message} - {ex.ToString()}");
                    }
                }
                #endregion

                #region RabbitMQ
                rs.RabbitMQ = false;

                if (!string.IsNullOrEmpty(_config["RabbitMQ:Uri"]))
                {
                    var factory = new ConnectionFactory
                    {
                        Uri = new Uri(_config["RabbitMQ:Uri"])
                    };

                    try
                    {
                        using (var connection = factory.CreateConnection())
                        {
                            // Kiểm tra kết nối bằng cách tạo một channel
                            using (var channel = connection.CreateModel())
                            {
                                Console.WriteLine("Channel đã được tạo thành công.");
                                rs.RabbitMQ = true;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"RabbitMQ - {ex.Message} - {ex.ToString()}");
                    }
                }
                #endregion

                #region SQLServer
                rs.SQLServer = false;
                string sqlConnectionString = String.Format(_config["Database:System:ConnectionString:MSSQLDatabase"],
                            AesEncryption.Decrypt(_config, _config["Database:UserId"]),
                            AesEncryption.Decrypt(_config, _config["Database:Password"]));

                if (!string.IsNullOrEmpty(sqlConnectionString))
                {
                    try
                    {
                        using (var connection = new SqlConnection(sqlConnectionString))
                        {
                            await connection.OpenAsync(); // Mở kết nối

                            Console.WriteLine("Kết nối thành công đến SQL Server.");

                            // Thực hiện truy vấn đơn giản (tùy chọn)
                            using (var command = new SqlCommand("SELECT @@VERSION;", connection))
                            {
                                var version = await command.ExecuteScalarAsync();
                                Console.WriteLine($"Phiên bản SQL Server: {version}");
                                rs.SQLServer = true;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"SQLServer - {ex.Message} - {ex.ToString()}");
                    }
                }
                #endregion

                #region MongoDB
                rs.MongoDB = false;
                if (!string.IsNullOrEmpty(_config["MongoDBDatabaseSettings:ConnectionString"]))
                {
                    try
                    {
                        // Tạo client MongoDB
                        var client = new MongoClient(_config["MongoDBDatabaseSettings:ConnectionString"]);

                        // Kiểm tra kết nối bằng cách lấy danh sách cơ sở dữ liệu
                        var databases = await client.ListDatabaseNamesAsync();
                        rs.MongoDB = true;
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"MongoDB - {ex.Message} - {ex.ToString()}");
                    }
                }
                #endregion

                #region SSO
                rs.IdentityServer = false;
                try
                {
                    var identityServerEnable = _config["Authentication:IdentityServer:Enable"];
                    var identityServerUrl = _config["Authentication:IdentityServer:Uri"];

                    if (identityServerEnable == "true" && !string.IsNullOrEmpty(identityServerUrl))
                    {
                        using (var httpClient = new HttpClient())
                        {
                            using var cts = new CancellationTokenSource();
                            cts.CancelAfter(TimeSpan.FromSeconds(120));

                            // Gửi yêu cầu GET đến URL
                            var response = await httpClient.GetAsync(identityServerUrl, cts.Token);

                            // Kiểm tra trạng thái phản hồi
                            if (response.IsSuccessStatusCode)
                            {
                                rs.IdentityServer = true;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"IdentityServer - {ex.Message} - {ex.ToString()}");
                }

                #endregion

                return rs;
            }
        }
    }

}
