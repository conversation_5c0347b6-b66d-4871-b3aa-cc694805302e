﻿using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/tinh")]
    [ApiExplorerSettings(GroupName = "24. Tỉnh")]
    [Authorize]
    public class TinhController : ApiControllerBase
    {
        public TinhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách tỉnh cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TinhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTinhQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách tỉnh có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TinhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TinhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTinhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết tỉnh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TinhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] string id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTinhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới tỉnh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TINH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TINH_CREATE;


                return await _mediator.Send(new CreateTinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel tỉnh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyTinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TINH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TINH_CREATE_MANY;


                return await _mediator.Send(new CreateManyTinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa tỉnh
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_EDIT))]
        public async Task<IActionResult> Update(string id, [FromBody] UpdateTinhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TINH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TINH_UPDATE;
                return await _mediator.Send(new UpdateTinhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa tỉnh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TINH_DELETE))]
        public async Task<IActionResult> Delete(string id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TINH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TINH_DELETE;

                return await _mediator.Send(new DeleteTinhCommand(id, u.SystemLog));
            });
        }

    }
}
