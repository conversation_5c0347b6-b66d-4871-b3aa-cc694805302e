﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svTinh")]
    public class SvTinh
    {

        public SvTinh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tinh"), MaxLength(5)]
        public string IdTinh { get; set; }

        [Column("Ten_tinh"), MaxLength(50)]
        public string TenTinh { get; set; }

        [Column("Ten_tinh_en"), MaxLength(50)]
        public string TenTinhEn { get; set; }


    }
}
