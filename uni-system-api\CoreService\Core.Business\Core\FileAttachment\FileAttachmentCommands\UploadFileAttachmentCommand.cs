﻿using Core.Shared;
using Core.Shared.ContextAccessor;
using Core.Shared.Enums;
using MediatR;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UploadFileAttachmentCommand : IRequest<MinIOFileUploadResult>
    {
        public IFormFile File { get; set; }
        public string BucketName { get; set; }
        public short Source { get; set; } = 0;

        public UploadFileAttachmentCommand(IFormFile file, string bucketName, short source)
        {
            File = file;
            BucketName = bucketName;
            Source = source;
        }

        public class Handler : IRequestHandler<UploadFileAttachmentCommand, MinIOFileUploadResult>
        {
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;
            private readonly IWebHostEnvironment _webHostEnvironment;
            private readonly IContextAccessor _contextAccessor;

            public Handler(
                IStringLocalizer<Resources> localizer, IMediator mediator,
                IConfiguration config, IWebHostEnvironment webHostEnvironment, Func<IContextAccessor> contextAccessorFactory
                )
            {
                _localizer = localizer;
                _mediator = mediator;
                _config = config;
                _webHostEnvironment = webHostEnvironment;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<MinIOFileUploadResult> Handle(UploadFileAttachmentCommand request, CancellationToken cancellationToken)
            {
                var file = request.File;
                var bucketName = request.BucketName;
                var source = request.Source;

                if (file == null)
                {
                    throw new ArgumentException("File is null");
                }
                Log.Information($"Create {FileAttachmentConstant.CachePrefix}: {file.FileName}");

                UploadConfig uploadConfig = _config.GetSection("AppSettings:UploadConfig").Get<UploadConfig>();

                if (uploadConfig == null)
                    uploadConfig = new UploadConfig();

                // file max 10Mb
                if (file.Length > uploadConfig.MaxFileSize * 1024 * 1024)
                {
                    throw new ArgumentException("File size exceeds the maximum limit of 10MB.");
                }

                // Lấy định dạng file
                var fileExtension = Path.GetExtension(file.FileName).ToLower();

                // Kiểm tra định dạng file
                var allowedExtensions = uploadConfig.AllowedExtensions;
                if (!allowedExtensions.Contains(fileExtension))
                {
                    throw new ArgumentException("Invalid file format. Supported formats: jpg, jpeg, png, gif, pdf, doc, docx, xls, xlsx, ppt, pptx.");
                }

                if (_config["minio:enabled"] == "true" || source == SourceFileEnums.MinIO.GetHashCode())
                {
                    MinIOService minIOService = new MinIOService(_config);

                    var filePath = Guid.NewGuid() + fileExtension;

                    var minIOFileUpload = await minIOService.UploadObjectAsync(request.BucketName, file.FileName, file.OpenReadStream());

                    var fileDinhKem = await _mediator.Send(new CreateFileAttachmentCommand(new FileAttachmentModel
                    {
                        BucketName = minIOFileUpload.BucketName,
                        ObjectName = minIOFileUpload.ObjectName,
                        FileName = minIOFileUpload.FileName,
                        CreateUserName = _contextAccessor.UserName,
                        Source = SourceFileEnums.MinIO.GetHashCode(),
                        Length = file.Length
                    }, _contextAccessor.SystemLog));

                    return new MinIOFileUploadResult
                    {
                        Id = fileDinhKem.Id,
                        FileName = file.FileName,
                        FilePath = minIOFileUpload.FileName
                    };
                }
                else
                {
                    var filePath = await UploadFiles(file, uploadConfig);

                    var fileDinhKem = await _mediator.Send(new CreateFileAttachmentCommand(new FileAttachmentModel
                    {
                        CreateUserName = _contextAccessor.UserName,
                        FileName = file.FileName,
                        FilePath = filePath,
                        Source = SourceFileEnums.Server.GetHashCode(),
                        Length = file.Length
                    }, _contextAccessor.SystemLog));

                    return new MinIOFileUploadResult
                    {
                        Id = fileDinhKem.Id,
                        FileName = file.FileName,
                        FilePath = filePath
                    };
                }
            }

            private async Task<string> UploadFiles(IFormFile file, UploadConfig uploadConfig)
            {
                try
                {
                    var currentDate = DateTime.Now;
                    var uploadFolder = uploadConfig.FolderUpload;
                    var filePath = $"{uploadFolder}/{currentDate.Year}/{currentDate.Month}/{currentDate.Day}/";
                    var uploadPath = Path.Combine(_webHostEnvironment.WebRootPath, filePath);

                    if (!Directory.Exists(uploadPath))
                    {
                        Directory.CreateDirectory(uploadPath);
                    }

                    if (file.Length > 0)
                    {
                        var fileExtension = Path.GetExtension(file.FileName).ToLower();

                        // Generate new file name
                        var fileSubfix = $"{currentDate:yyMMddHHmmss}" + new Random(currentDate.Millisecond).Next(10, 99);
                        var fileName = Path.GetFileNameWithoutExtension(file.FileName) + "_" + fileSubfix +
                                          fileExtension;

                        var fullFilePath = Path.Combine(uploadPath, fileName);

                        using (var stream = new FileStream(fullFilePath, FileMode.Create))
                        {
                            await file.CopyToAsync(stream);
                        }

                        return filePath + fileName;
                    }

                    throw new ArgumentException(_localizer["data.not-found"]);
                }
                catch (Exception ex)
                {
                    throw new ArgumentException(ex.Message);
                }
            }
        }
    }
}
