﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svChuyenNganh")]
    public class SvChuyenNganh
    {
        public SvChuyenNganh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [Column("Ma_chuyen_nganh"), MaxLength(20)]
        public string MaChuyenNganh { get; set; }

        [Column("chuyen_nganh"), MaxLength(200)]
        public string ChuyenNganh { get; set; }

        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("chuyen_nganh_en"), <PERSON><PERSON><PERSON><PERSON>(200)]
        public string ChuyenNganhEn { get; set; }
    }
}
