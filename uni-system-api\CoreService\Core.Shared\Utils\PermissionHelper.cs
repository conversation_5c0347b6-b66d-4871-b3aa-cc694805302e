﻿using System.Linq;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Core.Shared
{
    public static class PermissionHelper
    {
        public static bool HasPermission(HttpContext httpContext, string claimType, string permissionToCheck)
        {
            var user = httpContext.User;
            if (user == null || !user.Identity.IsAuthenticated)
                return false;

            var claimValue = user.Claims.FirstOrDefault(c => c.Type == claimType)?.Value;

            if (!string.IsNullOrEmpty(claimValue))
            {
                var permissions = claimValue.Split(',');
                return permissions.Contains(permissionToCheck);
            }

            return false;
        }
    }
}
