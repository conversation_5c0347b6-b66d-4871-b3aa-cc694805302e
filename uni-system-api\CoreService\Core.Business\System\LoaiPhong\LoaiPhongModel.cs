﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiPhongSelectItemModel
    {
        public int IdLoaiPhong { get; set; }
        public string Ma<PERSON>oaiPhong { get; set; }
        public string TenLoaiPhong { get; set; }
    }

    public class LoaiPhongBaseModel
    {
        public int IdLoaiPhong { get; set; }
        public string Ma<PERSON>oaiPhong { get; set; }
        public string TenLoaiPhong { get; set; }
        public bool ThucHanh { get; set; }
    }


    public class LoaiPhongModel : LoaiPhongBaseModel
    {

    }

    public class LoaiPhongFilterModel : BaseQueryFilterModel
    {
        public LoaiPhongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdLoaiPhong";
        }
    }

    public class CreateLoaiPhongModel
    {
        [Required(ErrorMessage = "LoaiPhong.IdLoaiPhong.NotRequire")]
        public int IdLoaiPhong { get; set; }

        [MaxLength(5, ErrorMessage = "LoaiPhong.MaLoaiPhong.MaxLength(5)")]
        [Required(ErrorMessage = "LoaiPhong.MaLoaiPhong.NotRequire")]
        public string MaLoaiPhong { get; set; }

        [MaxLength(50, ErrorMessage = "LoaiPhong.LoaiPhong.MaxLength(50)")]
        [Required(ErrorMessage = "LoaiPhong.LoaiPhong.NotRequire")]
        public string TenLoaiPhong { get; set; }

        public bool ThucHanh { get; set; }

    }

    public class CreateManyLoaiPhongModel
    {
        public List<CreateLoaiPhongModel> listLoaiPhongModels { get; set; }
    }

    public class UpdateLoaiPhongModel : CreateLoaiPhongModel
    {
        public void UpdateEntity(TkbLoaiPhong input)
        {
            input.IdLoaiPhong = IdLoaiPhong;
            input.MaLoaiPhong = MaLoaiPhong;
            input.TenLoaiPhong = TenLoaiPhong;
            input.ThucHanh = ThucHanh;

        }
    }
}
