﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepLoaiRenLuyenSelectItemModel
    {

        public int IdXepLoai { get; set; }

        public string XepLoai { get; set; }
    }

    public class XepLoaiRenLuyenBaseModel
    {
        public int IdXepLoai { get; set; }

        public string XepLoai { get; set; }

        public string XepLoaiEn { get; set; }

        public int TuDiem { get; set; }

        public int DenDiem { get; set; }

        public float HeSo { get; set; }
    }


    public class XepLoaiRenLuyenModel : XepLoaiRenLuyenBaseModel
    {

    }

    public class XepLoaiRenLuyenFilterModel : BaseQueryFilterModel
    {
        public XepLoaiRenLuyenFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoai";
        }
    }

    public class CreateXepLoaiRenLuyenModel
    {

        [Required(ErrorMessage = "XepLoaiRenLuyen.IdXepLoai.NotRequire")]
        public int IdXepLoai { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiRenLuyen.XepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiRenLuyen.XepLoai.NotRequire")]
        public string XepLoai { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiRenLuyen.XepLoaiEn.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiRenLuyen.XepLoaiEn.NotRequire")]
        public string XepLoaiEn { get; set; }

        [Required(ErrorMessage = "XepLoaiRenLuyen.TuDiem.NotRequire")]
        public int TuDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiRenLuyen.DenDiem.NotRequire")]
        public int DenDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiRenLuyen.HeSo.NotRequire")]
        public float HeSo { get; set; }

    }

    public class CreateManyXepLoaiRenLuyenModel
    {
        public List<CreateXepLoaiRenLuyenModel> listXepLoaiRenLuyenModels { get; set; }
    }

    public class UpdateXepLoaiRenLuyenModel : CreateXepLoaiRenLuyenModel
    {
        public void UpdateEntity(SvXepLoaiRenLuyen input)
        {
            input.IdXepLoai = IdXepLoai;
            input.XepLoai = XepLoai;
            input.XepLoaiEn = XepLoaiEn;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.HeSo = HeSo;

        }
    }
}
