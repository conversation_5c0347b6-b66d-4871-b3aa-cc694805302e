﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svChuongTrinhDaoTaoRangBuoc")]
    public class SvChuongTrinhDaoTaoRangBuoc
    {

        public SvChuongTrinhDaoTaoRangBuoc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_rb")]
        public int IdRb { get; set; }

        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("ID_mon_rb")]
        public int IdMonRb { get; set; }

        [Column("Loai_rang_buoc")]
        public int LoaiRangBuoc { get; set; }


    }
}
