using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateThamSoHeThongCommand : IRequest<Unit>
    {
        public UpdateThamSoHeThongModel Model { get; set; }

        /// <summary>
        /// Cập nhật tham số hệ thống
        /// </summary>
        /// <param name="model">Thông tin tham số hệ thống cần cập nhật</param>
        public UpdateThamSoHeThongCommand(UpdateThamSoHeThongModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateThamSoHeThongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateThamSoHeThongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {ThamSoHeThongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.HtThamSoHeThongs.FirstOrDefaultAsync(x => x.IdThamSo == model.IdThamSo && x.IdPh == model.IdPh);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["tham-so-he-thong.not-found"]}");
                }

                model.UpdateEntity(entity);
                entity.UserName = _contextAccessor.UserName;

                _dataContext.HtThamSoHeThongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Update {ThamSoHeThongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tham số hệ thống mã: {entity.IdThamSo}",
                    ObjectCode = ThamSoHeThongConstant.CachePrefix,
                    ObjectId = entity.IdThamSo
                });

                //Xóa cache
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey());
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey(model.IdThamSo));

                return Unit.Value;
            }
        }
    }
}
