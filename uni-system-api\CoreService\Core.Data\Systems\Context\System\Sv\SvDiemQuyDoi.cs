﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDiemQuyDoi")]
    public class SvDiemQuyDoi
    {

        public SvDiemQuyDoi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai")]
        public int IdXepLoai { get; set; }

        [Column("Tu_hoc_ky")]
        public int TuHocKy { get; set; }

        [Column("Tu_nam_hoc"), MaxLength(10)]
        public string TuNamHoc { get; set; }

        [Column("Den_hoc_ky")]
        public int DenHocKy { get; set; }

        [Column("Den_nam_hoc"), MaxLength(10)]
        public string DenNamHoc { get; set; }

        [Column("Xep_loai"), <PERSON><PERSON><PERSON><PERSON>(50)]
        public string XepLoai { get; set; }

        [Column("Diem_chu"), MaxLength(2)]
        public string Diem<PERSON>hu { get; set; }

        [Column("Diem_so")]
        public float DiemSo { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Tich_luy")]
        public bool TichLuy { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

    }
}
