﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ChuongTrinhDaoTaoKienThucSelectItemModel
    {
        public int IdKienThuc { get; set; }
        public string TenKienThuc { get; set; }
    }

    public class ChuongTrinhDaoTaoKienThucBaseModel
    {
        public int IdKienThuc { get; set; }
        public bool MonChuyenNganh { get; set; }
        public string TenKienThuc { get; set; }
        public string TenKienThucEn { get; set; }
    }


    public class ChuongTrinhDaoTaoKienThucModel : ChuongTrinhDaoTaoKienThucBaseModel
    {

    }

    public class ChuongTrinhDaoTaoKienThucFilterModel : BaseQueryFilterModel
    {
        public ChuongTrinhDaoTaoKienThucFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdKienThuc";
        }
    }

    public class CreateChuongTrinhDaoTaoKienThucModel
    {
        [Required(ErrorMessage = "ChuongTrinhDaoTaoKienThuc.IdKienThuc.NotRequire")]
        public int IdKienThuc { get; set; }

        [Required(ErrorMessage = "ChuongTrinhDaoTaoKienThuc.MonChuyenNganh.NotRequire")]
        public bool MonChuyenNganh { get; set; }

        [MaxLength(100, ErrorMessage = "ChuongTrinhDaoTaoKienThuc.TenKienThuc.MaxLength(100)")]
        [Required(ErrorMessage = "ChuongTrinhDaoTaoKienThuc.TenKienThuc.NotRequire")]
        public string TenKienThuc { get; set; }

        [MaxLength(200, ErrorMessage = "ChuongTrinhDaoTaoKienThuc.TenKienThucEn.MaxLength(200)")]
        public string TenKienThucEn { get; set; }

    }

    public class CreateManyChuongTrinhDaoTaoKienThucModel
    {
        public List<CreateChuongTrinhDaoTaoKienThucModel> listChuongTrinhDaoTaoKienThucModels { get; set; }
    }

    public class UpdateChuongTrinhDaoTaoKienThucModel : CreateChuongTrinhDaoTaoKienThucModel
    {
        public void UpdateEntity(SvChuongTrinhDaoTaoKienThuc input)
        {
            input.IdKienThuc = IdKienThuc;
            input.MonChuyenNganh = MonChuyenNganh;
            input.TenKienThuc = TenKienThuc;
            input.TenKienThucEn = TenKienThucEn;

        }
    }
}
