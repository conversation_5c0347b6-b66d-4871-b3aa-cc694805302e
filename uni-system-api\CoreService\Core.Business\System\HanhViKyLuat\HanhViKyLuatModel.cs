﻿using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HanhViSelectItemModel
    {
        public int IdHanhVi { get; set; }
        public string MaHanhVi { get; set; }
        public string HanhVi { get; set; }
    }

    public class HanhViBaseModel
    {
        public int IdHanhVi { get; set; }
        public string MaHanhVi { get; set; }
        public string HanhVi { get; set; }

    }


    public class HanhViModel : HanhViBaseModel
    {

    }

    public class HanhViFilterModel : BaseQueryFilterModel
    {
        public HanhViFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHanhVi";
        }
    }

    public class CreateHanhViModel
    {
        [Required(ErrorMessage = "HanhVi.IdHanhVi.NotRequire")]
        public int IdHanhVi { get; set; }

        [MaxLength(5, ErrorMessage = "HanhVi.MaHanhVi.MaxLength(5)")]
        public string MaHanhVi { get; set; }

        [MaxLength(100, ErrorMessage = "HanhVi.HanhVi.MaxLength(100)")]
        [Required(ErrorMessage = "HanhVi.HanhVi.NotRequire")]
        public string HanhVi { get; set; }


    }

    public class CreateManyHanhViModel
    {
        public List<CreateHanhViModel> listHanhViModels { get; set; }
    }

    public class UpdateHanhViModel : CreateHanhViModel
    {
        public void UpdateEntity(SvHanhVi input)
        {
            input.IdHanhVi = IdHanhVi;
            input.MaHanhVi = MaHanhVi;
            input.HanhVi = HanhVi;

        }
    }
}
