﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svChuongTrinhDaoTaoNhomTuChon")]
    public class SvChuongTrinhDaoTaoNhomTuChon
    {

        public SvChuongTrinhDaoTaoNhomTuChon()
        {

        }

        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("Nhom_tu_chon")]
        public int NhomTuChon { get; set; }

        [Column("So_mon_tu_chon")]
        public int SoMonTuChon { get; set; }

        [Column("So_mon_dang_ky")]
        public int SoMonDangKy { get; set; }

    }
}
