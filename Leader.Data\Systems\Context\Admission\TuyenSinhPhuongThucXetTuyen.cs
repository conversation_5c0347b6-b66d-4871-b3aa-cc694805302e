﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Leader.Data
{
    [Table("svTuyenSinhPhuongThucXetTuyen")]
    public class TuyenSinhPhuongThucXetTuyen
    {
        public TuyenSinhPhuongThucXetTuyen()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phuong_thuc_xet_tuyen")]
        public int IdPhuongThucXetTuyen { get; set; }

        [Column("Ma_phuong_thuc_xet_tuyen"), MaxLength(50)]
        public string MaPhuongThucXetTuyen { get; set; }

        [Column("Ten_phuong_thuc_xet_tuyen"), MaxLength(500)]
        public string TenPhuongThucXetTuyen { get; set; }

        [<PERSON>umn("Ghi_chu"), MaxLength(500)]
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }
    }
}
